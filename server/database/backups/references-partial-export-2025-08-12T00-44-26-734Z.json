[{"id": 318, "name": "Disco Elysium", "primary_type": "video_game", "secondary_type": "C-RPG", "views_count": 0, "likes_count": 0, "created_at": "2025-08-11 14:57:44", "updated_at": "2025-08-11 15:56:29", "urls": []}, {"id": 317, "name": "<PERSON><PERSON><PERSON>, l'Univers dévoilé", "primary_type": "documentary", "secondary_type": "Sciences", "views_count": 0, "likes_count": 0, "created_at": "2025-08-11 14:52:13", "updated_at": "2025-08-11 15:56:58", "urls": []}, {"id": 316, "name": "The Old Guard", "primary_type": "film", "secondary_type": "Action, Sciences-Fiction", "views_count": 0, "likes_count": 0, "created_at": "2025-08-11 14:37:49", "updated_at": "2025-08-11 15:57:22", "urls": []}, {"id": 315, "name": "Final Space", "primary_type": "tv_series", "secondary_type": "Science-Fiction, Space", "views_count": 0, "likes_count": 0, "created_at": "2025-08-11 14:32:56", "updated_at": "2025-08-11 15:57:38", "urls": []}, {"id": 314, "name": "Les carnets secrets d'une fille de joie", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2025-08-11 14:09:42", "updated_at": "2025-08-11 14:09:42", "urls": []}, {"id": 313, "name": "<PERSON>", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2025-08-11 14:09:12", "updated_at": "2025-08-11 14:09:12", "urls": []}, {"id": 312, "name": "The Suicide of <PERSON>", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2025-08-11 14:07:26", "updated_at": "2025-08-11 14:07:26", "urls": []}, {"id": 311, "name": "Dr Who", "primary_type": "tv_series", "secondary_type": "Science-Fiction", "views_count": 0, "likes_count": 0, "created_at": "2025-08-11 14:06:08", "updated_at": "2025-08-11 15:57:52", "urls": []}, {"id": 310, "name": "<PERSON> and <PERSON>", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2025-08-11 14:05:36", "updated_at": "2025-08-11 14:05:36", "urls": []}, {"id": 309, "name": "Le podcast de l'entrepreneur", "primary_type": "podcast", "description": "Conseils, Astuces, News, Soutien pour les entrepreneurs. Les episodes sont en francais. Le Podcast de l'entrepreneur est une solution très productive qui vous fera gagner du temps dans votre journée chargée. En effet, lorsque l’on est dans les temps d’attente ou les temps morts (voiture, embouteillages, métro, train…) pour aller travail le podcast est la solution pour continuer d’apprendre, de se former, d’accéder à des informations.\n\nhttps://pca.st/5VM8", "views_count": 0, "likes_count": 0, "created_at": "2025-08-11 13:59:45", "updated_at": "2025-08-11 14:04:39", "urls": []}, {"id": 306, "name": "No et Moi", "primary_type": "other", "release_date": "2024-06-13", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 307, "name": "Procès VA-OM", "primary_type": "other", "release_date": "2024-06-13", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 308, "name": "Épigraphe du Révisor", "primary_type": "other", "release_date": "2023-09-05", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 285, "name": "Journal", "primary_type": "other", "release_date": "2024-06-13", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 286, "name": "It's a Wonderful Life", "primary_type": "other", "release_date": "2024-01-31", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 287, "name": "Fleming: The Man Who Would Be Bond", "primary_type": "other", "release_date": "2024-09-14", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 288, "name": "Radio France", "primary_type": "other", "release_date": "2024-08-09", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 289, "name": "Horses", "primary_type": "other", "description": "Horses is a YouTube channel about philosophy, art and reflections.", "release_date": "2024-02-21", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 290, "name": "Lettre à Laza", "primary_type": "other", "release_date": "2023-08-28", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 291, "name": "Le Grand Livre De L'humour Noir", "primary_type": "other", "release_date": "2024-04-21", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 292, "name": "Dans ces années-là (2008)", "primary_type": "other", "release_date": "2024-06-13", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 293, "name": "Meditations", "primary_type": "other", "release_date": "2024-02-20", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 294, "name": "<PERSON>", "primary_type": "other", "release_date": "2024-01-28", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 295, "name": "Secret Story", "primary_type": "other", "release_date": "2024-05-13", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 296, "name": "Alice aux pays des merveilles", "primary_type": "other", "release_date": "2023-08-28", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 297, "name": "<PERSON>", "primary_type": "other", "release_date": "2024-01-13", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 298, "name": "Le Figaro Littéraire", "primary_type": "other", "release_date": "1951-06-29", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 299, "name": "Les filles du feu", "primary_type": "other", "release_date": "2024-06-13", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 300, "name": "The Killer", "primary_type": "other", "release_date": "2023-12-12", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 301, "name": "Une vie", "primary_type": "other", "release_date": "2024-06-13", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 302, "name": "L'Avant-Scène n°303/304", "primary_type": "other", "release_date": "1983-09-04", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 303, "name": "<PERSON>", "primary_type": "other", "release_date": "2024-04-27", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 304, "name": "Les Disparus De Bas-Vourlans", "primary_type": "other", "release_date": "2024-06-20", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 305, "name": "Mille et une pensées (2005)", "primary_type": "other", "release_date": "2024-06-13", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 282, "name": "Spider-<PERSON>: Into the Spider-verse", "primary_type": "other", "release_date": "2023-11-16", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:17", "updated_at": "2025-08-08 13:02:17", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 283, "name": "Leave The World Behind", "primary_type": "other", "release_date": "2023-12-11", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:17", "updated_at": "2025-08-08 13:02:17", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 284, "name": "<PERSON><PERSON><PERSON>", "primary_type": "other", "release_date": "2024-04-23", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:17", "updated_at": "2025-08-08 13:02:17", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 260, "name": "The screenwriter looks at the screenwriter", "primary_type": "other", "release_date": "1972-07-30", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 261, "name": "Notes pour trop tard", "primary_type": "other", "release_date": "2016-12-31", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 262, "name": "The craft of the screenwriter", "primary_type": "other", "release_date": "1981-07-25", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 263, "name": "Premier placet du Tartuffe", "primary_type": "other", "release_date": "1664-07-26", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 264, "name": "Detroit: Become Human", "primary_type": "other", "release_date": "2023-03-18", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 265, "name": "Les sciences face aux créationnismes", "primary_type": "other", "release_date": "2023-07-29", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 266, "name": "Re:zero", "primary_type": "other", "release_date": "2021-06-13", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 267, "name": "Apollo 13", "primary_type": "other", "release_date": "2024-06-07", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 268, "name": "Fireship", "primary_type": "other", "release_date": "2023-05-20", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 269, "name": "Peur de l'échec", "primary_type": "other", "release_date": "2008-12-31", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 270, "name": "Les Cahiers de la Bande Dessinée n°22", "primary_type": "other", "release_date": "1973-07-30", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 271, "name": "<PERSON><PERSON><PERSON>", "primary_type": "other", "release_date": "2010-12-31", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 272, "name": "À l'heure où je me couche", "primary_type": "other", "release_date": "2021-10-24", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}]