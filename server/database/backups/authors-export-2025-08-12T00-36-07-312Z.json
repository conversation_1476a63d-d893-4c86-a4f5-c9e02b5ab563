[{"id": 672, "name": "Barman", "is_fictional": true, "job": "<PERSON>man (Disco Elysium)", "description": "The barman in Disco Elysium game.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:57:44", "updated_at": "2025-08-11 15:01:49", "socials": []}, {"id": 671, "name": "<PERSON>", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:56:50", "updated_at": "2025-08-11 14:56:50", "socials": []}, {"id": 670, "name": "<PERSON>", "is_fictional": false, "job": "Physicist", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:52:13", "updated_at": "2025-08-11 15:53:52", "socials": []}, {"id": 669, "name": "Woman in the store", "is_fictional": true, "job": "<PERSON><PERSON> (The Old Guard)", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:41:16", "updated_at": "2025-08-11 14:44:45", "socials": []}, {"id": 668, "name": "<PERSON>", "is_fictional": true, "job": "The Old Guard", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:40:34", "updated_at": "2025-08-11 14:42:26", "socials": []}, {"id": 667, "name": "<PERSON>", "is_fictional": true, "job": "The Old Guard", "description": "Fictional character from The Old Guard film.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:37:49", "updated_at": "2025-08-11 14:40:09", "socials": []}, {"id": 666, "name": "<PERSON>", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:35:49", "updated_at": "2025-08-11 14:35:49", "socials": []}, {"id": 665, "name": "<PERSON><PERSON>", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:34:52", "updated_at": "2025-08-11 14:34:52", "socials": []}, {"id": 664, "name": "<PERSON> (Final Space)", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:32:56", "updated_at": "2025-08-11 14:32:56", "socials": []}, {"id": 663, "name": "<PERSON><PERSON>", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:09:12", "updated_at": "2025-08-11 14:09:12", "socials": []}, {"id": 662, "name": "<PERSON>", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:07:26", "updated_at": "2025-08-11 14:07:26", "socials": []}, {"id": 661, "name": "<PERSON>", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:06:40", "updated_at": "2025-08-11 14:06:40", "socials": []}, {"id": 660, "name": "The Doctor", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:06:08", "updated_at": "2025-08-11 14:06:08", "socials": []}, {"id": 659, "name": "<PERSON>", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:05:36", "updated_at": "2025-08-11 14:05:36", "socials": []}, {"id": 636, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-07-13", "death_date": "2024-07-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 637, "name": "Edison", "is_fictional": false, "birth_date": "2023-10-14", "death_date": "2023-10-14", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 638, "name": "Proverbe portugais", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 639, "name": "Miss Piano", "is_fictional": false, "birth_date": "2025-01-06", "death_date": "2025-01-06", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 640, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "1966-09-25", "death_date": "2023-10-03", "job": "German physics researcher", "description": "<PERSON><PERSON> is a German Dutch professor of radio astronomy and astroparticle physics at the Radboud University Nijmegen. He was a winner of the 2011 Spinoza Prize. His main field of study is black holes, and he is the originator of the concept of the 'black hole shadow'.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/thumb/a/a4/HeinoFalcke2011.jpg/897px-HeinoFalcke2011.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 641, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-27", "death_date": "2024-06-27", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 642, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-01-05", "death_date": "2024-01-05", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 643, "name": "Lucy?", "is_fictional": false, "birth_date": "2024-02-02", "death_date": "2024-02-02", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 644, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 645, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 646, "name": "Christian Saint-Étienne", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 647, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-09-26", "death_date": "2023-09-26", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 648, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-01-05", "death_date": "2024-01-05", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 649, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-04-21", "death_date": "2024-04-21", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 650, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 651, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-03-12", "death_date": "2024-03-12", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 652, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 653, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 654, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 655, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "job": "Politic", "description": "Old mayor of Montpellier, France.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 656, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 657, "name": "Malcom Forbes, PDG de la revue financière Forbes", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 658, "name": "<PERSON>, acteur éc<PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 559, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 560, "name": "Réformateur", "is_fictional": true, "birth_date": "2023-11-11", "death_date": "2023-11-11", "job": "Reformer in the Volt", "description": "A reformer in the Volt which warned <PERSON> and his colleagues about their actions. He's supposedly a person who works on rules concerning the Volt.\n\nHe's a fictional character in the book \"La Zone du Dehors\".", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 561, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 562, "name": "Narra<PERSON> (Fight Club)", "is_fictional": false, "birth_date": "2024-04-21", "death_date": "2024-04-21", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 563, "name": "House Owner", "is_fictional": false, "birth_date": "2023-12-11", "death_date": "2023-12-11", "job": "Market dealer", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 564, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-08-14", "death_date": "2024-08-14", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 565, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-01-29", "death_date": "2024-01-29", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 566, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 567, "name": "<PERSON>, cofonda<PERSON>ur de LinkedIn", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 568, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-09-14", "death_date": "2024-09-14", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 569, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-01-05", "death_date": "2024-01-05", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 570, "name": "Proverbe espagnol", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 571, "name": "Sherpa 2", "is_fictional": false, "birth_date": "2024-01-04", "death_date": "2024-01-04", "description": "A random character in the book «Kilomètre zéro».", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 572, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-02-21", "death_date": "2024-02-21", "job": "<PERSON>", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 573, "name": "<PERSON>, pdg du groupe américain titan international", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 574, "name": "<PERSON><PERSON><PERSON>, <PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 575, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 576, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 577, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 578, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-08-28", "death_date": "2023-08-28", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 579, "name": "Le narrateur dans le film Fight Club", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 580, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-04-21", "death_date": "2024-04-21", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 581, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-11-16", "death_date": "2023-11-16", "job": "Superhero", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 582, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 583, "name": "<PERSON>, Le livre de ma mère", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 584, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 585, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 586, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 587, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 588, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-07-23", "death_date": "2024-07-23", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 589, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 590, "name": "Maxime Rover", "is_fictional": false, "birth_date": "2024-06-26", "death_date": "2024-06-26", "job": "Philosophe français", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 591, "name": "Capt", "is_fictional": false, "birth_date": "2023-12-10", "death_date": "2023-12-10", "job": "Théoricien des pouvoirs", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 592, "name": "<PERSON> le <PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 593, "name": "<PERSON> AS2", "is_fictional": false, "birth_date": "2024-01-31", "death_date": "2024-01-31", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 594, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 595, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 596, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 597, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 598, "name": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> tardif", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 599, "name": "Le tueur du Zodiaque", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 600, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-02-20", "death_date": "2024-02-20", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 601, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-26", "death_date": "2024-06-26", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 602, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-07-13", "death_date": "2024-07-13", "job": "Past french minister", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 603, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 604, "name": "La Cicciolina", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 605, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 606, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-14", "death_date": "2024-06-14", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 607, "name": "<PERSON><PERSON><PERSON>", "is_fictional": true, "birth_date": "2024-01-13", "death_date": "2024-01-13", "job": "Scientist", "description": "<PERSON><PERSON><PERSON> is a scientist in the book «Kilomètre zéro». He works with <PERSON> in neurosciences.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 608, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 609, "name": "Amethyst", "is_fictional": false, "birth_date": "2024-01-27", "death_date": "2024-01-27", "job": "Cystal gem, super-heroin", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 610, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "2023-09-22", "death_date": "2023-09-22", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 611, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 612, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 613, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-07-13", "death_date": "2024-07-13", "job": "Past french minister", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 614, "name": "<PERSON><PERSON> Proust", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 615, "name": "La voix", "is_fictional": false, "birth_date": "2024-05-13", "death_date": "2024-05-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 616, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 617, "name": "<PERSON>, <PERSON> Petite Marchande de prose", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 618, "name": "<PERSON>ine rouge", "is_fictional": false, "birth_date": "2023-08-28", "death_date": "2023-08-28", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 619, "name": "<PERSON> (<PERSON> Dad)", "is_fictional": false, "birth_date": "2024-01-13", "death_date": "2024-01-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 620, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-08-12", "death_date": "2024-08-12", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 621, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 622, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 623, "name": "The Killer", "is_fictional": false, "birth_date": "2023-12-12", "death_date": "2023-12-12", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 624, "name": "<PERSON> de la belle-mère", "is_fictional": false, "birth_date": "2023-09-24", "death_date": "2023-09-24", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 625, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "0215-09-06", "death_date": "0164-09-06", "job": "King", "description": "<PERSON><PERSON> IV <PERSON> was a Greek Hellenistic king who ruled the Seleucid Empire from 175 BC until his death in 164 BC. He was a son of King <PERSON><PERSON> the Great. Originally named <PERSON><PERSON><PERSON><PERSON>, he assumed the name <PERSON><PERSON> after he ascended the throne.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/8/8b/Antiochus_IV_Epiphanes_-_Altes_Museum_-_Berlin_-_Germany_2017.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 626, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 627, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-09-05", "death_date": "2023-09-05", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 628, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-06-20", "death_date": "2024-06-20", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 629, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-04-21", "death_date": "2024-04-21", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 630, "name": "<PERSON><PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 631, "name": "Obffs", "is_fictional": false, "birth_date": "2023-12-19", "death_date": "2023-12-19", "job": "Volté. Membre du Bosquet.", "description": "<PERSON><PERSON><PERSON> is a fictional character in the book La Zone du Dehors.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 632, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 633, "name": "<PERSON><PERSON><PERSON>, Hell", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 634, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 635, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-09-26", "death_date": "2023-09-26", "job": "Procreate co-funder", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 549, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-08-29", "death_date": "2024-08-29", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:17", "updated_at": "2025-08-08 13:02:17", "socials": []}, {"id": 550, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-09-16", "death_date": "2024-09-16", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:17", "updated_at": "2025-08-08 13:02:17", "socials": []}, {"id": 551, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-09-08", "death_date": "2024-09-08", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:17", "updated_at": "2025-08-08 13:02:17", "socials": []}, {"id": 552, "name": "<PERSON>o of Citium", "is_fictional": false, "birth_date": "2024-08-27", "death_date": "2024-08-27", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:17", "updated_at": "2025-08-08 13:02:17", "socials": []}, {"id": 553, "name": "A", "is_fictional": false, "birth_date": "2023-12-12", "death_date": "2023-12-12", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:17", "updated_at": "2025-08-08 13:02:17", "socials": []}, {"id": 554, "name": "Proverbe algérien", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:17", "updated_at": "2025-08-08 13:02:17", "socials": []}, {"id": 555, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "2023-11-16", "death_date": "2023-11-16", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:17", "updated_at": "2025-08-08 13:02:17", "socials": []}, {"id": 556, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-12-11", "death_date": "2023-12-11", "job": "Public relations in advertising", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:17", "updated_at": "2025-08-08 13:02:17", "socials": []}, {"id": 557, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-04-23", "death_date": "2024-04-23", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:17", "updated_at": "2025-08-08 13:02:17", "socials": []}, {"id": 558, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-01-07", "death_date": "2024-01-07", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:17", "updated_at": "2025-08-08 13:02:17", "socials": []}, {"id": 523, "name": "I.A.L. Diamond", "is_fictional": false, "birth_date": "2023-07-31", "death_date": "2023-07-31", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 524, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-07-26", "death_date": "2023-07-26", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 525, "name": "<PERSON>", "is_fictional": false, "birth_date": "2021-05-26", "death_date": "2021-05-26", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 526, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2021-05-25", "death_date": "2021-05-25", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 527, "name": "Teest", "is_fictional": false, "job": "CEO", "description": "qweqe", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 528, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-03-18", "death_date": "2023-03-18", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 529, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-07-29", "death_date": "2023-07-29", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 530, "name": "<PERSON><PERSON>", "is_fictional": false, "death_date": "2025-03-05", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 531, "name": "Emilia", "is_fictional": false, "birth_date": "2021-06-13", "death_date": "2021-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 532, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-07-28", "death_date": "2023-07-28", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 533, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-07-31", "death_date": "2023-07-31", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 534, "name": "Plutarch", "is_fictional": false, "death_date": "2024-09-05", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 535, "name": "<PERSON><PERSON>'s mother", "is_fictional": false, "birth_date": "2021-07-03", "death_date": "2021-07-03", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 536, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2021-09-03", "death_date": "2021-09-03", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 537, "name": "<PERSON>", "is_fictional": false, "birth_date": "2021-07-15", "death_date": "2021-07-15", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 538, "name": "Firefox design team", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 539, "name": "<PERSON>", "is_fictional": false, "death_date": "2023-07-31", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 540, "name": "Stuntman <PERSON>", "is_fictional": false, "death_date": "2024-07-21", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 541, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-07-26", "death_date": "2023-07-26", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 542, "name": "Sartre", "is_fictional": false, "birth_date": "2023-01-14", "death_date": "2023-01-14", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 543, "name": "<PERSON>", "is_fictional": false, "birth_date": "2021-05-16", "death_date": "2021-05-16", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 544, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2023-07-26", "death_date": "2023-07-26", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 545, "name": "Emannuel <PERSON>", "is_fictional": false, "birth_date": "2021-05-29", "death_date": "2021-05-29", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 546, "name": "Chewbacca", "is_fictional": false, "death_date": "2024-06-07", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 547, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "2022-05-17", "death_date": "2022-05-17", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 548, "name": "<PERSON>", "is_fictional": false, "death_date": "2024-08-03", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "socials": []}, {"id": 482, "name": "<PERSON><PERSON>", "is_fictional": true, "job": "Student", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 483, "name": "<PERSON>", "is_fictional": false, "birth_date": "2021-05-25", "death_date": "2021-05-25", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 484, "name": "<PERSON><PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2022-12-08", "death_date": "2022-12-08", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 485, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-02-16", "death_date": "2023-02-16", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 486, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2021-07-15", "death_date": "2021-07-15", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 487, "name": "<PERSON>", "is_fictional": false, "birth_date": "2022-02-20", "death_date": "2022-02-20", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 488, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-07-31", "death_date": "2023-07-31", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 489, "name": "Chapelin", "is_fictional": false, "birth_date": "2023-07-26", "death_date": "2023-07-26", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 490, "name": "<PERSON>", "is_fictional": false, "birth_date": "2021-06-25", "death_date": "2021-06-25", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 491, "name": "Aristote", "is_fictional": false, "birth_date": "2023-07-26", "death_date": "2023-07-26", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 492, "name": "<PERSON>", "is_fictional": false, "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 493, "name": "<PERSON>", "is_fictional": false, "birth_date": "2021-05-11", "death_date": "2021-05-11", "job": "Psychologist", "description": "ep 5", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 494, "name": "<PERSON>", "is_fictional": false, "birth_date": "2021-08-06", "death_date": "2021-08-06", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 495, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "death_date": "2024-08-27", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 496, "name": "<PERSON>", "is_fictional": false, "birth_date": "2021-07-15", "death_date": "2021-07-15", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 497, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-03-22", "death_date": "2023-03-22", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 498, "name": "Vi", "is_fictional": false, "birth_date": "2021-11-21", "death_date": "2021-11-21", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 499, "name": "<PERSON>", "is_fictional": false, "death_date": "2025-01-06", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 500, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-01-15", "death_date": "2023-01-15", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 501, "name": "<PERSON><PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2021-11-22", "death_date": "2021-11-22", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 502, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2023-07-27", "death_date": "2023-07-27", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 503, "name": "<PERSON><PERSON><PERSON><PERSON>", "is_fictional": false, "death_date": "2024-08-26", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 504, "name": "<PERSON>", "is_fictional": false, "death_date": "2024-08-03", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 505, "name": "<PERSON>", "is_fictional": false, "birth_date": "2022-11-12", "death_date": "2022-11-12", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 506, "name": "<PERSON><PERSON>", "is_fictional": true, "birth_date": "2023-03-25", "death_date": "2023-03-25", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 507, "name": "Local woman", "is_fictional": false, "birth_date": "2021-05-29", "death_date": "2021-05-29", "job": "<PERSON><PERSON><PERSON>", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 508, "name": "<PERSON><PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2023-07-26", "death_date": "2023-07-26", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 509, "name": "<PERSON><PERSON>", "is_fictional": false, "death_date": "2024-08-29", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 510, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-07-03", "death_date": "2023-07-03", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 511, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "2023-07-26", "death_date": "2023-07-26", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 512, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-05-05", "death_date": "2023-05-05", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 513, "name": "<PERSON>", "is_fictional": false, "birth_date": "2021-05-16", "death_date": "2021-05-16", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 514, "name": "<PERSON>", "is_fictional": false, "birth_date": "2021-07-27", "death_date": "2021-07-27", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 515, "name": "Erin", "is_fictional": false, "birth_date": "2021-09-29", "death_date": "2021-09-29", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 516, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2021-11-27", "death_date": "2021-11-27", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 517, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "is_fictional": false, "death_date": "2024-06-09", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 518, "name": "Psy", "is_fictional": false, "birth_date": "2021-05-11", "death_date": "2021-05-11", "description": "ep 5", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 519, "name": "<PERSON>", "is_fictional": false, "birth_date": "2021-11-21", "death_date": "2021-11-21", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 520, "name": "<PERSON>", "is_fictional": false, "birth_date": "2022-02-19", "death_date": "2022-02-19", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 521, "name": "Man in the cave", "is_fictional": false, "birth_date": "2021-11-22", "death_date": "2021-11-22", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 522, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-05-31", "death_date": "2023-05-31", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "socials": []}, {"id": 471, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-06-02", "death_date": "2023-06-02", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:47", "updated_at": "2025-08-03 05:59:47", "socials": []}, {"id": 472, "name": "<PERSON>", "is_fictional": false, "death_date": "2024-06-09", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:47", "updated_at": "2025-08-03 05:59:47", "socials": []}, {"id": 473, "name": "Local man", "is_fictional": false, "birth_date": "2021-05-29", "death_date": "2021-05-29", "job": "<PERSON><PERSON><PERSON>", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:47", "updated_at": "2025-08-03 05:59:47", "socials": []}, {"id": 474, "name": "<PERSON><PERSON>", "is_fictional": false, "death_date": "2024-06-07", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:47", "updated_at": "2025-08-03 05:59:47", "socials": []}, {"id": 475, "name": "<PERSON>hams", "is_fictional": false, "birth_date": "2021-07-31", "death_date": "2021-07-31", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:47", "updated_at": "2025-08-03 05:59:47", "socials": []}, {"id": 476, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "2021-09-19", "death_date": "2021-09-19", "job": "Pilote de ligne", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:47", "updated_at": "2025-08-03 05:59:47", "socials": []}, {"id": 477, "name": "<PERSON>", "is_fictional": false, "death_date": "2024-06-07", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:47", "updated_at": "2025-08-03 05:59:47", "socials": []}, {"id": 478, "name": "<PERSON>. <PERSON> the Divine", "is_fictional": false, "birth_date": "2023-01-26", "death_date": "2023-01-26", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:47", "updated_at": "2025-08-03 05:59:47", "socials": []}, {"id": 479, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-03-11", "death_date": "2023-03-11", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:47", "updated_at": "2025-08-03 05:59:47", "socials": []}, {"id": 480, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "2023-02-02", "death_date": "2023-02-02", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:47", "updated_at": "2025-08-03 05:59:47", "socials": []}, {"id": 481, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-02-10", "death_date": "2023-02-10", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-03 05:59:47", "updated_at": "2025-08-03 05:59:47", "socials": []}, {"id": 195, "name": "Queen", "is_fictional": false, "birth_date": "2024-07-22T19:47:44.096Z", "death_date": "2024-07-22T19:43:51.266Z", "job": "British Rock Band", "description": "Queen are a British rock band formed in London in 1970 by <PERSON> (lead vocals, piano), <PERSON> (guitar, vocals), and <PERSON> (drums, vocals), later joined by <PERSON> (bass). Their earliest works were influenced by progressive rock, hard rock, and heavy metal, but the band gradually ventured into more conventional and radio-friendly works by incorporating further styles, such as arena rock and pop rock.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FQueen--1721677667747.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-07-22T19:47:46.607Z", "updated_at": "2024-07-22T19:47:46.607Z", "socials": []}, {"id": 280, "name": "<PERSON>", "is_fictional": true, "birth_date": "2024-07-22T19:41:57.476Z", "death_date": "2024-07-22T19:39:47.750Z", "description": "<PERSON> is an FBI special agent who believes in the existence of extraterrestrials and the paranormal.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FFox-Mulder-1721677320963.png?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-07-22T19:41:59.685Z", "updated_at": "2024-07-22T19:41:59.685Z", "socials": []}, {"id": 433, "name": "<PERSON>", "is_fictional": true, "birth_date": "2024-07-22T19:39:18.720Z", "death_date": "2024-07-22T19:34:52.384Z", "description": "<PERSON> is a space ranger action figure and one of the main characters in the Toy Story franchise.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBuzz-Lightyear--1721677175198.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-07-22T19:39:29.607Z", "updated_at": "2024-07-22T19:39:29.608Z", "socials": []}, {"id": 57, "name": "<PERSON><PERSON> Vader", "is_fictional": true, "birth_date": "2024-07-22T19:33:57.525Z", "death_date": "2024-07-22T19:28:35.302Z", "description": "<PERSON><PERSON> is a Sith Lord and the father of <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDarth-Vader-1721676852595.png?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-07-22T19:34:05.955Z", "updated_at": "2024-07-22T19:34:05.955Z", "socials": []}, {"id": 410, "name": "Groot", "is_fictional": true, "birth_date": "2024-07-22T18:51:25.303Z", "death_date": "2024-07-22T18:48:20.299Z", "description": "<PERSON><PERSON> is a character appearing in American comic books published by Marvel Comics. Created by <PERSON>, <PERSON> and <PERSON>, the character first appeared in Tales to Astonish #13 (November 1960). An extraterrestrial, sentient tree-like creature, the original <PERSON><PERSON> first appeared as an invader that intended to capture humans for experimentation. The character can only say the repeated line \"I am <PERSON><PERSON>\", but has different meanings depending on context. In the Marvel Cinematic Universe, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> the Destroyer are able to understand him.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FGroot-1721674299987.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-07-22T18:51:33.695Z", "updated_at": "2024-07-22T18:51:33.696Z", "socials": []}, {"id": 152, "name": "<PERSON>", "is_fictional": true, "birth_date": "2024-07-22T18:40:21.792Z", "death_date": "2024-07-22T18:37:50.651Z", "description": "<PERSON> is an actor and food enthusiast, known for his role on Days of Our Lives.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FJoey-<PERSON>bbiani--1721673626593.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-07-22T18:40:24.344Z", "updated_at": "2024-07-22T18:40:24.344Z", "socials": []}, {"id": 389, "name": "<PERSON>", "is_fictional": true, "birth_date": "2024-07-22T18:37:38.664Z", "death_date": "2024-07-22T18:35:09.894Z", "description": "<PERSON> is a theoretical physicist known for his social ineptitude and catchphrase.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2<PERSON><PERSON><PERSON>-Cooper--1721673462908.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-07-22T18:37:41.628Z", "updated_at": "2024-07-22T18:37:41.628Z", "socials": []}, {"id": 60, "name": "<PERSON>", "is_fictional": true, "birth_date": "2024-07-22T18:33:40.286Z", "death_date": "2024-07-22T18:27:49.400Z", "description": "<PERSON> is the bumbling husband of <PERSON><PERSON> and father to <PERSON>, <PERSON>, and <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FH<PERSON>-<PERSON>--1721673224588.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-07-22T18:33:43.402Z", "updated_at": "2024-07-22T18:33:43.403Z", "socials": []}, {"id": 269, "name": "<PERSON>pock", "is_fictional": true, "birth_date": "2024-07-22T18:27:12.579Z", "death_date": "2024-07-22T18:23:59.835Z", "description": "<PERSON><PERSON><PERSON> is a Vulcan-human hybrid, the first officer and science officer of the USS Enterprise.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSpock-1721672837426.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-07-22T18:27:16.122Z", "updated_at": "2024-07-22T18:27:16.122Z", "socials": []}, {"id": 235, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-07-22T18:22:59.825Z", "death_date": "2024-07-22T18:20:27.032Z", "description": "<PERSON> is the head of <PERSON> Stark, Lord of Winterfell, and Warden of the North.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FNed-Stark-1721672583961.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-07-22T18:23:02.754Z", "updated_at": "2024-07-22T18:23:02.755Z", "socials": []}, {"id": 427, "name": "<PERSON>", "is_fictional": true, "birth_date": "2024-07-22T18:20:13.881Z", "death_date": "2024-07-22T18:16:17.717Z", "description": "<PERSON>, also known by his alias <PERSON><PERSON><PERSON>, is the fictional antihero or villain protagonist of the American crime drama television series Breaking Bad, portrayed by <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FWalter-White--1721672429098.png?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-07-22T18:20:23.559Z", "updated_at": "2024-07-22T18:20:23.559Z", "socials": []}, {"id": 285, "name": "<PERSON>", "is_fictional": true, "birth_date": "2024-07-22T18:06:12.073Z", "death_date": "2024-07-22T18:00:27.104Z", "description": "<PERSON><PERSON><PERSON> is a fictional character portrayed by <PERSON> and created by <PERSON> and <PERSON> for the CBS television series How I Met Your Mother (2005–2014).", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBarney-Stinson-1721671586675.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-07-22T18:06:20.814Z", "updated_at": "2024-07-22T18:06:20.814Z", "socials": []}, {"id": 426, "name": "<PERSON>", "is_fictional": true, "birth_date": "2024-07-22T15:46:19.971Z", "death_date": "2024-07-22T15:42:29.029Z", "description": "<PERSON> (born October 18, 1968), portrayed by <PERSON>, is one of the six main characters of the NBC sitcom <PERSON>. <PERSON> is considered by many to be the most intelligent member of the group and is noted for his goofy but lovable demeanor.His relationship with <PERSON> was included in TV Guide's list of the best TV couples of all time, as well as Entertainment Weekly's \"30 Best 'Will They/Won't They?' TV Couples\". <PERSON>, who was one of the executive producers of the show, had worked with <PERSON><PERSON><PERSON><PERSON> before, so the writers were already developing <PERSON>'s character in <PERSON><PERSON><PERSON><PERSON>'s voice. Hence, <PERSON><PERSON><PERSON><PERSON> was the first person to be cast on the show.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FR<PERSON>-<PERSON><PERSON>-1721663195167.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-07-22T15:46:29.724Z", "updated_at": "2024-07-22T15:46:29.724Z", "socials": []}, {"id": 149, "name": "Han Solo", "is_fictional": false, "birth_date": "2024-07-22T15:38:58.503Z", "death_date": "2024-07-22T15:37:29.427Z", "description": "<PERSON> is a fictional character in the Star Wars franchise, a smuggler who becomes a leader in the Rebel Alliance. He is known for his roguish charm and quick wit.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FHan-Solo-1721662753585.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-07-22T15:39:07.592Z", "updated_at": "2024-07-22T15:39:07.592Z", "socials": []}, {"id": 432, "name": "Sirius Black", "is_fictional": true, "birth_date": "2024-07-22T14:42:33.862Z", "death_date": "2024-07-22T14:39:54.035Z", "description": "<PERSON> is a fictional character in <PERSON><PERSON> <PERSON><PERSON>'s <PERSON> series. <PERSON> was first mentioned briefly in <PERSON> and the Philosopher's Stone as a wizard who lent <PERSON><PERSON><PERSON> a flying motorbike shortly after <PERSON> killed <PERSON> and <PERSON>. His character becomes prominent in <PERSON> and the Prisoner of Azkaban, in which he is the titular prisoner, and is also revealed to be the godfather of the central character <PERSON>. He is portrayed in the film adaptations by <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSirius-Black-1721659361573.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-07-22T14:42:35.644Z", "updated_at": "2024-07-22T14:42:35.644Z", "socials": []}, {"id": 211, "name": "Albus Dumbledore", "is_fictional": true, "birth_date": "2024-07-22T14:30:20.651Z", "death_date": "2024-07-22T14:24:28.976Z", "description": "Prof. <PERSON><PERSON> is a fictional character in the <PERSON> series of novels by <PERSON><PERSON> <PERSON><PERSON>. For most of the series, he is the headmaster of the wizarding school Hogwarts. As part of his backstory, it is revealed that he is the founder and leader of the Order of the Phoenix, an organisation dedicated to fighting the Dark wizard Lord <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAlbus-Dumbledore--1721658633329.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-07-22T14:30:27.940Z", "updated_at": "2024-07-22T14:30:27.940Z", "socials": []}, {"id": 366, "name": "Scar", "is_fictional": true, "birth_date": "2024-07-22T14:20:31.210Z", "death_date": "2024-07-22T14:16:32.210Z", "description": "<PERSON><PERSON> is a fictional character and the main antagonist in Disney's The Lion King franchise. He was created by screenwriters <PERSON>, <PERSON> and <PERSON>, and animated by <PERSON>. <PERSON><PERSON> is introduced in the first film as the ruthless, power-hungry younger brother of <PERSON><PERSON><PERSON>, ruler of the Pride Lands. Originally first in line to <PERSON><PERSON><PERSON>'s throne until he is suddenly replaced by <PERSON><PERSON><PERSON>'s son <PERSON><PERSON>, <PERSON><PERSON> decides to lead an army of hyenas in his plot to take the throne by killing <PERSON><PERSON><PERSON> and <PERSON><PERSON>, who escapes into exile, ultimately blaming his brother's death on his nephew.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FScar-1721658046627.png?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-07-22T14:20:41.068Z", "updated_at": "2024-07-22T14:20:41.068Z", "socials": []}, {"id": 261, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-07-22T09:18:00.700Z", "death_date": "2024-07-22T09:13:11.046Z", "job": "25th president of France ", "description": "<PERSON> born 21 December 1977 is a French politician who has been serving as the 25th president of France since 2017 and ex officio one of the two Co-Princes of Andorra. He previously was Minister of Economics, Industry and Digital Affairs under President <PERSON> from 2014 to 2016 and Deputy Secretary-General to the President from 2012 to 2014. He has been a member of Renaissance since he founded it in 2016.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FEmmanuel-Macron-1721639896791.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-07-22T09:18:10.994Z", "updated_at": "2024-07-22T09:18:10.994Z", "socials": []}, {"id": 83, "name": "<PERSON>", "is_fictional": true, "birth_date": "2024-07-22T09:12:10.947Z", "death_date": "2024-07-22T09:06:23.950Z", "description": "<PERSON> G<PERSON> is a 1994 American comedy-drama film directed by <PERSON> and written by <PERSON>. It is an adaptation of the 1986 novel of the same name by <PERSON>, and stars <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>.\n\n", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FForrest-Gump-1721675406046.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-07-22T09:12:21.233Z", "updated_at": "2024-07-22T09:12:21.233Z", "socials": []}, {"id": 407, "name": "<PERSON><PERSON><PERSON>", "is_fictional": true, "birth_date": "2024-07-21T15:47:37.049Z", "death_date": "2024-07-17T19:31:33.967Z", "description": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>; born February 26, 1978) is an American businesswoman. Until September 22, 2019, she served as chief brand and impact officer at WeWork, a company founded by her husband, <PERSON>, and oversaw its education program WeGrow.\n\nIn the Apple TV+ series WeCrashed (2022), <PERSON><PERSON><PERSON> is portrayed by <PERSON>. <PERSON> also appears in the nonfiction book Billion Dollar Loser", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FRebekah-Newmann-1721638843787.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-07-21T15:47:45.514Z", "updated_at": "2024-07-21T15:47:45.514Z", "socials": []}, {"id": 461, "name": "The Cozy Grove Scout Motto", "is_fictional": true, "birth_date": "2024-06-10T10:36:01.661Z", "death_date": "2024-06-10T10:25:25.845Z", "job": "Fictional camping group", "description": "Cozy Grove is a life-sim game about camping on a haunted, ever-changing island.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe-<PERSON><PERSON>-Grove-Scout-Mo<PERSON>-1718015778913.png?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-06-10T10:36:11.380Z", "updated_at": "2024-06-10T10:36:11.380Z", "socials": []}, {"id": 290, "name": "<PERSON>", "is_fictional": false, "birth_date": "1977-12-24T23:00:00.000Z", "death_date": "2024-06-05T13:18:50.819Z", "job": "Iconic English comic actor and filmmaker", "description": "Sir <PERSON> was an English comic actor, filmmaker, and composer who rose to fame in the era of silent film. He became a worldwide icon through his screen persona, the Tramp, and is considered one of the film industry's most important figures. His career spanned more than 75 years, from childhood in the Victorian era until a year before his death in 1977, and encompassed both adulation and controversy.\n\n- <PERSON> (1889-1977) was an English comic actor, filmmaker, and composer who rose to fame in the era of silent film with his iconic \"Tramp\" character.\n\n- He was a pioneering filmmaker who acted in, directed, produced, edited, and composed the music for most of his films. Some of his most famous works include The Kid (1921), The Gold Rush (1925), City Lights (1931), Modern Times (1936), and The Great Dictator (1940).\n\n- <PERSON> had a very difficult childhood marked by poverty and hardship. His mother was committed to a mental asylum when he was young, and he was sent to a workhouse twice before age 9.\n\n- He began performing on stage at a very young age, making his first amateur appearance at age 5.[1] By age 26, he had become one of the highest paid people in the world with a contract for $670,000 per year.\n\n- <PERSON> composed the scores and songs for several of his films, including the popular songs \"Smile\" from Modern Times and \"This Is My Song\" from <PERSON> Countess from Hong Kong. He won an Academy Award for Best Original Score for Limelight in 1973.\n\n- He was married four times, including three marriages to teenagers when he was much older. His turbulent personal life, political views, and marriages to young women caused scandals.\n\n- <PERSON> was a perfectionist who would do hundreds of takes to get a scene right. For City Lights, he made an actress repeat just two words (\"Flower, sir\") 342 times.\n\n- He co-founded the movie studio United Artists in 1919 with Douglas Fairbanks, <PERSON> Pickford, and D.W. Griffith as a way to maintain creative control over their films.\n", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FC<PERSON><PERSON>-<PERSON>-*************.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-06-05T13:19:08.660Z", "updated_at": "2025-07-26 17:10:05", "socials": []}, {"id": 115, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-05T13:04:19.161Z", "death_date": "1986-04-13T22:00:00.000Z", "job": "French existentialist philosopher, writer, and feminist activist", "description": "<PERSON> was a French existentialist philosopher, writer, social theorist, and feminist activist. Though she did not consider herself a philosopher, nor was she considered one at the time of her death, she had a significant influence on both feminist existentialism and feminist theory.\n\n- She was a French writer, philosopher, and feminist theorist who is best known for her seminal work \"The Second Sex\" published in 1949. This groundbreaking book analyzed the oppression of women in society and laid the foundation for modern feminism.\n\n- <PERSON><PERSON><PERSON> was born in Paris in 1908 into a bourgeois family. She was raised Catholic but became an atheist at age 14.\n\n- She studied at the University of Paris (Sorbonne) and passed the highly competitive agrégation exam in philosophy at the youngest possible age of 21, coming second only to her lifelong companion <PERSON><PERSON><PERSON>.\n\n- <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> had an unconventional open relationship for over 50 years, never marrying but remaining intellectual and romantic partners until <PERSON><PERSON><PERSON>'s death in 1980.\n\n- In addition to \"The Second Sex\", she wrote novels, memoirs, biographies and political treatises, often exploring existentialist themes. Her novel \"The Mandarins\" won France's prestigious Prix Goncourt in 1954.\n\n- She was deeply involved in political activism, speaking out against the French colonial policies in Algeria and supporting feminist causes like the legalization of abortion in France.\n\n- <PERSON><PERSON><PERSON> died in Paris in 1986 at age 78 and is buried next to <PERSON><PERSON><PERSON> in the Montparnasse Cemetery.\n", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSimone-de-Beauvoir-1717592673545.png?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-06-05T13:04:27.700Z", "updated_at": "2024-06-05T13:04:27.701Z", "socials": []}, {"id": 72, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-06-05T12:58:44.200Z", "death_date": "1992-04-05T22:00:00.000Z", "job": "American writer and professor of biochemistry", "description": "<PERSON> was an American writer and professor of biochemistry at Boston University. During his lifetime, <PERSON><PERSON><PERSON> was considered one of the \"Big Three\" science fiction writers, along with <PERSON> and <PERSON>. A prolific writer, he wrote or edited more than 500 books. He also wrote an estimated 90,000 letters and postcards.\n\n- <PERSON> was born in Petrovichi, Russia (now part of Belarus) on January 2, 1920. His family immigrated to the United States when he was 3 years old.\n\n- He taught himself to read at age 5 and was a voracious reader from a young age, devouring the science fiction magazines sold at his family's candy stores in Brooklyn.\n\n- <PERSON><PERSON><PERSON> earned a PhD in biochemistry from Columbia University in 1948 and worked as a professor of biochemistry at Boston University for many years.\n\n- He was one of the most prolific writers of all time, authoring or editing over 500 books across genres like science fiction, mysteries, and popular science. His most famous works are the Foundation and Robot series.\n\n- <PERSON><PERSON><PERSON> coined the term \"robotics\" and developed the Three Laws of Robotics which have become hugely influential in science fiction and real-world robotics.\n\n- He won numerous awards including multiple Hugo and Nebula Awards for his science fiction writing.\n\n- <PERSON><PERSON><PERSON> was an atheist and a prominent member of the humanist movement, serving as president of the American Humanist Association from 1985 until his death in 1992.\n\n- He had a fear of flying and did so only twice in his life, preferring to travel by ship or train.\n\n- <PERSON><PERSON><PERSON> died in New York City on April 6, 1992 at age 72 from heart and kidney failure, complicated by HIV contracted from a blood transfusion.\n", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FIssac-Asimov-1717592345520.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-06-05T12:58:58.087Z", "updated_at": "2024-06-05T12:58:58.087Z", "socials": []}, {"id": 292, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-06-05T00:45:48.158Z", "death_date": "2024-06-05T00:32:20.039Z", "job": "Pseudonymous person or persons who developed Bitcoin", "description": "Here are some key facts about <PERSON><PERSON>, the pseudonymous creator of Bitcoin:\n\n- <PERSON><PERSON> is not a real name, but rather a pseudonym used by the unknown person or group who created Bitcoin.\n\n- They published the Bitcoin white paper in October 2008, outlining the concept of a decentralized peer-to-peer electronic cash system.\n\n- In January 2009, <PERSON><PERSON><PERSON> released the first Bitcoin software and mined the first block of bitcoins, known as the \"genesis block.\"\n\n- <PERSON><PERSON><PERSON> communicated with early Bitcoin developers through online forums and email until around 2010, after which they disappeared from public view.\n\n- Their true identity remains a mystery, with various theories speculating that <PERSON><PERSON><PERSON> could be an individual like <PERSON> or <PERSON>, or even a group of developers.\n\n- <PERSON><PERSON><PERSON> is estimated to have mined around 1 million bitcoins, which would be worth billions of dollars today, but this stash has remained untouched.\n\n- In 2015, <PERSON><PERSON><PERSON> was nominated for the Nobel Prize in Economics for their contributions to the creation of Bitcoin.\n\n- Despite numerous claims by individuals like <PERSON>, no one has provided conclusive evidence of being the real <PERSON><PERSON>.\n\n- The reasons for <PERSON><PERSON><PERSON>'s anonymity are unknown, but it aligns with Bitcoin's decentralized principles and may have been for privacy and security.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSatoshi-Na<PERSON>moto-1717548369362.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-06-05T00:46:02.499Z", "updated_at": "2024-06-05T00:46:02.499Z", "socials": []}, {"id": 170, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-05-14T01:54:23.453Z", "death_date": "2024-05-12T16:19:06.786Z", "job": "American researcher, professor, and writer", "description": "<PERSON> is an American researcher, professor, and author who is widely regarded as a pioneer in the fields of design, usability engineering, and cognitive science. Here are some key facts about him:\n\n📕 • He is best known for his book \"The Design of Everyday Things\", which advocates for user-centered design principles and explores how design impacts our ability to interact with everyday objects and technologies.\n\n🍎 • He coined the term \"User Experience Architect\" when he joined Apple in 1993, making him the first person to have \"User Experience\" in his job title. This helped establish and popularize the field of user experience (UX) design.\n\n🏛️ • He co-founded the Nielsen Norman Group, a leading consulting firm in the user experience field, along with <PERSON>.\n\n🎙️ • He has served on numerous advisory boards and committees, including at Motorola, Toyota, TED Conference, Panasonic, and Encyclopædia Britannica, contributing his expertise in design and human-computer interaction.\n\n🎖️ • He received the prestigious Benjamin Franklin Medal in Computer and Cognitive Science in 2006 for his pioneering work in the field of user-centered design.\n\n🤓 • In 2011, he was elected as a member of the National Academy of Engineering for developing design principles based on human cognition that enhance human-technology interaction.\n\n🚀 • He advocates that while academics can refine existing products, it is technologists who accomplish major breakthroughs and innovations in product design.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDonal<PERSON>-Norman-1715651686430.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-05-14T01:54:38.644Z", "updated_at": "2024-05-14T01:54:38.644Z", "socials": []}, {"id": 320, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-03-12T23:26:36.021Z", "death_date": "2024-02-28T12:27:53.180Z", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-03-12T23:26:42.096Z", "updated_at": "2024-03-12T23:26:42.096Z", "socials": []}, {"id": 33, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-03-12T23:25:03.149Z", "death_date": "2024-03-12T22:04:30.052Z", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-03-12T23:26:16.518Z", "updated_at": "2024-03-12T23:26:16.518Z", "socials": []}, {"id": 132, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-03-01T12:26:20.972Z", "death_date": "1968-02-29T23:00:00.000Z", "job": "American politician, Former United States Secretary of Health, Education, and Welfare", "description": "<PERSON>, born on October 8, 1912, in Los Angeles, was a prominent figure in American politics and public service. He served as the Secretary of Health, Education, and Welfare under President <PERSON>. <PERSON> was known for his advocacy for citizen participation and campaign finance reform, earning him the title \"the father of campaign finance reform.\" He played a significant role in various initiatives, including the 1965 Elementary and Secondary Education Act and the enforcement of civil rights laws.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON><PERSON><PERSON>-1709296007543.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-03-01T12:26:40.085Z", "updated_at": "2024-03-01T12:26:40.085Z", "socials": []}, {"id": 233, "name": "<PERSON> \"<PERSON> (FANGS)", "is_fictional": true, "birth_date": "2024-01-22T09:24:18.809Z", "death_date": "2024-01-15T01:27:04.226Z", "description": "<PERSON> is a character in the TV series «A Murder at the End of the World». He's an artist under the name FANG<PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FW<PERSON>iam \"<PERSON>\" <PERSON> (FANGS)-1705915483322.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-22T09:24:34.000Z", "updated_at": "2024-01-22T09:24:34.000Z", "socials": []}, {"id": 442, "name": "<PERSON>", "is_fictional": false, "birth_date": "1947-11-29T23:00:00.000Z", "death_date": "2023-08-01T16:48:15.595Z", "job": "American playwright and filmmaker", "description": "<PERSON> is an American playwright, filmmaker, and author. He won a Pulitzer Prize and received Tony nominations for his plays <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDavid Mamet-1705914198114.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-22T09:03:07.676Z", "updated_at": "2024-01-22T09:03:07.676Z", "socials": []}, {"id": 97, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-01-19T22:03:10.230Z", "death_date": "1915-12-14T00:00:00.000Z", "job": "Philanthropist", "description": "<PERSON>, known as <PERSON>, born on October 4, 1832 and died on December 14, 1915, was a French philanthropist and woman of letters.\n\nA member of the Société des gens de lettres and Officer of the Palmes académiques, <PERSON> is best known for her thoughts and aphorisms. She has also written poems and song lyrics. The philosophy behind her works is summed up in the preface to <PERSON><PERSON><PERSON>, her first book: \"To study man, to seek to know him better, is not to condemn oneself to love him less, it is to learn to pity him in all his failings, to excuse him in many of his errors, to judge more closely the difficulties of the struggle, and to account to him for his efforts\".", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAnne Barratin-*************.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-19T22:03:14.809Z", "updated_at": "2024-01-19T22:03:14.809Z", "socials": []}, {"id": 312, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-01-19T21:58:35.311Z", "death_date": "2023-08-01T16:48:15.595Z", "job": "French-Polish film director", "description": "<PERSON> is a French and Polish film director, producer, screenwriter, and actor. He is the recipient of numerous accolades, including an Academy Award, two British Academy Film Awards, ten César Awards, two Golden Globe Awards, as well as the Golden Bear and a Palme d'Or.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FRoman Polanski-*************.png?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-19T21:58:41.527Z", "updated_at": "2024-01-19T21:58:41.527Z", "socials": []}, {"id": 111, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-01-19T21:54:50.073Z", "death_date": "1984-06-21T22:00:00.000Z", "job": "American theatre director and film producer", "description": "<PERSON> was an American theatre and film director, producer, and screenwriter. Born in Wisconsin, he studied in Germany with <PERSON><PERSON> and then returned to the United States.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON><PERSON>-1705701295492.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-19T21:54:54.110Z", "updated_at": "2024-01-19T21:54:54.111Z", "socials": []}, {"id": 141, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-01-19T21:46:15.638Z", "death_date": "2024-01-17T10:27:20.621Z", "job": "French singer-songwriter, actor, and poker player", "description": "<PERSON>, born 14 May 1959), better known by his stage name <PERSON>, is a French singer-songwriter, actor and professional poker player.\n\nHis first success came as an actor, in 1979's Le Coup de sirocco. He continued acting in films, on television, and in the theater while pursuing his singing career. His first single, \"Vide\" (\"Empty\"), released in 1982, was not a success, but the follow-up, \"Marre de cette nana-là\" (\"Fed up with that chick\"), was a hit.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPatrick Bruel-1705700792047.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-19T21:46:25.133Z", "updated_at": "2024-01-19T21:46:25.133Z", "socials": []}, {"id": 373, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-01-19T18:26:13.631Z", "death_date": "1699-04-20T22:00:00.000Z", "job": "17th-century French dramatist", "description": "<PERSON><PERSON><PERSON> was a French dramatist, one of the three great playwrights of 17th-century France, along with <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> as well as an important literary figure in the Western tradition and world literature.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FJean Racine-1705688780868.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-19T18:26:19.519Z", "updated_at": "2024-01-19T18:26:19.519Z", "socials": []}, {"id": 201, "name": "<PERSON><PERSON><PERSON>", "is_fictional": true, "birth_date": "2024-01-19T18:22:52.769Z", "death_date": "2024-01-17T10:43:35.183Z", "job": "Japanese researcher", "description": "<PERSON><PERSON><PERSON> is a japanese researcher who has a conversation with <PERSON><PERSON><PERSON> in a forest in Nepal.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-19T18:22:58.716Z", "updated_at": "2024-01-19T18:22:58.717Z", "socials": []}, {"id": 378, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-01-19T18:15:33.154Z", "death_date": "1967-06-06T23:00:00.000Z", "job": "American poet and writer", "description": "<PERSON> was an American poet, writer, critic, wit, and satirist based in New York; she was known for her caustic wisecracks, and eye for 20th-century urban foibles.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON><PERSON>-1705688139044.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-19T18:15:37.238Z", "updated_at": "2024-01-19T18:15:37.238Z", "socials": []}, {"id": 409, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-01-19T18:10:38.473Z", "death_date": "1963-11-21T23:00:00.000Z", "job": "English writer and philosopher", "description": "<PERSON><PERSON><PERSON> was an English writer and philosopher. His bibliography spans nearly 50 books, including novels and non-fiction works, as well as essays, narratives, and poems. Born into the prominent <PERSON> family, he graduated from Balliol College, Oxford, with an undergraduate degree in English literature.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FA<PERSON><PERSON> Huxley-1705687854596.png?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-19T18:10:48.072Z", "updated_at": "2024-01-19T18:10:48.072Z", "socials": []}, {"id": 370, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-01-19T17:48:14.842Z", "death_date": "2023-08-01T19:40:09.029Z", "job": "Author", "description": "Passionate about the secrets of dramaturgy, <PERSON><PERSON><PERSON> is the author of L'écriture de scénarios, considered a reference by professionals and students alike. He is also the author of the only lexicon dedicated to the profession: Le lexique du scénariste.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON>-<PERSON>-1705686502535.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-19T17:48:21.399Z", "updated_at": "2024-01-19T17:48:21.399Z", "socials": []}, {"id": 37, "name": "<PERSON><PERSON>", "is_fictional": true, "birth_date": "2024-01-19T17:37:42.898Z", "death_date": "2024-01-06T11:44:42.296Z", "job": "Interrogator, investigator", "description": "<PERSON><PERSON> – played by <PERSON> is an investigator in the TV series The Crowded Room.\n\nShe gets <PERSON> to slowly unveil his past through a serie of interviews.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FR<PERSON>-1705685870619.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-19T17:37:49.409Z", "updated_at": "2024-01-19T17:37:49.409Z", "socials": []}, {"id": 48, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-01-19T17:29:00.101Z", "death_date": "2024-01-18T17:52:11.502Z", "job": "French author", "description": "<PERSON>, born October 23, 1971, is a French financial advisor and writer.\n\nIn 2017, <PERSON> signed her first novel, <PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>ro : Le chemin du bonheur, inspired by her personal story. At the request of her best friend, the main character, <PERSON><PERSON><PERSON>, financial director of a start-up, puts her daily life on hold to complete an ascent of Annapurna,.\n\nPassionate about human relationships, the author devotes herself to feel-good literature about personal well-being and the quest for self.\n\nIn Plus jamais sans moi, published in 2023, the heroine sets off on a journey of self-discovery, this time on the roads to Santiago de Compostela.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMaud Ankaoua-1705685347133.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-19T17:29:04.846Z", "updated_at": "2024-01-19T17:29:04.846Z", "socials": []}, {"id": 440, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-01-19T17:28:19.351Z", "death_date": "1916-02-28T00:00:00.000Z", "job": "American-British author", "description": "<PERSON> was an American-British author. He is regarded as a key transitional figure between literary realism and literary modernism, and is considered by many to be among the greatest novelists in the English language.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FH<PERSON><PERSON>-1705685310936.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-19T17:28:24.633Z", "updated_at": "2024-01-19T17:28:24.633Z", "socials": []}, {"id": 282, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-01-19T16:45:45.144Z", "death_date": "0763-01-18T23:00:00.000Z", "job": "Philosopher", "description": "<PERSON><PERSON><PERSON> was an 8th-century CE Indian philosopher, Buddhist monk, poet, and scholar at the mahavihara of Nalanda. He was an adherent of the Mādhyamaka philosophy of Nāgārjuna. He is also considered to be one of the 84 mahasiddhas and is known as <PERSON><PERSON><PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FShantideva-1705682752071.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-19T16:45:50.211Z", "updated_at": "2024-01-19T16:45:50.211Z", "socials": []}, {"id": 283, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-01-19T16:45:17.334Z", "death_date": "1931-04-10T00:00:00.000Z", "job": "Lebanese-American writer and poet", "description": "<PERSON><PERSON><PERSON>, usually referred to in English as <PERSON><PERSON><PERSON>, was a Lebanese-American writer, poet and visual artist; he was also considered a philosopher, although he himself rejected the title.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON><PERSON>-1705682734428.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-19T16:45:27.701Z", "updated_at": "2024-01-19T16:45:27.701Z", "socials": []}, {"id": 355, "name": "<PERSON><PERSON>", "is_fictional": true, "birth_date": "2024-01-06T11:44:10.049Z", "death_date": "2024-01-06T11:32:48.479Z", "job": "Guide, carrier, tour guide", "description": "<PERSON><PERSON> is <PERSON><PERSON><PERSON>'s guide in the book «Kilomètre zéro». They travel through Annapurnas mountains in Népal in order to retrieve a book. During the journey, <PERSON><PERSON> has several conversations with <PERSON><PERSON><PERSON> about happiness, emotions, reality perception, expectations.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-06T11:44:21.053Z", "updated_at": "2024-01-06T11:44:21.053Z", "socials": []}, {"id": 353, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-01-03T10:54:08.582Z", "death_date": "2013-12-04T23:00:00.000Z", "job": "Former President of South Africa", "description": "<PERSON> was a South African anti-apartheid activist and politician who served as the first president of South Africa from 1994 to 1999. He was the country's first black head of state and the first elected in a fully representative democratic election.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON><PERSON> Mandelak-1704279268988.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-03T10:54:20.798Z", "updated_at": "2024-01-03T10:54:20.798Z", "socials": []}, {"id": 359, "name": "<PERSON><PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-01-02T11:50:18.914Z", "death_date": "0406-01-01T23:00:00.000Z", "job": "5th century BC Athenian tragic playwright", "description": "<PERSON><PERSON><PERSON><PERSON> (c. 497/496 – winter 406/405 BC) was an ancient Greek tragedian, known as one of three from whom at least one play has survived in full. <PERSON><PERSON><PERSON><PERSON> wrote over 120 plays, but only seven have survived in a complete form: <PERSON>, <PERSON><PERSON><PERSON>, Women of Trachis, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> at Colonus. \n\nFor almost fifty years, <PERSON><PERSON><PERSON><PERSON> was the most celebrated playwright in the dramatic competitions of the city-state of Athens which took place during the religious festivals of the Lenaea and the Dionysia. He competed in thirty competitions, won twenty-four, and was never judged lower than second place. <PERSON><PERSON><PERSON><PERSON> won thirteen competitions, and was sometimes defeated by <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> won four.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSophocles-1704196226589.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-02T11:50:24.547Z", "updated_at": "2024-01-02T11:50:24.547Z", "socials": []}, {"id": 251, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-01-02T11:37:47.104Z", "death_date": "2024-01-02T11:25:41.868Z", "job": "14th Dal<PERSON><PERSON><PERSON><PERSON>", "description": "The 14th Dalai Lama, known to the Tibetan people as <PERSON><PERSON><PERSON>, is, as the incumbent <PERSON><PERSON>, the highest spiritual leader and head of Tibet. He is considered a living Bodhisattva; specifically, an emanation of <PERSON><PERSON><PERSON><PERSON> in Sanskrit, and <PERSON><PERSON><PERSON> in Tibetan.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FTenzin Gyatso-1704195490632.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-02T11:38:01.940Z", "updated_at": "2024-01-02T11:38:01.940Z", "socials": []}, {"id": 438, "name": "<PERSON><PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-01-01T14:24:29.302Z", "death_date": "2006-05-29T22:00:00.000Z", "job": "Japanese film director", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> was a Japanese film director. His main interest as a filmmaker lay in the depiction of the lower strata of Japanese society. A key figure in the Japanese New Wave, who continued working into the 21st century, <PERSON><PERSON> is the only director from Japan to win two Palme d'Or awards.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FShohei Imamura-1704119081019.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-01T14:24:34.154Z", "updated_at": "2024-01-01T14:24:34.154Z", "socials": []}, {"id": 92, "name": "<PERSON>", "is_fictional": false, "birth_date": "1908-11-27T23:50:39.000Z", "death_date": "2009-10-29T23:00:00.000Z", "job": "French anthropologist and ethnologist", "description": "<PERSON> was a French anthropologist and ethnologist whose work was key in the development of the theories of structuralism and structural anthropology. \n\nHe held the chair of Social Anthropology at the Collège de France between 1959 and 1982, was elected a member of the Académie française in 1973 and was a member of the School for Advanced Studies in the Social Sciences in Paris. He received numerous honours from universities and institutions throughout the world.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FC<PERSON><PERSON> -1704115930111.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-01T13:32:08.892Z", "updated_at": "2024-01-01T13:32:08.893Z", "socials": []}, {"id": 287, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-01-01T13:24:43.030Z", "death_date": "2024-01-01T12:17:02.130Z", "job": "American teacher", "description": "<PERSON> is one of the first American vipassana teachers, co-founder of the Insight Meditation Society with <PERSON> and <PERSON>, a contemporary author of numerous popular books on Buddhism, a resident guiding teacher at IMS, and a leader of retreats worldwide on insight (vipassana) and lovingkindness (metta) meditation.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON><PERSON>-1704115508206.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-01T13:25:04.714Z", "updated_at": "2024-01-01T13:25:04.714Z", "socials": []}, {"id": 65, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-01-01T13:24:43.030Z", "death_date": "2023-12-29T08:07:33.134Z", "job": "American Nun", "description": "<PERSON><PERSON> is an American Tibetan-Buddhist. She is an ordained nun, former acharya of Shambhala Buddhism and disciple of <PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON> has written several dozen books and audiobooks, and is principal teacher at Gampo Abbey in Nova Scotia.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPema Chödrön-1704115501228.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-01T13:24:56.717Z", "updated_at": "2024-01-01T13:24:56.717Z", "socials": []}, {"id": 159, "name": "<PERSON><PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-01-01T13:24:43.030Z", "death_date": "2023-12-28T10:26:04.624Z", "job": "French author", "description": "He's the author of «Observer les oiseaux de la baie du Mont-Saint-Michel».\n\nHe's done 16 years as a research officer and animator. He then  launched \"Birding Mont-Saint-Michel\" in 2018, a structure specializing in guided outings in Normandy and Brittany.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2<PERSON><PERSON><PERSON><PERSON>-1704115501133.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-01T13:24:54.151Z", "updated_at": "2024-01-01T13:24:54.151Z", "socials": []}, {"id": 295, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-01-01T13:24:43.030Z", "death_date": "2007-03-05T23:00:00.000Z", "job": "French sociologist and philosopher", "description": "<PERSON> was a French sociologist, philosopher and poet with interest in cultural studies. He is best known for his analyses of media, contemporary culture, and technological communication, as well as his formulation of concepts such as hyperreality.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON>rd-1704115500486.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-01T13:24:53.792Z", "updated_at": "2024-01-01T13:24:53.792Z", "socials": []}, {"id": 58, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-01-01T13:04:59.298Z", "death_date": "1941-01-03T22:00:00.000Z", "job": "French philosopher", "description": "<PERSON><PERSON><PERSON> was a French philosopher, who was influential in the traditions of analytic philosophy and continental philosophy, especially during the first half of the 20th century until the Second World War, but also after 1966 when <PERSON> published Le Bergsonisme.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON><PERSON>-1704114318578.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2024-01-01T13:05:12.454Z", "updated_at": "2024-01-01T13:05:12.454Z", "socials": []}, {"id": 100, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "1915-01-04T00:00:00.000Z", "death_date": "1998-02-16T23:00:00.000Z", "job": "Swiss psychologist", "description": "<PERSON><PERSON><PERSON> was a Swiss Jungian psychologist and scholar, known for her psychological interpretations of fairy tales and of alchemical manuscripts.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2<PERSON><PERSON><PERSON>-<PERSON>-1702427686964.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-12-13T00:34:45.709Z", "updated_at": "2023-12-13T00:34:45.709Z", "socials": []}, {"id": 13, "name": "<PERSON>", "is_fictional": false, "birth_date": "1882-10-21T22:00:00.000Z", "death_date": "1944-01-30T23:00:00.000Z", "job": "French novelist, essayist, diplomat and playwright", "description": "<PERSON><PERSON><PERSON> <PERSON> was a French novelist, essayist, diplomat and playwright. He is considered among the most important French dramatists of the period between World War I and World War II.\n\nHis work is noted for its stylistic elegance and poetic fantasy. <PERSON><PERSON><PERSON><PERSON>'s dominant theme is the relationship between man and woman—or in some cases, between man and some unattainable ideal.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON>-1702427292035.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-12-13T00:28:05.837Z", "updated_at": "2023-12-13T00:28:05.837Z", "socials": []}, {"id": 35, "name": "<PERSON> Williams", "is_fictional": false, "birth_date": "1911-03-26T00:00:00.000Z", "death_date": "1983-02-24T23:00:00.000Z", "job": "American playwright and screenwriter", "description": "<PERSON>, known by his pen name <PERSON>, was an American playwright and screenwriter. Along with contemporaries <PERSON> and <PERSON>, he is considered among the three foremost playwrights of 20th-century American drama.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FTenne<PERSON><PERSON> Williams -1702426186224.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-12-13T00:09:44.763Z", "updated_at": "2023-12-13T00:09:44.763Z", "socials": []}, {"id": 192, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-12-13T00:03:18.400Z", "death_date": "2023-08-09T14:13:00.493Z", "job": "Portuguese-American neuroscientist", "description": "<PERSON> is a Portuguese-American neuroscientist. He is currently the <PERSON> Chair in Neuroscience, as well as Professor of Psychology, Philosophy, and Neurology, at the University of Southern California, and, additionally, an adjunct professor at the Salk Institute.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAntonio Damasio-1702425804480.png?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-12-13T00:03:23.003Z", "updated_at": "2023-12-13T00:03:23.003Z", "socials": []}, {"id": 162, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-12-12T23:58:25.170Z", "death_date": "1980-04-28T22:00:00.000Z", "job": "Film director and screenwriter", "description": "Sir <PERSON> was an English film director, screenwriter, producer and editor. He is widely regarded as one of the most influential figures in the history of cinema. In a career spanning six decades, he directed over 50 feature films, many of which are still widely watched and studied today.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAlfred <PERSON>-1702425521914.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-12-12T23:58:36.161Z", "updated_at": "2023-12-12T23:58:36.161Z", "socials": []}, {"id": 395, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-12-12T23:30:17.006Z", "death_date": "2023-07-10T22:00:00.000Z", "job": "Czech-French novelist", "description": "<PERSON> was a Czech and French novelist. <PERSON><PERSON><PERSON> went into exile in France in 1975, acquiring citizenship in 1981. His Czechoslovak citizenship was revoked in 1979, but he was granted Czech citizenship in 2019. <PERSON><PERSON><PERSON>'s best-known work is The Unbearable Lightness of Being.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMilan Kundera-1702424337413.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-12-12T23:30:21.362Z", "updated_at": "2023-12-12T23:30:21.363Z", "socials": []}, {"id": 423, "name": "DFG", "is_fictional": false, "birth_date": "2023-12-12T23:23:48.148Z", "death_date": "2023-12-12T14:33:48.546Z", "job": "French streamer", "description": "DFG alias <PERSON><PERSON><PERSON><PERSON> is a french streamer on Twitch and Youtuber. He's the CEO of DFG Entertainment and TEAM AEGIS co-founder.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDFG-1702423443846.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-12-12T23:23:57.138Z", "updated_at": "2023-12-12T23:23:57.138Z", "socials": []}, {"id": 350, "name": "<PERSON> Renard", "is_fictional": true, "birth_date": "2023-12-09T14:00:20.114Z", "death_date": "2023-08-09T14:25:28.959Z", "description": "«<PERSON> re<PERSON>» (The fox) is a fictional character in the novel «Le Petit Prince». The fox teaches the prince that his rose is special because she is the object of the prince's love and time. Because he had \"tamed\" her, she's more precious than all of the other roses.\n\nThe fox also says that important things can only be seen with the heart, not the eyes.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLe Renard-1702130435299.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-12-09T14:00:29.595Z", "updated_at": "2023-12-09T14:00:29.595Z", "socials": []}, {"id": 383, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "2023-12-09T13:32:05.381Z", "death_date": "2023-12-09T13:14:13.006Z", "job": "American singer-songwriter and rapper", "description": "<PERSON><PERSON> is an American singer, songwriter, rapper and actress. She has received ten Grammy Award nominations, and won a Screen Actors Guild Award and a Children's and Family Emmy Award.\n\n<PERSON><PERSON><PERSON> has a mezzo-soprano voice. Her musical styles have been described as \"a soaring orchestral trip enlivened with blockbuster vocals, mysterious imagery and notes of Sixties pop and jazz\". More succintly: R&B, Pop, Funk, Psychedelic, Soul.\n\n<PERSON><PERSON><PERSON> has also ventured into acting, first gaining attention for starring in the 2016 films Moonlight and Hidden Figures. She has since starred in the films <PERSON> (2019) and Glass Onion (2022), and the television series Homecoming (2020).", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON><PERSON>-1702128813070.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-12-09T13:33:26.728Z", "updated_at": "2023-12-09T13:33:26.728Z", "socials": []}, {"id": 263, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2023-12-09T12:30:58.148Z", "death_date": "2023-11-29T22:17:56.220Z", "job": "Canadian singer-songwriter", "description": "<PERSON><PERSON><PERSON> is the stage name of <PERSON><PERSON><PERSON>, a Canadian singer-songwriter from Quebec. Beginning her career in 2009 as a performer at various festivals and music contests in Quebec, she released her self-titled debut EP in 2012. Her full-length album L'Alchimie des monstres followed in 2013.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FKlô Pelgag-1702125074332.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-12-09T12:31:08.042Z", "updated_at": "2023-12-09T12:31:08.042Z", "socials": []}, {"id": 382, "name": "<PERSON><PERSON><PERSON>", "is_fictional": true, "birth_date": "2023-11-10T13:50:50.328Z", "death_date": "2023-10-24T23:26:36.098Z", "job": "UN Deputy Undersecretary of Executive Administration, later UN Secretary-General", "description": "<PERSON><PERSON><PERSON>\tis a UN Deputy Undersecretary of Executive Administration of Earth in the TV series The Expanse.\n\nShe's played by <PERSON><PERSON><PERSON><PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FC<PERSON>isjen <PERSON>-1699624274809.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-11-10T13:51:08.623Z", "updated_at": "2023-11-10T13:51:08.623Z", "socials": []}, {"id": 368, "name": "Calisto", "is_fictional": true, "birth_date": "2023-10-23T14:28:52.341Z", "death_date": "2023-10-23T14:03:05.009Z", "job": "The Wise Ones leader", "description": "<PERSON><PERSON><PERSON> <PERSON> alias <PERSON> <PERSON> is a fictional character played by <PERSON> in the tv series The Changeling.\n\nShe's the leader of the community \"The Wise Ones\" living on the North Brother Island.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCalisto-1698071352200.png?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-10-23T14:29:06.387Z", "updated_at": "2023-10-23T14:29:06.387Z", "socials": []}, {"id": 7, "name": "<PERSON><PERSON><PERSON>", "is_fictional": true, "birth_date": "2023-10-21T13:09:07.818Z", "death_date": "2023-10-21T12:50:43.299Z", "description": "<PERSON><PERSON><PERSON> is a shape-shifter and mysterious woman who proposed a deal to Roderick & Madeline <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FVerna-1697893764491.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-10-21T13:09:17.878Z", "updated_at": "2023-10-21T13:09:17.878Z", "socials": []}, {"id": 14, "name": "<PERSON>", "is_fictional": true, "birth_date": "2023-09-25T14:14:15.694Z", "death_date": "2023-09-25T14:09:10.693Z", "job": "<PERSON><PERSON><PERSON> and <PERSON>'s stay-at-home husband", "description": "<PERSON> is a sculptor and <PERSON>'s stay-at-home husband. He's played by <PERSON> in the TV limited series BEEF created by <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FGeorge Nakai-1695651270178.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-25T14:14:23.674Z", "updated_at": "2023-09-25T14:14:23.674Z", "socials": []}, {"id": 68, "name": "<PERSON>", "is_fictional": true, "birth_date": "2023-09-25T14:04:33.934Z", "death_date": "2023-09-25T13:53:39.085Z", "job": "Unemployed student", "description": "<PERSON>'s unmotivated younger brother. He likes video-games. This character is played by <PERSON> in the TV limited series BEEF, created by <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2<PERSON><PERSON>-1695650709908.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-25T14:05:03.003Z", "updated_at": "2023-09-25T14:05:03.003Z", "socials": []}, {"id": 40, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-09-25T11:38:42.762Z", "death_date": "2023-09-25T11:27:50.194Z", "job": "Hungarian artist", "description": "<PERSON> (born 1924) is a Hungarian media artist living and working in France. <PERSON><PERSON><PERSON> is widely considered to be a pioneer of computer art and generative art, and is also one of the first women to use computers in her art practice.\n\nBorn in Hungary, she studied aesthetics and art history at the Hungarian University of Fine Arts. In the 1940s and 50s, she created non-representational paintings. By 1959 she was making combinatorial images; in 1968, she would use a computer to create her first algorithmic drawings.\n\nIn the 1960s, she founded two groups in France concerned with the use of technology within the arts: the Groupe de Recherche d’Art Visuel and Art et Informatique. In 1976 took place her first solo exhibition in the gallery of the London Polytechnic.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FVera Molnar-1695641940276.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-25T11:38:53.305Z", "updated_at": "2023-09-25T11:38:53.305Z", "socials": []}, {"id": 304, "name": "Arte", "is_fictional": false, "birth_date": "2023-09-25T09:59:24.694Z", "death_date": "2023-09-25T09:34:26.315Z", "job": "Franco-German TV network", "description": "Arte (Association relative à la télévision européenne) is a European public service channel dedicated to culture.\n\nArte was initiated as a symbol of Franco-German friendship and had been championed since 1988 by French President <PERSON> and German Chancellor <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FArte-1695636044854.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-25T10:00:24.277Z", "updated_at": "2023-09-25T10:00:24.277Z", "socials": []}, {"id": 275, "name": "<PERSON><PERSON><PERSON>", "is_fictional": true, "birth_date": "2023-09-22T00:35:29.075Z", "death_date": "1970-01-01T00:00:00.000Z", "job": "Crewmember on the Dewalt", "description": "<PERSON><PERSON><PERSON> is a member of <PERSON><PERSON>'s polyamorous family and a crewmember on the Dewalt.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FOksana-1695342946452.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-22T00:35:39.758Z", "updated_at": "2023-09-22T00:35:39.758Z", "socials": []}, {"id": 306, "name": "<PERSON>", "is_fictional": true, "birth_date": "2023-08-16T22:22:54.930Z", "death_date": "2023-08-16T22:22:54.930Z", "job": "High school graduate", "description": "<PERSON> is a recent high school graduate who participates in Panic at the last minute, when her mom spends the tuition <PERSON> saved. She lives in a trailer.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLily Nill-1695134514605.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-19T14:41:46.315Z", "updated_at": "2023-09-19T14:41:46.315Z", "socials": []}, {"id": 157, "name": "<PERSON>", "is_fictional": false, "birth_date": "1914-01-25T00:00:00.000Z", "death_date": "1995-10-29T23:00:00.000Z", "job": "American physician", "description": "<PERSON> is considered the founder of the discipline of metapsychiatry, an attempt to integrate principles from metaphysics, spirituality, and psychology.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThomas Hora-1695133135501.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-19T14:18:46.416Z", "updated_at": "2023-09-19T14:18:46.416Z", "socials": []}, {"id": 470, "name": "<PERSON>", "is_fictional": true, "job": "<PERSON><PERSON>", "description": "<PERSON> is a nanny for <PERSON>'s children in the film \"Deadly illusions\". The character is played by <PERSON><PERSON><PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FGrace-1617623048009.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:29.268Z", "updated_at": "2023-09-12T23:47:29.268Z", "socials": []}, {"id": 469, "name": "Marine", "is_fictional": true, "job": "Caregiver", "description": "<PERSON> is a fictional character and <PERSON>'s cousin in the french book \"Le parfum du bonheur est plus fort sous le pluie\". She's a caregiver and takes a trip with her boyfriend when she meets <PERSON> at a family house.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:29.236Z", "updated_at": "2023-09-12T23:47:29.236Z", "socials": []}, {"id": 468, "name": "<PERSON>", "is_fictional": false, "job": "26th U.S. President", "description": "<PERSON> was an American statesman, politician, conservationist, naturalist, and writer who served as the 26th president of the United States from 1901 to 1909. He served as the 25th vice president from March to September 1901 and as the 33rd governor of New York from 1899 to 1900.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/1/1c/President_<PERSON>_-_<PERSON>h_Bros.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:29.201Z", "updated_at": "2023-09-12T23:47:29.201Z", "socials": []}, {"id": 467, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "job": "Lawyer", "description": "<PERSON><PERSON><PERSON> is a fictional character played by <PERSON><PERSON><PERSON><PERSON> in the TV miniseries The Woods.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:29.161Z", "updated_at": "2023-09-12T23:47:29.161Z", "socials": []}, {"id": 466, "name": "<PERSON>", "is_fictional": false, "birth_date": "1844-10-14T23:50:39.000Z", "birth_location": "Röcken, Germany", "death_date": "1900-08-24T23:50:39.000Z", "job": "Philosopher", "description": "<PERSON> was a German philosopher who became one of the most influential of all modern thinkers. His attempts to unmask the motives that underlie traditional Western religion, morality, and philosophy deeply affected generations of theologians, philosophers, psychologists, poets, novelists, and playwrights.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FF<PERSON><PERSON> Nietzsche-1614727502837.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:29.126Z", "updated_at": "2023-09-12T23:47:29.126Z", "socials": []}, {"id": 465, "name": "<PERSON>", "is_fictional": false, "job": "English biologist", "description": "<PERSON> PC FRS HonFRSE FLS was an English biologist and anthropologist specialising in comparative anatomy. He is known as \"<PERSON>'s Bulldog\" for his advocacy of <PERSON>'s theory of evolution.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/2/2e/T<PERSON><PERSON>.<PERSON>%28Woodburytype%29.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:29.092Z", "updated_at": "2023-09-12T23:47:29.092Z", "socials": []}, {"id": 464, "name": "<PERSON>", "is_fictional": false, "birth_date": "1969-12-31T23:00:00.000Z", "job": "American software developer", "description": "<PERSON> is an American software developer, author, blogger, and entrepreneur. He writes the computer programming blog Coding Horror.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FJef<PERSON>-1610659110611.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:29.056Z", "updated_at": "2023-09-12T23:47:29.056Z", "socials": []}, {"id": 463, "name": "<PERSON>", "is_fictional": false, "job": "Unemplyed", "description": "<PERSON> is a fictional character and <PERSON><PERSON>'s father in the TV series <PERSON>. The character is played by <PERSON>.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/1/11/<PERSON>_<PERSON>_at_the_2009_Tribeca_Film_Festival.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:29.022Z", "updated_at": "2023-09-12T23:47:29.022Z", "socials": []}, {"id": 462, "name": "<PERSON>", "is_fictional": false, "job": "American writer", "description": "<PERSON>, known by his pen name <PERSON>, was an American writer, humorist, entrepreneur, publisher, and lecturer. He was lauded as the \"greatest humorist this country has produced\", and <PERSON> called him \"the father of American literature\".", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.991Z", "updated_at": "2023-09-12T23:47:28.991Z", "socials": []}, {"id": 460, "name": "<PERSON>", "is_fictional": true, "job": "Lawyer", "description": "<PERSON> is a fictional character played by <PERSON> in the TV series \"Dirty Jonh\". In the show, he's <PERSON>'s divorce lawyer.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBob <PERSON>-1604440709017.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.954Z", "updated_at": "2023-09-12T23:47:28.954Z", "socials": []}, {"id": 459, "name": "PNL", "is_fictional": false, "birth_date": "2013-12-31T23:00:00.000Z", "birth_location": "Ile-de-France, Corbeil-Essonnes, France", "job": "Band", "description": "PNL is a French rap band formed in 2014 by <PERSON><PERSON><PERSON> and N.O.S, from Corbeil-Essonnes.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPNL-1604576692717.png?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.921Z", "updated_at": "2023-09-12T23:47:28.921Z", "socials": []}, {"id": 458, "name": "<PERSON>", "is_fictional": false, "job": "Content producer", "description": "<PERSON> is a content producer and game developer.  He writes music, designs games, illustrates, and write. His YoutTube channel is talks about game development and arts.", "image_url": "https://yt3.ggpht.com/a/AATXAJyc2LVH7c4Ps8KiKkLZ6Wh-gerXn5omEHrDSg=s288-c-k-c0xffffffff-no-rj-mo", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.887Z", "updated_at": "2023-09-12T23:47:28.887Z", "socials": []}, {"id": 457, "name": "<PERSON>", "is_fictional": false, "job": "American actor", "description": "<PERSON> was an American actor and comedian. <PERSON> established a career in both stand-up comedy and feature film acting. He was known for his improvisation skills and the wide variety of memorable character voices he created. Many critics have lauded <PERSON> as one of the funniest comedians of all time.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/5/59/<PERSON>_<PERSON>_Happy_Feet_premiere.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.850Z", "updated_at": "2023-09-12T23:47:28.850Z", "socials": []}, {"id": 456, "name": "<PERSON>", "is_fictional": false, "job": "American screenwriter", "description": "<PERSON> is an American screenwriter, director and screenwriting teacher. He has served as a consultant on over 1,000 film scripts over the past three decades, and is also known for the screenwriting software program Blockbuster.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fjohn-truby.jpg?alt=media&token=a3defdd3-5dfb-4f8b-ade1-a451701eccb8", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.816Z", "updated_at": "2023-09-12T23:47:28.816Z", "socials": []}, {"id": 455, "name": "<PERSON>", "is_fictional": true, "birth_date": "1947-11-06T23:00:00.000Z", "birth_location": "Brooklyn, New York, United States", "job": "Housewife", "description": "<PERSON> is a fictional character played by <PERSON> in the TV series \"Dirty Jonh\". \n\nBecause \"Dirty Jonh' is a true-crime series, <PERSON> really exists and is an American woman who was convicted of killing her ex-husband, <PERSON>, and his second wife, <PERSON> (<PERSON><PERSON><PERSON>) <PERSON>, on November 5, 1989.\n\nAt a second trial on December 11, 1991, she was convicted of two counts of second-degree murder and later sentenced to 32-years-to-life in prison.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBetty Broderick-1604440255465.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.783Z", "updated_at": "2023-09-12T23:47:28.783Z", "socials": []}, {"id": 454, "name": "<PERSON>", "is_fictional": false, "job": "American columnist", "description": "<PERSON> \"<PERSON><PERSON>\" <PERSON>, also known as <PERSON>, was an American advice columnist and radio show host who began the Dear Abby column in 1956. It became the most widely syndicated newspaper column in the world, syndicated in 1,400 newspapers with 110 million readers.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/e/e9/<PERSON>_<PERSON>_1961.JPG", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.752Z", "updated_at": "2023-09-12T23:47:28.752Z", "socials": []}, {"id": 453, "name": "<PERSON>", "is_fictional": false, "job": "Policeman", "description": "<PERSON> is a fictional character in the 2019 TV series Watchmen, played by <PERSON>. He's <PERSON>'s grandfather, formerly known as <PERSON><PERSON>, the first masked hero who inspired the Minutemen.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FL<PERSON><PERSON>_<PERSON>ssett_Jr.jpg?alt=media&token=862d0926-8c3d-4210-a038-e1962c22c1a2", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.719Z", "updated_at": "2023-09-12T23:47:28.719Z", "socials": []}, {"id": 452, "name": "Holland March", "is_fictional": false, "job": "Private eye", "description": "<PERSON> is a fictional character played by <PERSON> in the film 'The Nice Guys'. He investigates the disappearance of a teenage girl (<PERSON><PERSON><PERSON>). ", "image_url": "https://upload.wikimedia.org/wikipedia/commons/f/f6/<PERSON>_<PERSON>_in_2018.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.684Z", "updated_at": "2023-09-12T23:47:28.684Z", "socials": []}, {"id": 451, "name": "<PERSON>", "is_fictional": false, "job": "Irish poet", "description": "<PERSON><PERSON><PERSON> was an Irish poet and playwright. After writing in different forms throughout the 1880s, the early 1890s saw him become one of the most popular playwrights in London.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/9/94/<PERSON>_<PERSON>_1889-05-23_WDDowney.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.650Z", "updated_at": "2023-09-12T23:47:28.650Z", "socials": []}, {"id": 450, "name": "<PERSON> G<PERSON>ule", "is_fictional": false, "job": "Youtube channel", "description": "Data Gueule is a french TV show and a web série. The program offers animated videos dealing with current events in a fun way, and condensed for educational purposes. Each episode attempts to reveal and decipher the mechanisms of society and their little-known aspects.", "image_url": "https://yt3.ggpht.com/a/AGF-l7-luyqwwKEE2thUIUI2dpGn8VkKZnCwMEjQ0w=s288-c-k-c0xffffffff-no-rj-mo", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.616Z", "updated_at": "2023-09-12T23:47:28.616Z", "socials": []}, {"id": 449, "name": "<PERSON>", "is_fictional": false, "description": "<PERSON> is an American entrepreneur, author and podcaster.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/thumb/e/ec/Ferriss_%40_%27Worlds_Collide%27_Hosted_by_<PERSON>_<PERSON>_%28cropped_to_face_of_<PERSON>%29.jpg/800px-<PERSON><PERSON>_%40_%27Worlds_Collide%27_Hosted_by_<PERSON>_<PERSON>_%28cropped_to_face_of_Ferris%29.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.581Z", "updated_at": "2023-09-12T23:47:28.581Z", "socials": []}, {"id": 448, "name": "<PERSON>", "is_fictional": false, "job": "English poet", "description": "<PERSON> was an English poet, playwright, and actor, widely regarded as the greatest writer in the English language and the world's greatest dramatist. He is often called England's national poet and the \"Bard of Avon\".", "image_url": "https://upload.wikimedia.org/wikipedia/commons/a/a2/Shakespeare.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.545Z", "updated_at": "2023-09-12T23:47:28.545Z", "socials": []}, {"id": 447, "name": "<PERSON>", "is_fictional": false, "job": "American professor", "description": "<PERSON> is an American professor of history at Princeton University. His research interests include the political, social, and urban/suburban history of 20th-century America, with a particular focus on the making of modern conservatism.", "image_url": "https://psmag.com/.image/c_limit%2Ccs_srgb%2Cq_auto:good%2Cw_300/MTYwMTY0MzA2NzI0MzMyOTcx/kroose.webp", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.510Z", "updated_at": "2023-09-12T23:47:28.510Z", "socials": []}, {"id": 446, "name": "<PERSON>", "is_fictional": false, "description": "French sociologist, anthropologist, philosopher and public intellectual.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/d/df/<PERSON>_<PERSON>_1969_%28cropped%29.tif", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.472Z", "updated_at": "2023-09-12T23:47:28.472Z", "socials": []}, {"id": 445, "name": "<PERSON>", "is_fictional": false, "job": "Philosopher", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> or <PERSON><PERSON><PERSON><PERSON> in Pali, also called the <PERSON><PERSON><PERSON> Buddha, the <PERSON><PERSON><PERSON>uni Buddha or simply the Buddha, after the title of Buddha, was a monk, mendicant, sage, philosopher, teacher and religious leader on whose teachings Buddhism was founded.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/f/ff/Buddha_in_Sarnath_Museum_%28D<PERSON><PERSON><PERSON>_<PERSON>tra%29.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.436Z", "updated_at": "2023-09-12T23:47:28.436Z", "socials": []}, {"id": 444, "name": "<PERSON>", "is_fictional": false, "job": "TV reporter", "description": "<PERSON> is a fictional character in the french movie \"La Cité de la peur\", played by <PERSON>.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/e/e7/AlainChabat2006.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.402Z", "updated_at": "2023-09-12T23:47:28.402Z", "socials": []}, {"id": 443, "name": "<PERSON>", "is_fictional": false, "description": "German mathematician and one of the few well-known astronomers of his time.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/4/46/<PERSON>_<PERSON>.signature.png", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.369Z", "updated_at": "2023-09-12T23:47:28.369Z", "socials": []}, {"id": 441, "name": "<PERSON>", "is_fictional": false, "description": "<PERSON> was a British chemist. He is known for his theories on the origin of life.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.335Z", "updated_at": "2023-09-12T23:47:28.335Z", "socials": []}, {"id": 439, "name": "<PERSON>", "is_fictional": false, "job": "Philosopher", "description": "<PERSON> was a German philosopher, economist, historian, sociologist, political theorist, journalist and socialist revolutionary. Born in Trier, Germany, <PERSON> studied law and philosophy at university. He married <PERSON> in 1843.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/d/d4/<PERSON>_<PERSON>_001.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.302Z", "updated_at": "2023-09-12T23:47:28.302Z", "socials": []}, {"id": 437, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "1952-02-01T23:00:00.000Z", "birth_location": "Cayenne, French Guiana", "job": "French Politician", "description": "<PERSON><PERSON> is a French politician who on 15 May 2012, was appointed Minister of Justice of France in the <PERSON><PERSON><PERSON> Government under President <PERSON>. She resigned from office on 27 January 2016.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FChristiane%20Taubira.jpg?alt=media&token=b264bf11-34b9-4420-b303-3af1f49a6ae2", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.268Z", "updated_at": "2023-09-12T23:47:28.268Z", "socials": []}, {"id": 436, "name": "iconfinder", "is_fictional": false, "job": "Business", "description": "Iconfinder is a marketplace for icons.", "image_url": "https://pbs.twimg.com/profile_images/1041986188821848069/spGckKc6_400x400.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.235Z", "updated_at": "2023-09-12T23:47:28.235Z", "socials": []}, {"id": 435, "name": "<PERSON>", "is_fictional": false, "birth_date": "1957-11-27T23:00:00.000Z", "birth_location": "Soho, London, United Kingdom", "death_date": "1927-08-11T23:00:00.000Z", "job": "Poet", "description": "<PERSON> was an English poet, painter, and printmaker. Largely unrecognised during his lifetime, <PERSON> is now considered a seminal figure in the history of the poetry and visual arts of the Romantic Age.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON><PERSON>-1608724072981.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.197Z", "updated_at": "2023-09-12T23:47:28.197Z", "socials": []}, {"id": 434, "name": "<PERSON>", "is_fictional": false, "description": "CEO, YouTuber, programmer & content maker. Creator of Grind Reel.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.163Z", "updated_at": "2023-09-12T23:47:28.163Z", "socials": []}, {"id": 431, "name": "<PERSON>", "is_fictional": false, "job": "Businessman", "description": "<PERSON> is an American businessman, author and former real estate investor. <PERSON> has authored and self-published books on real estate investing, football coaching, baseball coaching, success, and self-publishing.", "image_url": "https://cdn.shopify.com/s/files/1/0958/9924/t/3/assets/logo.png?v=12699847405552735349", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.129Z", "updated_at": "2023-09-12T23:47:28.129Z", "socials": []}, {"id": 430, "name": "<PERSON>", "is_fictional": false, "job": "French novelist", "description": "<PERSON>, known as <PERSON>, was a French novelist, playwright, screenwriter, essayist, and experimental filmmaker. Her script for the film Hiroshima mon amour earned her a nomination for Best Original Screenplay at the Academy Awards.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMarguerite%20Duras.jpg?alt=media&token=be9f5ae4-8c74-4b66-a965-33bbacbc49eb", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.094Z", "updated_at": "2023-09-12T23:47:28.094Z", "socials": []}, {"id": 429, "name": "<PERSON>", "is_fictional": false, "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FW<PERSON><PERSON>_Buffett_KU_Visit.jpg?alt=media&token=7173ba17-dede-41c7-9d9a-06657b87e48b", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.059Z", "updated_at": "2023-09-12T23:47:28.059Z", "socials": []}, {"id": 428, "name": "GJow", "is_fictional": false, "job": "YouTuber", "description": "<PERSON><PERSON><PERSON> is a french YouTuber and Twitch streamer. His channel has tutorials and let's play on different games like Brawlhalla and Pokemon.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FGJow.png?alt=media&token=8ebe427e-e146-4fc6-8516-80d51935f4dd", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:28.011Z", "updated_at": "2023-09-12T23:47:28.011Z", "socials": []}, {"id": 425, "name": "<PERSON>", "is_fictional": true, "job": "Housewife", "description": "<PERSON> is a fictional character played by <PERSON> in the TV series \"Behind Her Eyes\". In the show, the character seems to be either under her husband watch, Dr. <PERSON>, or in psychological distress.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAdele <PERSON>-1614721715016.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.979Z", "updated_at": "2023-09-12T23:47:27.979Z", "socials": []}, {"id": 424, "name": "Unknown", "is_fictional": false, "description": "An unknown person who does not want to disclose their identity.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.945Z", "updated_at": "2023-09-12T23:47:27.945Z", "socials": []}, {"id": 422, "name": "<PERSON>", "is_fictional": false, "job": "American author", "description": "<PERSON> is an American author and columnist who wrote a nationally syndicated humor column for the Miami Herald from 1983 to 2005. He has also written numerous books of humor and parody, as well as comic novels.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/0/04/<PERSON>-barry-post-hunt-2011.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.911Z", "updated_at": "2023-09-12T23:47:27.911Z", "socials": []}, {"id": 421, "name": "<PERSON>", "is_fictional": false, "job": "Entrepreneur - YouTube", "description": "<PERSON> is a french entrepreneur and YouTuber.", "image_url": "https://yt3.ggpht.com/a/AGF-l7-5Ds6LLd3CrZa4gwNRw5JFvQJnkco-QptkNA=s288-c-k-c0xffffffff-no-rj-mo", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.878Z", "updated_at": "2023-09-12T23:47:27.878Z", "socials": []}, {"id": 420, "name": "<PERSON>", "is_fictional": false, "description": "French mathematician, theoretical physicist, engineer, and philosopher of science.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/f/f4/PSM_V82_D416_<PERSON>_<PERSON>.png", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.844Z", "updated_at": "2023-09-12T23:47:27.844Z", "socials": []}, {"id": 419, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "1982-07-31T22:00:00.000Z", "birth_location": "Alençon, Normandy, France", "job": "French rapper", "description": "<PERSON><PERSON><PERSON><PERSON>, better known by his stage name <PERSON><PERSON><PERSON>, sometimes stylized as <PERSON><PERSON><PERSON><PERSON>, is a French rapper, songwriter, record producer, actor and film director.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FOrelsan-1619990138849.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.810Z", "updated_at": "2023-09-12T23:47:27.810Z", "socials": []}, {"id": 418, "name": "<PERSON>", "is_fictional": false, "job": "Content producer", "description": "Podcaster first, French second. Commenting tech and gaming in English et français.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPatrick%20Beja.jpg?alt=media&token=ce3802cd-b560-4219-9d48-6a3146848d3f", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.775Z", "updated_at": "2023-09-12T23:47:27.775Z", "socials": []}, {"id": 417, "name": "<PERSON>", "is_fictional": false, "job": "Essayist", "description": "<PERSON> was an American essayist, poet, and philosopher. A leading transcendentalist, he is best known for his book Walden, a reflection upon simple living in natural surroundings, and his essay \"Civil Disobedience\", an argument for disobedience to an unjust state.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/2/2a/<PERSON>_<PERSON>_<PERSON>_-_<PERSON>_<PERSON>_-_Restored_-_greyscale_-_straightened.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.736Z", "updated_at": "2023-09-12T23:47:27.736Z", "socials": []}, {"id": 416, "name": "<PERSON>", "is_fictional": false, "job": "American actress", "description": "<PERSON> is an American actress, political activist and former fashion model.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/4/46/<PERSON>_<PERSON>_Cannes_2015.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.705Z", "updated_at": "2023-09-12T23:47:27.705Z", "socials": []}, {"id": 415, "name": "<PERSON><PERSON> (Alain)", "is_fictional": false, "birth_date": "1868-03-02T23:50:39.000Z", "birth_location": "Mortagne-au-Perche, France", "death_date": "1951-06-01T23:00:00.000Z", "job": "French philosopher", "description": "<PERSON><PERSON><PERSON><PERSON>, commonly known as <PERSON>, was a French philosopher, journalist, and pacifist. He adopted his pseudonym in homage to the 15th-century Norman poet <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON> (Alain)-1610234003437.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.668Z", "updated_at": "2023-09-12T23:47:27.668Z", "socials": []}, {"id": 414, "name": "<PERSON>", "is_fictional": false, "birth_date": "1960-12-31T23:00:00.000Z", "birth_location": "Tel Aviv-Yafo, Israel", "job": "Researcher", "description": "<PERSON> is a lecturer, entrepreneur, author and behavior expert who focuses on brain research and brain retraining. He has appeared on Larry <PERSON> Live, <PERSON> and The Ellen <PERSON> Show. <PERSON><PERSON><PERSON> has been featured in eight movies, including The Secret[6] and Quest for Success with <PERSON> and the <PERSON><PERSON> Lama. He is founder and CEO of NeuroGym,[8] a company that focuses on brain-training methods.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FJ<PERSON><PERSON>-1604422130434.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.635Z", "updated_at": "2023-09-12T23:47:27.635Z", "socials": []}, {"id": 413, "name": "Saint Germain", "is_fictional": true, "birth_date": "2021-05-16T23:38:25.544Z", "death_date": "2021-05-16T23:38:25.544Z", "job": "Alchemist", "description": "<PERSON> is a alchemist and strange man researching a realm known as the Infinite Corridor.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSaint Germain-1621855489714.png?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.602Z", "updated_at": "2023-09-12T23:47:27.602Z", "socials": []}, {"id": 412, "name": "<PERSON>", "is_fictional": false, "job": "Housewife", "description": "<PERSON> is a fictional character in <PERSON><PERSON>'s magnum opus The Great Gatsby (1925). In the novel, <PERSON> is depicted as a married woman with a daughter who is reunited with her former lover <PERSON>, arousing the jealousy of her husband, <PERSON>. She is widely believed to have been based on <PERSON><PERSON><PERSON><PERSON>.[1] She has appeared in various media related to the novel, including feature films and plays.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDaisy%20Buchanan.png?alt=media&token=315fd670-8590-40b9-a1e0-8b33c3d17306", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.568Z", "updated_at": "2023-09-12T23:47:27.568Z", "socials": []}, {"id": 411, "name": "<PERSON>", "is_fictional": false, "job": "American football player", "description": "<PERSON> was an American football player, coach, and executive in the National Football League.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/b/b0/<PERSON>_lomba<PERSON>_bart_starr.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.530Z", "updated_at": "2023-09-12T23:47:27.530Z", "socials": []}, {"id": 408, "name": "QueenCamille", "is_fictional": false, "job": "Rédactrice - YouYubeuse", "description": "Rédactrice feel good, sexualité & société pour le magazine MadmoiZelle ainsi que YouTubeuse.", "image_url": "https://yt3.ggpht.com/a/AGF-l79hVm2ugWsLcsb2Wa-omIEfm_LGkHvZlViMJA=s288-c-k-c0xffffffff-no-rj-mo", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.495Z", "updated_at": "2023-09-12T23:47:27.495Z", "socials": []}, {"id": 406, "name": "<PERSON><PERSON>", "is_fictional": false, "job": "YouTube channel", "description": "Balade Mentale is a french YouTube channel exploring the Universe in poetry. It explores topics like the ending of the Universe, stars' lifespan, or the Library of Babel.", "image_url": "https://yt3.ggpht.com/a/AATXAJyIaTK7HHmPeyfI35MF7EEG3yrMw43vazxx8A=s288-c-k-c0xffffffff-no-rj-mo", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.459Z", "updated_at": "2023-09-12T23:47:27.459Z", "socials": []}, {"id": 405, "name": "Ancient Indian Proverb", "is_fictional": false, "birth_date": "2023-12-14T13:59:11.809Z", "death_date": "2023-12-14T13:59:11.809Z", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.425Z", "updated_at": "2023-09-12T23:47:27.425Z", "socials": []}, {"id": 404, "name": "<PERSON>", "is_fictional": true, "job": "The Fraternity Leader", "description": "<PERSON> is a fictional character played by <PERSON><PERSON> in the TV series \"La Révolution\". In the show, she's The Fraternity's leader and want to overthrow the gouvernment.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMarianne-1604357575948.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.385Z", "updated_at": "2023-09-12T23:47:27.385Z", "socials": []}, {"id": 403, "name": "<PERSON>", "is_fictional": false, "job": "American poet", "description": "<PERSON> was an American poet, singer, memoirist, and civil rights activist. She published seven autobiographies, three books of essays, several books of poetry, and is credited with a list of plays, movies, and television shows spanning over 50 years.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/4/4f/<PERSON><PERSON>_at_<PERSON>_inauguration_%28cropped_2%29.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.351Z", "updated_at": "2023-09-12T23:47:27.351Z", "socials": []}, {"id": 402, "name": "<PERSON>", "is_fictional": false, "job": "French poet", "description": "<PERSON> was a French poet, novelist, and dramatist of the Romantic movement. <PERSON> is considered to be one of the greatest and best-known French writers. Outside France, his most famous works are the novels Les Misérables, 1862, and The Hunchback of Notre-Dame, 1831.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.319Z", "updated_at": "2023-09-12T23:47:27.319Z", "socials": []}, {"id": 401, "name": "Grégoire Fraty", "is_fictional": false, "job": "Secrétaire Général de la Fédération Nationale des UROF", "description": "<PERSON><PERSON><PERSON><PERSON> is chief of staff of \"Federation Nationale des UROF\", a french national training organization. He's also one of the 150 people chosen at random for \"La Convention Cityoyenne pour le Climat\" which is political convention for the climate.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FGre%CC%81goire%20Fraty.png?alt=media&token=6e2b8ade-67d3-428e-914b-5a254d1f6de5", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.284Z", "updated_at": "2023-09-12T23:47:27.284Z", "socials": []}, {"id": 400, "name": "<PERSON>", "is_fictional": false, "job": "American engineer", "description": "<PERSON> is an American engineer, physician, and former NASA astronaut. She became the first black woman to travel into space when she served as a mission specialist aboard the Space Shuttle Endeavour.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/5/55/<PERSON>_<PERSON>_<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.249Z", "updated_at": "2023-09-12T23:47:27.249Z", "socials": []}, {"id": 399, "name": "<PERSON>", "is_fictional": false, "job": "Philosopher", "description": "<PERSON> was a Roman African, early Christian theologian and Neoplatonic philosopher from Numidia whose writings influenced the development of the Western Church and Western philosophy, and indirectly all of Western Christianity.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/9/91/<PERSON>_<PERSON>_-_<PERSON>_<PERSON>.JPG", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.214Z", "updated_at": "2023-09-12T23:47:27.214Z", "socials": []}, {"id": 398, "name": "<PERSON>", "is_fictional": false, "birth_date": "1901-12-04T23:50:39.000Z", "birth_location": "Würzburg, Germany", "death_date": "1976-01-31T23:00:00.000Z", "job": "German physicist", "description": "<PERSON> was a German theoretical physicist and one of the key pioneers of quantum mechanics. He published his work in 1925 in a breakthrough paper.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FWerner Heisenberg-1604325547826.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.181Z", "updated_at": "2023-09-12T23:47:27.181Z", "socials": []}, {"id": 397, "name": "<PERSON>", "is_fictional": false, "job": "Fictional Character", "description": "He's a student counsellor and <PERSON>'s husband in the American–Canadian comedy-drama television series You Me Her.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/9/96/Skr%C3%A5blikk_Skandinavia_-_NMD_2014_%2813949699607%29_%28cropped%29.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.142Z", "updated_at": "2023-09-12T23:47:27.142Z", "socials": []}, {"id": 396, "name": "<PERSON><PERSON>", "is_fictional": false, "job": "Concierge", "description": "<PERSON><PERSON> is a fictional character played by <PERSON><PERSON><PERSON> in the TV series <PERSON>. She often visits <PERSON> and take care of several flats.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBrigitte <PERSON>ren.png?alt=media&token=d1ba0250-4a7a-497b-9613-fe604d18aec2", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.110Z", "updated_at": "2023-09-12T23:47:27.110Z", "socials": []}, {"id": 394, "name": "<PERSON>", "is_fictional": false, "birth_date": "1900-06-28T23:50:39.000Z", "birth_location": "Lyon, France", "death_date": "1944-07-30T22:00:00.000Z", "job": "French writer", "description": "French writer, poet, aristocrat, journalist and pioneering aviator.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAntoine de Saint-Exupéry-1615981963039.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.077Z", "updated_at": "2023-09-12T23:47:27.077Z", "socials": []}, {"id": 393, "name": "Cloud", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.043Z", "updated_at": "2023-09-12T23:47:27.043Z", "socials": []}, {"id": 392, "name": "<PERSON>", "is_fictional": false, "job": "French content producer", "description": "<PERSON> is a french content producer specialized in history and archeology. She has a YouTube channel, Les Revues du Monde, where she tells stories on various subjects including ancient unresolved cases, critical expeditions, or myths. Her videos often gives a good look on the past. She also explains the work of an archeologist.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCharlie Danger-1610238225804.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:27.008Z", "updated_at": "2023-09-12T23:47:27.008Z", "socials": []}, {"id": 391, "name": "<PERSON>", "is_fictional": false, "birth_date": "1990-08-01T22:00:00.000Z", "birth_location": "C<PERSON>art, France", "job": "French YouTuber", "description": "<PERSON> is a french YouTuber talking about psychology, philosophy, social, among other subjects. He has a videos serie on philosophy where he explains concepts with a modern take.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCyrus North-1610062241446.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.975Z", "updated_at": "2023-09-12T23:47:26.975Z", "socials": []}, {"id": 390, "name": "<PERSON><PERSON>", "is_fictional": false, "job": "Artist", "description": "Cute and motivational drawings to brighten your day!", "image_url": "https://66.media.tumblr.com/6e1100ef90c5bfdb666c279b23eb0e33/ee9a59298c63dee7-c0/s500x750/1135ea532feaf9b4260394ae3b424023cfe988d1.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.940Z", "updated_at": "2023-09-12T23:47:26.940Z", "socials": []}, {"id": 388, "name": "<PERSON>", "is_fictional": true, "job": "Writer", "description": "<PERSON> is a fictional character played by <PERSON> in the film \"Vanilla Sky\". <PERSON> is <PERSON>'s friend and want to live as a writer. One day, he introduces one of his friend, <PERSON>, to <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2<PERSON><PERSON>-1614728624646.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.903Z", "updated_at": "2023-09-12T23:47:26.903Z", "socials": []}, {"id": 387, "name": "<PERSON>", "is_fictional": false, "job": "American folk artist", "description": "<PERSON>, known by her nickname <PERSON>, was an American folk artist. She began painting in earnest at the age of 78 and is often cited as an example of an individual who successfully began a career in the arts at an advanced age.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/a/ab/<PERSON>_<PERSON>_NYWTS.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.868Z", "updated_at": "2023-09-12T23:47:26.868Z", "socials": []}, {"id": 386, "name": "<PERSON><PERSON><PERSON><PERSON>", "is_fictional": true, "job": "Grandmother", "description": "<PERSON><PERSON><PERSON><PERSON> is a fictional character played by <PERSON><PERSON> in the TV series \"<PERSON><PERSON><PERSON> (The Gift)\". In the show, she's <PERSON><PERSON><PERSON>'s grandmother.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FZühre-1604423033229.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.830Z", "updated_at": "2023-09-12T23:47:26.830Z", "socials": []}, {"id": 385, "name": "<PERSON>", "is_fictional": false, "description": "<PERSON> was a Polish Marxist theorist, philosopher, economist, anti-war activist and revolutionary socialist who became a naturalized German citizen at the age of 28.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.795Z", "updated_at": "2023-09-12T23:47:26.795Z", "socials": []}, {"id": 384, "name": "<PERSON><PERSON>", "is_fictional": false, "description": "<PERSON><PERSON> FRS is a technology entrepreneur, investor, and engineer.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.744Z", "updated_at": "2023-09-12T23:47:26.744Z", "socials": []}, {"id": 381, "name": "<PERSON>", "is_fictional": false, "job": "Actress", "description": "<PERSON> was a British actress and humanitarian. Recognised as a film and fashion icon, she was ranked by the American Film Institute as the third-greatest female screen legend in Golden Age Hollywood, and was inducted into the International Best Dressed List Hall of Fame.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/5/5e/<PERSON>_<PERSON>_1956.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.711Z", "updated_at": "2023-09-12T23:47:26.711Z", "socials": []}, {"id": 380, "name": "<PERSON>", "is_fictional": false, "description": "<PERSON> is a lawyer in the miniseries Unbelievable.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.671Z", "updated_at": "2023-09-12T23:47:26.671Z", "socials": []}, {"id": 379, "name": "mistermv", "is_fictional": false, "job": "Streamer", "description": "<PERSON><PERSON><PERSON>, known as mister<PERSON><PERSON>, is a french streamer, video content producer, presentator and music artist. He's specialized in video games and speedrun. He started music in the 1990s and his streaming career in the early 2010s.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fmistermv-1604580113084.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.616Z", "updated_at": "2023-09-12T23:47:26.616Z", "socials": []}, {"id": 377, "name": "Aurora Lane", "is_fictional": true, "job": "Journalist, Writer", "description": "<PERSON> is a fictional character played by <PERSON> in the film \"Passengers\". She plays as a colonist on The Avalon, a sleeper ship.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAurora Lane-1615230438569.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.582Z", "updated_at": "2023-09-12T23:47:26.582Z", "socials": []}, {"id": 376, "name": "<PERSON>", "is_fictional": false, "birth_date": "1830-12-09T23:00:00.000Z", "birth_location": "Amherst, Massachusetts, United States", "death_date": "1886-05-14T22:00:00.000Z", "job": "American poet", "description": "<PERSON> was an American poet. Little known during her life, she has since been regarded as one of the most important figures in American poetry. <PERSON> was born in Amherst, Massachusetts into a prominent family with strong ties to its community. ", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FEmily <PERSON>-1619986469905.png?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.548Z", "updated_at": "2023-09-12T23:47:26.548Z", "socials": []}, {"id": 375, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "job": "Vietnamese monk", "description": "<PERSON><PERSON><PERSON><PERSON> is a Vietnamese Buddhist monk and peace activist, founder of the Plum Village Tradition. <PERSON><PERSON><PERSON><PERSON> spent most of his later life residing in the Plum Village Monastery in southwest France, travelling internationally to give retreats and talks.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/1/1b/Thich_<PERSON><PERSON>_Hanh_12_%28cropped%29.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.516Z", "updated_at": "2023-09-12T23:47:26.516Z", "socials": []}, {"id": 374, "name": "<PERSON><PERSON>", "is_fictional": false, "job": "<PERSON>", "description": "The 14th Dalai Lama is the current <PERSON><PERSON> Lama. Dalai Lamas are important monks of the Gelug school, the newest school of Tibetan Buddhism, which was formally headed by the <PERSON><PERSON><PERSON>.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/3/30/<PERSON><PERSON>_<PERSON>_in_2012_02.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.473Z", "updated_at": "2023-09-12T23:47:26.473Z", "socials": []}, {"id": 372, "name": "<PERSON>", "is_fictional": false, "job": "American journalist", "description": "<PERSON> is an American whistleblower who copied and leaked highly classified information from the National Security Agency in 2013 when he was a Central Intelligence Agency employee and subcontractor.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/6/60/<PERSON>_<PERSON>-2.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.439Z", "updated_at": "2023-09-12T23:47:26.439Z", "socials": []}, {"id": 371, "name": "<PERSON> (Film)", "is_fictional": false, "description": "<PERSON> is a fictional character played by <PERSON> in the film \"Meet Joe <PERSON>\".", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBrad%20Pitt.jpg?alt=media&token=452f6128-66a6-4d94-abf0-5a052d4fa95b", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.408Z", "updated_at": "2023-09-12T23:47:26.408Z", "socials": []}, {"id": 369, "name": "<PERSON>", "is_fictional": false, "job": "Baseball pitcher", "description": "<PERSON> \"<PERSON><PERSON>\" <PERSON> is an American former baseball pitcher who played sixteen seasons in Major League Baseball for the Pittsburgh Pirates. He played in 1950–51 and 1954–67. He batted and threw right-handed and was listed at 6 feet 2 inches and 195 pounds.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/5/58/Vern_Law_%285405222334%29_%28cropped%29.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.375Z", "updated_at": "2023-09-12T23:47:26.375Z", "socials": []}, {"id": 367, "name": "<PERSON>", "is_fictional": false, "job": "German diarist", "description": "<PERSON><PERSON> <PERSON> \"<PERSON>\" <PERSON> was a German-born Dutch-Jewish diarist. One of the most discussed Jewish victims of the Holocaust, she gained fame posthumously with the publication of The Diary of a Young Girl (originally <PERSON><PERSON> in Dutch; English: The Secret Annex), in which she documents her life in hiding from 1942 to 1944, during the German occupation of the Netherlands in World War II. It is one of the world's best known books and has been the basis for several plays and films.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/a/a4/AnneFrank1940_crop.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.340Z", "updated_at": "2023-09-12T23:47:26.340Z", "socials": []}, {"id": 365, "name": "<PERSON>", "is_fictional": false, "description": "<PERSON> is an American actress, filmmaker, musician, author, and comedian.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.306Z", "updated_at": "2023-09-12T23:47:26.306Z", "socials": []}, {"id": 364, "name": "<PERSON> Rire Jau<PERSON> (duo)", "is_fictional": false, "job": "YouTuber", "description": "<PERSON> Rire <PERSON> is two french YouTubers, <PERSON> and <PERSON>, which make humorous sketches videos on YouTube.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLe_Rire_Jaune.jpg?alt=media&token=aef2303c-4ae7-417e-bb5a-e34661e1b727", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.266Z", "updated_at": "2023-09-12T23:47:26.266Z", "socials": []}, {"id": 363, "name": "<PERSON>", "is_fictional": false, "birth_date": "1972-12-31T23:00:00.000Z", "birth_location": "Yonkers, New York State, United States", "death_date": "2021-03-20T14:26:23.249Z", "job": "American author", "description": "<PERSON> is an American author and educator, known for her work as an expert on food systems and as a sustainable food advocate.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAnna Lappé-1619991291595.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.211Z", "updated_at": "2023-09-12T23:47:26.211Z", "socials": []}, {"id": 362, "name": "<PERSON>", "is_fictional": false, "birth_date": "1804-07-03T22:00:00.000Z", "birth_location": "Salem, Massachusetts, United States", "death_date": "1864-05-18T22:00:00.000Z", "job": "American novelist", "description": "<PERSON> was an American novelist, dark romantic, and short story writer. His works often focus on history, morality, and religion. He was born in 1804 in Salem, Massachusetts, to <PERSON> and the former <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FN<PERSON><PERSON><PERSON> Hawthorne-1619986476534.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.177Z", "updated_at": "2023-09-12T23:47:26.177Z", "socials": []}, {"id": 361, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "job": "Video producer, Writer", "description": "<PERSON><PERSON><PERSON>, born in 1989 at Saint-Étienne, is a french video producer and writer. He's mainly known for his YouTube channel, DirtyBiology, started in June\n 2014.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/b/b3/L%C3%A9o_Grasset%2C_en_conf%C3%A9rence_%282017%29.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.142Z", "updated_at": "2023-09-12T23:47:26.142Z", "socials": []}, {"id": 360, "name": "Jérémie CORPINOT", "is_fictional": false, "description": "French developer and tech enthusiast.", "image_url": "https://pbs.twimg.com/profile_images/744898981583454208/2q2KOYz0_400x400.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.107Z", "updated_at": "2023-09-12T23:47:26.107Z", "socials": []}, {"id": 358, "name": "<PERSON>", "is_fictional": false, "job": "History professor", "description": "<PERSON> is a fictional character played by <PERSON> in the film Enemy. ", "image_url": "https://upload.wikimedia.org/wikipedia/commons/0/0e/<PERSON>_<PERSON>_2019_by_<PERSON>_<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.072Z", "updated_at": "2023-09-12T23:47:26.072Z", "socials": []}, {"id": 357, "name": "<PERSON><PERSON>", "is_fictional": false, "job": "Businessman", "description": "<PERSON> was a businessman, philanthropist and New Thought self-help book author.", "image_url": "https://images-na.ssl-images-amazon.com/images/I/81h2i4S145L._UX250_.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.035Z", "updated_at": "2023-09-12T23:47:26.035Z", "socials": []}, {"id": 356, "name": "<PERSON>'s grandfather", "is_fictional": true, "description": "<PERSON>'s grandfather is mentioned briefly in the book about his love for his wife.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:26.001Z", "updated_at": "2023-09-12T23:47:26.001Z", "socials": []}, {"id": 354, "name": "<PERSON><PERSON>", "is_fictional": true, "birth_date": "1936-08-31T23:00:00.000Z", "birth_location": "Tula, Russia, Soviet Union", "death_date": "1988-04-21T22:00:00.000Z", "job": "Inorganic chemist", "description": "<PERSON><PERSON> is a character played by <PERSON> in the TV series \"Chernobyl\". In the show, he's the deputy director of the Kurchatov Institute brought in to aid cleanup efforts. This character really existed.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON><PERSON>-1604527872729.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.961Z", "updated_at": "2023-09-12T23:47:25.961Z", "socials": []}, {"id": 352, "name": "<PERSON>", "is_fictional": false, "job": "Compositor", "description": "<PERSON> is a fictional character issued by <PERSON> in the film \"The Holiday\". He is a Hollywood music composer working with <PERSON> and an affiliate of her boyfriend <PERSON>.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/7/74/<PERSON>_<PERSON>_2_2011.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.923Z", "updated_at": "2023-09-12T23:47:25.923Z", "socials": []}, {"id": 351, "name": "<PERSON><PERSON>", "is_fictional": false, "job": "American author", "description": "<PERSON><PERSON> is an American author of the series Conversations with <PERSON>. He is also an actor, screenwriter, and speaker.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/e/e7/<PERSON><PERSON>_<PERSON>_<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.866Z", "updated_at": "2023-09-12T23:47:25.866Z", "socials": []}, {"id": 349, "name": "<PERSON>", "is_fictional": false, "job": "American educator", "description": "<PERSON> was an American educator, author, businessman, and keynote speaker. His most popular book is The 7 Habits of Highly Effective People.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/6/6d/<PERSON>_<PERSON>_2010.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.830Z", "updated_at": "2023-09-12T23:47:25.830Z", "socials": []}, {"id": 348, "name": "<PERSON>", "is_fictional": false, "job": "Geneticist", "description": "<PERSON> is a geneticist and a CEO in the TV series \"The One\".", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FReb<PERSON><PERSON> Webb-1617369335573.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.796Z", "updated_at": "2023-09-12T23:47:25.796Z", "socials": []}, {"id": 347, "name": "ZeratoR", "is_fictional": false, "birth_date": "1990-02-28T23:00:00.000Z", "birth_location": "Montpellier, France", "death_date": "2021-03-21T00:51:02.146Z", "job": "Streamer", "description": "<PERSON><PERSON><PERSON><PERSON>, alias <PERSON><PERSON>, is a french streamer, animator, and content producer specialized in video games. He career started in 2010 at aAa company, and became independant in January 2015.\n\nHe streams mainly on Twitch and published VOD on YouTube. He organizes and comments E-Sport events, charitable events (Z Event), and he owns a video games company called Unexpected.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FZeratoR-1619989827655.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.759Z", "updated_at": "2023-09-12T23:47:25.759Z", "socials": []}, {"id": 346, "name": "<PERSON>", "is_fictional": false, "birth_date": "1853-03-29T22:00:00.000Z", "birth_location": "Zundert, Netherlands", "death_date": "1890-07-28T22:00:00.000Z", "job": "Dutch painter", "description": "<PERSON> was a Dutch post-impressionist painter who posthumously became one of the most famous and influential figures in the history of Western art. In a decade, he created about 2,100 artworks, including around 860 oil paintings, most of which date from the last two years of his life.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FVin<PERSON> van <PERSON>-1621856462652.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.726Z", "updated_at": "2023-09-12T23:47:25.726Z", "socials": []}, {"id": 345, "name": "<PERSON>", "is_fictional": false, "birth_date": "1938-12-14T00:00:00.000Z", "birth_location": "Rockford, Illinois, United States", "job": "American writer", "description": "<PERSON> is an American writer, best known as editor of the Whole Earth Catalog. He founded a number of organizations, including The WELL, the Global Business Network, and the Long Now Foundation. He is the author of several books, most recently Whole Earth Discipline: An Ecopragmatist Manifesto.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FStewart Brand-1610065164397.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.692Z", "updated_at": "2023-09-12T23:47:25.692Z", "socials": []}, {"id": 344, "name": "<PERSON><PERSON>", "is_fictional": false, "job": "American humorist", "description": "<PERSON><PERSON> was an American humorist who achieved great popularity for her newspaper column that described suburban home life from the mid-1960s until the late 1990s. <PERSON><PERSON> also published 15 books, most of which became bestsellers.", "image_url": "https://upload.wikimedia.org/wikipedia/en/e/e6/E<PERSON>_<PERSON>eck.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.655Z", "updated_at": "2023-09-12T23:47:25.655Z", "socials": []}, {"id": 343, "name": "<PERSON> (<PERSON>)", "is_fictional": true, "job": "Inhabitant", "description": "<PERSON> is a fictional character in the video game Celeste. She's an old lady who warn <PERSON> about the degerous powers of the Celeste Mountain. She lives at the base of this mountain.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FGranny (Celia)-1610064909991.png?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.622Z", "updated_at": "2023-09-12T23:47:25.622Z", "socials": []}, {"id": 342, "name": "<PERSON>", "is_fictional": false, "job": "American painter", "description": "<PERSON> was an American painter and teacher. He was a leading figure of the Ashcan School of American realism and an organizer of the group known as \"The Eight,\" a loose association of artists who protested the restrictive exhibition practices of the powerful, conservative National Academy of Design.", "image_url": "https://uploads3.wikiart.org/images/robert-henri(1).jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.586Z", "updated_at": "2023-09-12T23:47:25.586Z", "socials": []}, {"id": 341, "name": "<PERSON>", "is_fictional": false, "job": "American professional baseball player", "description": "<PERSON> \"Babe\" <PERSON> was an American professional baseball player whose career in Major League Baseball spanned 22 seasons, from 1914 through 1935.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/1/13/<PERSON>_<PERSON>2.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.550Z", "updated_at": "2023-09-12T23:47:25.550Z", "socials": []}, {"id": 340, "name": "<PERSON>", "is_fictional": false, "job": "American author", "description": "<PERSON> was an American self-help author. He is known best for his book Think and Grow Rich which is among the 10 best selling self-help books of all time. <PERSON>'s works insisted that fervid expectations are essential to improving one's life.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/7/75/<PERSON>_<PERSON>_headshot.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.519Z", "updated_at": "2023-09-12T23:47:25.519Z", "socials": []}, {"id": 339, "name": "<PERSON>", "is_fictional": false, "job": "Founding Father of the United States", "description": "<PERSON> FRS FRSA FRSE was an American polymath and one of the Founding Fathers of the United States. <PERSON> was a leading writer, printer, political philosopher, politician, Freemason, postmaster, scientist, inventor, humorist, civic activist, statesman, and diplomat.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/2/25/<PERSON>_<PERSON>_by_<PERSON>_<PERSON>_1778.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.485Z", "updated_at": "2023-09-12T23:47:25.485Z", "socials": []}, {"id": 338, "name": "<PERSON><PERSON>", "is_fictional": false, "job": "Writer", "description": "<PERSON><PERSON> was a Russian-American writer and philosopher. She is known for her two best-selling novels, The Fountainhead and Atlas Shrugged, and for developing a philosophical system she named Objectivism. Educated in Russia, she moved to the United States in 1926. She had a play produced on Broadway in 1935 and 1936.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAyn Rand-1698680652062.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.451Z", "updated_at": "2023-09-12T23:47:25.451Z", "socials": []}, {"id": 337, "name": "<PERSON>", "is_fictional": true, "description": " <PERSON> is a fictional character played by <PERSON><PERSON><PERSON> in the film \"Malcom & Marie\". In the film, she's <PERSON><PERSON>'s girlfriend, a writer-director.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2<PERSON><PERSON><PERSON>-1614202345120.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.417Z", "updated_at": "2023-09-12T23:47:25.417Z", "socials": []}, {"id": 336, "name": "<PERSON>", "is_fictional": false, "job": "American educator", "description": "<PERSON> was an American educator, author, orator, and adviser to multiple presidents of the United States. Between 1890 and 1915, <PERSON> was the dominant leader in the African American community and of the contemporary black elite.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/1/1b/<PERSON>_<PERSON>_<PERSON>_retouched_flattened-crop.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.384Z", "updated_at": "2023-09-12T23:47:25.384Z", "socials": []}, {"id": 335, "name": "<PERSON> Jr.", "is_fictional": false, "job": "American minister", "description": "<PERSON> was an American Christian minister and activist who became the most visible spokesperson and leader in the Civil Rights Movement from 1955 until his assassination in 1968.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/0/05/<PERSON>_<PERSON>_<PERSON>%2C_Jr..jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.325Z", "updated_at": "2023-09-12T23:47:25.325Z", "socials": []}, {"id": 334, "name": "<PERSON><PERSON>", "is_fictional": false, "job": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON> better known under his stage name <PERSON><PERSON>, was a French comedian and actor famous for his irreverent sense of humor. <PERSON><PERSON> adopted <PERSON><PERSON> as a stage name at age 26, when he began his entertainment career. He became known for his irreverent attitude towards politics and the establishment, and he incorporated this into much of his material. He was one of the first major comedians to regularly use profanities as a source of humor on French television.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FColuche-1695304106136.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.289Z", "updated_at": "2023-09-12T23:47:25.289Z", "socials": []}, {"id": 333, "name": "Usbek&Rica", "is_fictional": false, "description": "Usbek & Rica est un média qui a envie de tester et faire tester le futur à ses lecteurs. D’être utile au plus grand nombre, sans quoi nous passerions à côté de notre mission.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.254Z", "updated_at": "2023-09-12T23:47:25.254Z", "socials": []}, {"id": 332, "name": "<PERSON>", "is_fictional": true, "job": "Mathematics Ph.D.", "description": "<PERSON> is a fictional character played by <PERSON> in the Tv series The Queen's Gambit. In the show, she's <PERSON>'s deceased mother who earned a Ph.D. in mathematics at Cornell University before experiencing a downward spiral in her mental health.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAlice Harmon-1607037282102.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.219Z", "updated_at": "2023-09-12T23:47:25.219Z", "socials": []}, {"id": 331, "name": "<PERSON>", "is_fictional": false, "job": "English novelist", "description": "<PERSON>, also known as <PERSON> and later as <PERSON>, was an English satirical novelist, diarist and playwright. Born in Lynn Regis, now King's Lynn, England, on 13 June 1752, to the musician Dr <PERSON> and his first wife, <PERSON>, she was the third of her mother's six children.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/6/60/<PERSON>_<PERSON>%27Arblay_%28%27<PERSON><PERSON><PERSON>_<PERSON>%27%29_by_<PERSON>_<PERSON>_<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.185Z", "updated_at": "2023-09-12T23:47:25.185Z", "socials": []}, {"id": 330, "name": "<PERSON>", "is_fictional": false, "job": "French-Polish physicist", "description": "<PERSON>, born <PERSON>, was a Polish and naturalized-French physicist and chemist who conducted pioneering research on radioactivity.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/7/7e/<PERSON>_<PERSON>_c1920.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.145Z", "updated_at": "2023-09-12T23:47:25.145Z", "socials": []}, {"id": 329, "name": "<PERSON>", "is_fictional": true, "job": "Owner of a publishing company", "description": "<PERSON> is a fictional character played by <PERSON> in the film \"Vanilla Sky\". <PERSON> has inherited of a large publishing company from his father. He's introduced to <PERSON> by his best friend, <PERSON>, during a party.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDavid Aames-1614728248704.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.111Z", "updated_at": "2023-09-12T23:47:25.111Z", "socials": []}, {"id": 328, "name": "<PERSON> (All the Bright Places)", "is_fictional": false, "job": "Student", "description": "<PERSON> is a fictional character in the movie All the Bright Places. His life changes when he meets <PERSON> in an unconventional way. As they struggle with the emotional and physical scars of their past, they come together through a school project, discovering that even the smallest places and moments can mean something.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FFinch (All the Bright Places)-1699442415041.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.080Z", "updated_at": "2023-09-12T23:47:25.080Z", "socials": []}, {"id": 327, "name": "<PERSON>", "is_fictional": false, "job": "American basketball player", "description": "<PERSON>, also known by his initials <PERSON><PERSON>, is an American former professional basketball player and the principal owner of the Charlotte Hornets of the National Basketball Association. He played 15 seasons in the NBA, winning six championships with the Chicago Bulls.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/a/ae/<PERSON>_<PERSON>_in_2014.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.046Z", "updated_at": "2023-09-12T23:47:25.046Z", "socials": []}, {"id": 326, "name": "<PERSON>", "is_fictional": false, "job": "CEO of Berkshire Hathaway", "description": "<PERSON> is an American investor, business tycoon, philanthropist, and the chairman and CEO of Berkshire Hathaway. He is considered one of the most successful investors in the world and has a net worth of US$78.9 billion as of August 2020, making him the world's seventh-wealthiest person.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FWarren_Buffett.jpg?alt=media&token=e79b7069-9ee0-426a-8fb1-07b2192bc38d", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:25.007Z", "updated_at": "2023-09-12T23:47:25.007Z", "socials": []}, {"id": 325, "name": "<PERSON>", "is_fictional": false, "job": "Singer-songwriter", "description": "Description<PERSON><PERSON><PERSON> was an English singer, songwriter and peace activist who gained worldwide fame as the founder, co-lead vocalist, and rhythm guitarist of the Beatles. His songwriting partnership with <PERSON> remains the most successful in history.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/8/85/<PERSON>_<PERSON>_1969_%28cropped%29.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.973Z", "updated_at": "2023-09-12T23:47:24.973Z", "socials": []}, {"id": 324, "name": "<PERSON>", "is_fictional": false, "job": "Former First Lady of the United States", "description": "<PERSON> was an American political figure, diplomat and activist. She served as the First Lady of the United States from March 4, 1933, to April 12, 1945, during her husband President <PERSON>'s four terms in office, making her the longest-serving First Lady of the United States.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/2/22/<PERSON>_<PERSON>_portrait_1933.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.930Z", "updated_at": "2023-09-12T23:47:24.930Z", "socials": []}, {"id": 323, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "1912-07-10T00:00:00.000Z", "birth_location": "Titiel, Serbia", "death_date": "1981-08-17T22:00:00.000Z", "job": "Double agent", "description": "<PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON> was a Serbian triple agent who served as part of the MI6 and Abwehr during World War II, and passed off disinformation to Germany as part of the Double-Cross System and working also as agent for the Yugoslav government-in-exile in London. ", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FD<PERSON><PERSON><PERSON>-1610663555375.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.897Z", "updated_at": "2023-09-12T23:47:24.897Z", "socials": []}, {"id": 322, "name": "Cloud", "is_fictional": true, "job": "Waitress", "description": "<PERSON>, is <PERSON><PERSON>'s free-spirited drug-addicted single mother. She's not really present for <PERSON><PERSON>. The fictional character is played by <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCloud-1619991291616.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.861Z", "updated_at": "2023-09-12T23:47:24.861Z", "socials": []}, {"id": 321, "name": "<PERSON>", "is_fictional": false, "job": "Screenwriter", "description": "<PERSON> is an American screenwriter, producer and director best known for the American television series Lost, for which he made the Time magazine list of the 100 most influential people in the world in 2010.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fcarlton-cuse.jpg?alt=media&token=a6ebc83d-516e-4359-86dc-ae74f0e00881", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.677Z", "updated_at": "2023-09-12T23:47:24.677Z", "socials": []}, {"id": 319, "name": "Gwynplaine", "is_fictional": true, "birth_date": "2021-03-18T15:21:30.808Z", "birth_location": "France", "death_date": "2021-03-18T15:21:30.808Z", "job": "Fictional character", "description": "<PERSON><PERSON><PERSON><PERSON> is a horrifyingly disfigured man who was surgically deformed with a grotesque, \"hideous\" smile. <PERSON><PERSON><PERSON><PERSON> was featured some of the earliest monster movies in history, such as The Man Who Laughs made in 1909 in France by the Pathé film company and produced by <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FGwynplaine-1616080850595.png?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.641Z", "updated_at": "2023-09-12T23:47:24.641Z", "socials": []}, {"id": 318, "name": "<PERSON>", "is_fictional": false, "job": "American philosopher", "description": "<PERSON> is an American pioneer of information technology, philosopher and sociologist. He coined the terms hypertext and hypermedia in 1963 and published them in 1965. <PERSON> coined the terms transclusion, virtuality, and intertwingularity, and teledildonics.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FTed_Nelson.jpg?alt=media&token=a423d270-6c43-4197-a39b-b3b05e9e8b89", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.609Z", "updated_at": "2023-09-12T23:47:24.609Z", "socials": []}, {"id": 317, "name": "<PERSON>", "is_fictional": false, "job": "Content producer", "description": "<PERSON> (<PERSON><PERSON><PERSON>) <PERSON><PERSON> is a YouTuber, podcaster, copywritter, writter specialized in web marketing. He has a psychological take on the world which helps to better understand human behaviour.", "image_url": "https://pbs.twimg.com/profile_images/949856203256995842/esyNv1yC_400x400.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.578Z", "updated_at": "2023-09-12T23:47:24.578Z", "socials": []}, {"id": 316, "name": "<PERSON>", "is_fictional": false, "job": "Philosopher", "description": "<PERSON> was a German philosopher, cultural critic, composer, poet, philologist, and Latin and Greek scholar whose work has exerted a profound influence on modern intellectual history. He began his career as a classical philologist before turning to philosophy.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/1/1b/Nietzsche187a.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.544Z", "updated_at": "2023-09-12T23:47:24.544Z", "socials": []}, {"id": 315, "name": "<PERSON>", "is_fictional": false, "job": "Churchman", "description": "<PERSON> was an English churchman and historian. He is now remembered for his writings, particularly his Worthies of England, published in 1662 after his death. He was a prolific author, and one of the first English writers able to live by his pen.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/8/88/<PERSON>_<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.512Z", "updated_at": "2023-09-12T23:47:24.512Z", "socials": []}, {"id": 314, "name": "<PERSON>", "is_fictional": false, "job": "American athlete", "description": "<PERSON> \"<PERSON>\" <PERSON> was an American track and field athlete and four-time gold medalist in the 1936 Olympic Games. <PERSON> specialized in the sprints and the long jump, and was recognized in his lifetime as \"perhaps the greatest and most famous athlete in track and field history\".", "image_url": "https://upload.wikimedia.org/wikipedia/commons/1/19/<PERSON>_<PERSON>_1936.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.481Z", "updated_at": "2023-09-12T23:47:24.481Z", "socials": []}, {"id": 313, "name": "<PERSON>", "is_fictional": false, "job": "Waiter", "description": "<PERSON> is a fictional character<PERSON> played by <PERSON> in the TV series Warrior Nun.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAlberto%20Ruano.jpg?alt=media&token=67191ae6-8046-4ed7-86e7-48dc49c9aacc", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.448Z", "updated_at": "2023-09-12T23:47:24.448Z", "socials": []}, {"id": 311, "name": "<PERSON>", "is_fictional": false, "job": "<PERSON><PERSON>", "description": "<PERSON> was an influential British Protestant preacher at the turn of the nineteenth to the twentieth century who wrote books on topics related to Christian living. He has been called \"The greatest preacher in the English speaking world.\"", "image_url": "https://upload.wikimedia.org/wikipedia/commons/f/f8/Rev_<PERSON>_<PERSON>_Congegationalist.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.415Z", "updated_at": "2023-09-12T23:47:24.415Z", "socials": []}, {"id": 310, "name": "<PERSON>", "is_fictional": false, "job": "Fictional character", "description": "Captain <PERSON> is a fictional character and the main protagonist of the Pirates of the Caribbean film series. The character was created by screenwr\niters <PERSON> and <PERSON> and is portrayed by <PERSON>.", "image_url": "https://vignette.wikia.nocookie.net/pirates/images/e/ea/DMTNT_<PERSON>_Sparrow_cropped.png/revision/latest?cb=20170507052033", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.373Z", "updated_at": "2023-09-12T23:47:24.373Z", "socials": []}, {"id": 309, "name": "<PERSON>", "is_fictional": false, "job": "American author", "description": "<PERSON> was an American radio speaker and author, dealing mostly with the subjects of human character development, motivation, and meaningful existence. He was the voice during the early 1950s of <PERSON>, the hero of a radio adventure series, and was a WGN radio program host from 1950 to 1956.", "image_url": "https://i.pinimg.com/originals/07/4e/64/074e641ea6d2efbe48b69285826b328f.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.320Z", "updated_at": "2023-09-12T23:47:24.320Z", "socials": []}, {"id": 308, "name": "<PERSON>", "is_fictional": true, "job": "Gardener", "description": "<PERSON> is a fictional character in the TV series The Haunting of Bly Manor played by <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Famelia_eve.jpg?alt=media&token=80279bbc-f222-4927-8932-553bd667819a", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.287Z", "updated_at": "2023-09-12T23:47:24.287Z", "socials": []}, {"id": 307, "name": "Dr. <PERSON>", "is_fictional": true, "birth_date": "2021-04-20T23:57:12.068Z", "death_date": "2021-04-20T23:57:12.068Z", "job": "Psychiatrist", "description": "Dr. <PERSON> is a weak willed psychiatrist whose job is to write a report about <PERSON>'s sanity, her memory, and what was her role in the murder. The character is played by the English actor <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDr. <PERSON>-1619986445393.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.251Z", "updated_at": "2023-09-12T23:47:24.251Z", "socials": []}, {"id": 305, "name": "Seneca", "is_fictional": false, "description": "Roman Stoic philosopher, statesman, dramatist, and—in one work—satirist of the Silver Age of Latin literature", "image_url": "https://upload.wikimedia.org/wikipedia/commons/4/44/<PERSON><PERSON>_herma_of_Socrates_and_<PERSON>_Antikensammlung_Berlin_07.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.217Z", "updated_at": "2023-09-12T23:47:24.217Z", "socials": []}, {"id": 303, "name": "<PERSON>'s mother", "is_fictional": true, "job": "Midwife", "description": "<PERSON>'s mother has difficulties to communicate with her daughter but tries to help her as she can.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.184Z", "updated_at": "2023-09-12T23:47:24.184Z", "socials": []}, {"id": 302, "name": "<PERSON>", "is_fictional": false, "job": "Private Investigator", "description": "<PERSON> is a fictional character created by American author <PERSON>. She has been portrayed on screen by <PERSON><PERSON> in the television adaptation of the Hodges trilogy and by <PERSON> in the miniseries adaptation of The Outside", "image_url": "https://upload.wikimedia.org/wikipedia/commons/c/cb/Cynthia_Erivo_%2882023%29_%28cropped%29.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.151Z", "updated_at": "2023-09-12T23:47:24.151Z", "socials": []}, {"id": 301, "name": "<PERSON>", "is_fictional": false, "job": " American columnist", "description": "<PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON>, better known by the pen name <PERSON>, was an American advice columnist and eventually a nationwide media celebrity. She began writing the \"Ask Ann Landers\" column in 1955 and continued for 47 years, by which time its readership was 90 million people.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/1/1b/<PERSON>_<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.115Z", "updated_at": "2023-09-12T23:47:24.115Z", "socials": []}, {"id": 300, "name": "<PERSON>", "is_fictional": false, "job": "Athenian philosopher", "description": "<PERSON> was an Athenian philosopher during the Classical period in Ancient Greece, founder of the Platonist school of thought, and the Academy, the first institution of higher learning in the Western world.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/8/88/<PERSON>_<PERSON><PERSON>_Musei_Capitolini_MC1377.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.080Z", "updated_at": "2023-09-12T23:47:24.080Z", "socials": []}, {"id": 299, "name": "<PERSON>", "is_fictional": false, "job": "Life Coach", "description": "He is a life coach, success and happiness coach. He want to discover what is holding people back from their potential and give them the tools to overcome those obstacles.", "image_url": "https://ksr-ugc.imgix.net/assets/024/392/146/db1e888fa7711696c6cd1e77d41d74a0_original.jpg?ixlib=rb-2.1.0&w=220&h=220&fit=crop&v=1552420709&auto=format&frame=1&q=92&s=0a52dedd056f3163945e58da02187ff0", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.047Z", "updated_at": "2023-09-12T23:47:24.047Z", "socials": []}, {"id": 298, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "job": "Italian poet", "description": "<PERSON><PERSON><PERSON> was an Italian poet, novelist, literary critic and translator. He is widely considered among the major authors of the 20th century in his home country.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/a/a6/<PERSON><PERSON><PERSON>_pavese.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:24.011Z", "updated_at": "2023-09-12T23:47:24.011Z", "socials": []}, {"id": 297, "name": "<PERSON><PERSON><PERSON>", "is_fictional": true, "job": "Archeologist", "description": "<PERSON><PERSON><PERSON> is a fictional character played by <PERSON><PERSON><PERSON> in the TV series \"At<PERSON><PERSON> (The Gift)\". In the show, he's an archeologist and will work with <PERSON><PERSON><PERSON> on his discovery.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FErhan-1604423458376.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.977Z", "updated_at": "2023-09-12T23:47:23.977Z", "socials": []}, {"id": 296, "name": "Air France", "is_fictional": false, "job": "Air carrier", "description": "Air France, stylized as AIRFRANCE, is the French flag carrier headquartered in Tremblay-en-France. It is a subsidiary of the Air France–KLM Group and a founding member of the SkyTeam global airline alliance.", "image_url": "https://images.ctfassets.net/biom0eqyyi6b/5Gh9h30pryYEkuwYM6eYQC/11bb4a49f703e7b96c306c27e0255797/AirFrance.png", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.944Z", "updated_at": "2023-09-12T23:47:23.944Z", "socials": []}, {"id": 294, "name": "<PERSON>", "is_fictional": false, "birth_date": "1856-07-27T23:50:39.000Z", "birth_location": "Dublin, Ireland", "death_date": "1950-11-01T23:00:00.000Z", "job": "Playwright", "description": "<PERSON>, known at his insistence simply as <PERSON>, was an Irish playwright, critic, polemicist and political activist. His influence on Western theatre, culture and politics extended from the 1880s to his death and beyond.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON><PERSON>-1604325062555.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.911Z", "updated_at": "2023-09-12T23:47:23.911Z", "socials": []}, {"id": 293, "name": "<PERSON>", "is_fictional": false, "job": "Poet", "description": "<PERSON> was a French poet, playwright, short story writer, novelist, and art critic of Polish-Belarusian descent. <PERSON><PERSON><PERSON><PERSON> is considered one of the foremost poets of the early 20th century, as well as one of the most impassioned defenders of Cubism and a forefather of Surrealism.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/6/68/Guillaume_Apollinaire_foto.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.876Z", "updated_at": "2023-09-12T23:47:23.876Z", "socials": []}, {"id": 291, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "job": "Social Networking Platform", "description": "Dribbble is a self-promotion and social networking platform for digital designers and creatives. It serves as a design portfolio platform, jobs and recruiting site and is one of the largest platforms for designers to share their work online. The company is fully remote with no headquarters.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fdribbble-ball-icon.png?alt=media&token=9cf965c9-da27-4659-ac58-46514d34ea55", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.829Z", "updated_at": "2023-09-12T23:47:23.829Z", "socials": []}, {"id": 289, "name": "ALT 236", "is_fictional": false, "job": "Content creator", "description": "<PERSON><PERSON> <PERSON> is a french content producer on YouTube and Twitch. He's also a music artist.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Falt_236.jpeg?alt=media&token=19786896-2a2b-42de-bb45-aaec3bb9c7cd", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.792Z", "updated_at": "2023-09-12T23:47:23.792Z", "socials": []}, {"id": 288, "name": "<PERSON>", "is_fictional": true, "job": "Technical support", "description": "<PERSON> is a fictional character played by <PERSON> in the film \"Vanilla Sky\". He plays as a technical support to <PERSON> and answers some of his questions.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FEdmund <PERSON>-1614724843146.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.755Z", "updated_at": "2023-09-12T23:47:23.755Z", "socials": []}, {"id": 286, "name": "Mrs. <PERSON>", "is_fictional": true, "job": "Companion", "description": "Mrs <PERSON> is a fictional character played by <PERSON> in the film <PERSON>. In the film, she's a young woman in her 20's working as a companion to Mrs. <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMrs. de Winter-1607785191383.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.716Z", "updated_at": "2023-09-12T23:47:23.716Z", "socials": []}, {"id": 284, "name": "<PERSON>", "is_fictional": false, "job": "Philosopher", "description": "<PERSON> was a German philosopher and an important figure of German idealism. He achieved recognition in his day and—while primarily influential in the continental tradition of philosophy—has become increasingly influential in the analytic tradition as well.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/c/ce/<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON><PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.677Z", "updated_at": "2023-09-12T23:47:23.677Z", "socials": []}, {"id": 281, "name": "<PERSON>", "is_fictional": false, "job": "Actor", "description": "<PERSON> is fictional character played by <PERSON> in the movie <PERSON><PERSON>. In the movie he's a known actor who has a role in a Broadway adaptation.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/e/ec/<PERSON>_<PERSON>_2012.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.618Z", "updated_at": "2023-09-12T23:47:23.618Z", "socials": []}, {"id": 279, "name": "<PERSON> \"<PERSON>\" <PERSON> (fictional)", "is_fictional": false, "job": "Studio executive", "description": "<PERSON> \"<PERSON>\" <PERSON> is a fictional character played by <PERSON> in the TV miniseries \"Hollywood\". He plays a studio executive at Ace Studios.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/a/af/Director_<PERSON>_<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.583Z", "updated_at": "2023-09-12T23:47:23.583Z", "socials": []}, {"id": 278, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "job": "Founder of IKEA", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> was a Swedish business magnate best known for founding IKEA, a multinational retail company specialising in furniture. He lived in Switzerland from 1976 to 2014. ", "image_url": "https://upload.wikimedia.org/wikipedia/commons/c/c4/Ing<PERSON>_Kamprad_Haparanda_June_2010.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.550Z", "updated_at": "2023-09-12T23:47:23.550Z", "socials": []}, {"id": 277, "name": "<PERSON>", "is_fictional": false, "description": "<PERSON> was an American science-fiction author, aeronautical engineer, and retired Naval officer.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/b/bf/<PERSON><PERSON><PERSON>-<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.517Z", "updated_at": "2023-09-12T23:47:23.517Z", "socials": []}, {"id": 276, "name": "<PERSON>", "is_fictional": false, "job": "Content producer", "description": "<PERSON> is a french video producer who created the YouTube channel \"Hygiène Mentale\". He teachs about the development of critical mind and the scientific method. He also works on paranormal events.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/c/c3/<PERSON>_<PERSON>.png", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.484Z", "updated_at": "2023-09-12T23:47:23.484Z", "socials": []}, {"id": 274, "name": "<PERSON>", "is_fictional": false, "job": "26th U.S. President", "description": "<PERSON> was an American statesman, politician, conservationist, naturalist, and writer who served as the 26th president of the United States from 1901 to 1909. He served as the 25th vice president from March to September 1901 and as the 33rd governor of New York from 1899 to 1900.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/1/1c/President_<PERSON>_-_<PERSON>h_Bros.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.450Z", "updated_at": "2023-09-12T23:47:23.450Z", "socials": []}, {"id": 273, "name": "<PERSON><PERSON><PERSON>", "is_fictional": true, "job": "<PERSON>", "description": "<PERSON><PERSON><PERSON> is a fictional character played by <PERSON><PERSON> in the TV series \"<PERSON><PERSON><PERSON> (The Gift)\". In the show, she's a painter who has strange visions and is fascinated with a particular symbol. She will start a travel in search of the truth.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAtiye-1604423792750.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.415Z", "updated_at": "2023-09-12T23:47:23.415Z", "socials": []}, {"id": 272, "name": "<PERSON>", "is_fictional": false, "job": "British author", "description": "<PERSON> was an English writer, philosopher and novelist. He also wrote widely on true crime, mysticism and the paranormal, eventually writing more than a hundred books.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/c/c1/<PERSON>_<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.379Z", "updated_at": "2023-09-12T23:47:23.379Z", "socials": []}, {"id": 271, "name": "<PERSON>", "is_fictional": false, "job": "American cartoonist", "description": "<PERSON> is an American former cartoonist and the author of the comic strip <PERSON>, which was syndicated from 1985 to 1995.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FB<PERSON>-1689879436847.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.320Z", "updated_at": "2023-09-12T23:47:23.320Z", "socials": []}, {"id": 270, "name": "<PERSON>", "is_fictional": true, "job": "Android bartender", "description": "<PERSON> is a fictional character played by <PERSON> in the film \"Passengers\". In the film, he's an android bartender on the Avalon. He has conversations with awaken colonist and crew members and serves them cocktails.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FArthur-1615231787755.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.287Z", "updated_at": "2023-09-12T23:47:23.287Z", "socials": []}, {"id": 268, "name": "<PERSON><PERSON>", "is_fictional": false, "description": "Technology industry leader and expert, having had 5+ years’ experience working and managing across product, engineering, editorial, revenue, and brand strategy at top global companies.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FA<PERSON><PERSON>-1698071800564.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.250Z", "updated_at": "2023-09-12T23:47:23.250Z", "socials": []}, {"id": 267, "name": "Protocol Labs", "is_fictional": false, "job": "Company", "description": "Protocol Labs is a research, development, and deployment institution for improving Internet technology. We create software systems that tackle significant challenges. We aim to solve them with new technology breakthroughs, great user experience design, and an open-source approach to creation.", "image_url": "https://lever-client-logos.s3.amazonaws.com/aaec006c-b5ef-48f1-a413-98889fda90d3-1488428386171.png", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.217Z", "updated_at": "2023-09-12T23:47:23.217Z", "socials": []}, {"id": 266, "name": "<PERSON>", "is_fictional": false, "job": "Security Guard", "description": "<PERSON> is a fictional character played by <PERSON> in the TV miniseries The Outsider. He's a security guard helping <PERSON> on a case.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDerek-Cecil.jpg?alt=media&token=fc1f4998-b409-470a-9849-4b4a8d9de969", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.180Z", "updated_at": "2023-09-12T23:47:23.180Z", "socials": []}, {"id": 265, "name": "<PERSON>", "is_fictional": true, "description": "<PERSON> is a fictional character played by <PERSON><PERSON> in the TV series \"Euphoria\". In the show, he's a man in recovery from substance use disorder who often speaks at <PERSON>'s Narcotics Anonymous meetings.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAli-1604582753274.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.145Z", "updated_at": "2023-09-12T23:47:23.145Z", "socials": []}, {"id": 264, "name": "<PERSON>", "is_fictional": false, "job": "American industrialist", "description": "<PERSON> was an American industrialist and a business magnate, the founder of the Ford Motor Company, and the sponsor of the development of the assembly line technique of mass production.<", "image_url": "https://upload.wikimedia.org/wikipedia/commons/1/18/<PERSON>_ford_1919.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.111Z", "updated_at": "2023-09-12T23:47:23.111Z", "socials": []}, {"id": 262, "name": "<PERSON>", "is_fictional": true, "birth_date": "1828-06-30T22:00:00.000Z", "birth_location": "Ulster, Ireland", "death_date": "1872-12-31T23:00:00.000Z", "job": "Maid", "description": "<PERSON> (c. July 1828 – after c. 1873) was an Irish-Canadian maid who was involved in the 1843 murder of her employer <PERSON> and his housekeeper, <PERSON>, in Richmond Hill, Ontario. Her conviction for the murder of <PERSON><PERSON><PERSON> was controversial and sparked much debate about whether <PERSON> was actually instrumental in the murder or merely an unwitting accessory. <PERSON> was the subject of <PERSON>'s historical fiction novel <PERSON><PERSON> and its adaptations in other media. ", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FGrace Marks-1619983110043.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.072Z", "updated_at": "2023-09-12T23:47:23.072Z", "socials": []}, {"id": 260, "name": "Ratched", "is_fictional": true, "job": "Nurse", "description": "Nurse <PERSON><PERSON> is a fictional character played by <PERSON> in the TV series \"Ratched\". In the show, she's a nurse hired by Dr. <PERSON> to work at Lucia State Hospital. But she has other motive.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FRatched-1604526838559.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.039Z", "updated_at": "2023-09-12T23:47:23.039Z", "socials": []}, {"id": 259, "name": "<PERSON>", "is_fictional": false, "job": "Science writer", "description": "Sir <PERSON>RAS was a British science fiction writer, science writer and futurist, inventor, undersea explorer, and television series host. He co-wrote the screenplay for the 1968 film 2001: A Space Odyssey, one of the most influential films of all time.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/6/62/<PERSON>_<PERSON>_<PERSON>_1965.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:23.007Z", "updated_at": "2023-09-12T23:47:23.007Z", "socials": []}, {"id": 258, "name": "Florence Nightingale", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:22.958Z", "updated_at": "2023-09-12T23:47:22.958Z", "socials": []}, {"id": 257, "name": "<PERSON>", "is_fictional": true, "job": "<PERSON>", "description": "<PERSON> is a fictional character played by <PERSON> in the TV series \"La Révolution\". In the show, he's <PERSON>'s father, and has the highest nobility title.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FGuy de Montargis-1604356803281.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:22.923Z", "updated_at": "2023-09-12T23:47:22.923Z", "socials": []}, {"id": 256, "name": "<PERSON>", "is_fictional": false, "job": "Film director", "description": "<PERSON> is a professional filmmaker and Artistic Director of The Atwater Playhouse.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON><PERSON>-1699706544658.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:22.888Z", "updated_at": "2023-09-12T23:47:22.888Z", "socials": []}, {"id": 255, "name": "<PERSON>", "is_fictional": false, "job": "American aviator", "description": "<PERSON> was an American aviation pioneer and author. <PERSON><PERSON><PERSON> was the first female aviator to fly solo across the Atlantic Ocean. She set many other records, wrote best-selling books about her flying experiences, and was instrumental in the formation of The Ninety-Nines, an organization for female pilots.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:22.853Z", "updated_at": "2023-09-12T23:47:22.853Z", "socials": []}, {"id": 254, "name": "<PERSON>", "is_fictional": false, "description": "<PERSON> is a British-American author, motivational speaker and organizational consultant. He is the author of five books, including Start With Why.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:22.820Z", "updated_at": "2023-09-12T23:47:22.820Z", "socials": []}, {"id": 253, "name": "<PERSON>", "is_fictional": false, "description": "Hong Kong-American actor, director, martial artist, martial arts instructor, and philosopher.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:22.786Z", "updated_at": "2023-09-12T23:47:22.786Z", "socials": []}, {"id": 252, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "job": "French playwright", "description": "<PERSON><PERSON><PERSON>, known by his stage name <PERSON><PERSON><PERSON>, was a French playwright, actor and poet, widely regarded as one of the greatest writers in the French language and universal literature. His extant works include comedies, farces, tragicomedies, comédie-ballets and more.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/d/d1/<PERSON>_<PERSON>_-_Portrait_de_<PERSON>-<PERSON>_<PERSON>_dit_<PERSON>li%C3%A8re_%281622-1673%29_-_Google_Art_Project_%28cropped%29.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:22.750Z", "updated_at": "2023-09-12T23:47:22.750Z", "socials": []}, {"id": 250, "name": "<PERSON><PERSON><PERSON>E<PERSON>ina”", "is_fictional": false, "birth_date": "2021-04-09T08:32:36.286Z", "death_date": "2021-04-09T08:32:36.286Z", "job": "Editor-in-chief, Podcaster", "description": "<PERSON><PERSON><PERSON><PERSON>' Chef @KissMyGeek - Podcasteuse @SuperGamerside et RDV <PERSON>ux - Chef de Projet IRL.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMaïté “Eskarina”-1619988878467.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:22.716Z", "updated_at": "2023-09-12T23:47:22.716Z", "socials": []}, {"id": 249, "name": "<PERSON>", "is_fictional": false, "job": "Inventor", "description": "<PERSON> was a Serbian-American inventor, electrical engineer, mechanical engineer, and futurist who is best known\n for his contributions to the design of the modern alternating current electricity supply system.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/d/d4/N.Tesla.JPG", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:22.682Z", "updated_at": "2023-09-12T23:47:22.682Z", "socials": []}, {"id": 248, "name": "Latin Proverb", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:22.650Z", "updated_at": "2023-09-12T23:47:22.650Z", "socials": []}, {"id": 247, "name": "<PERSON>", "is_fictional": true, "job": "Teenager", "description": "<PERSON> is a fictional character created by American author <PERSON> in his first published 1974 horror novel, <PERSON>. She is a popular teenage girl dating <PERSON>. After tormenting <PERSON> in the locker room, <PERSON> begins to feel remorse for her actions.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F <PERSON>-1615244670070.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:22.616Z", "updated_at": "2023-09-12T23:47:22.616Z", "socials": []}, {"id": 246, "name": "Inspector <PERSON>", "is_fictional": false, "job": "Polic Inspector", "description": "Fictional character in the TV series '<PERSON>'. He's charged to investigate on the deaths of some citizens.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:22.583Z", "updated_at": "2023-09-12T23:47:22.583Z", "socials": []}, {"id": 245, "name": "Anonymous", "is_fictional": false, "job": "Unkown", "description": "A anonymous person who doesn't want to be known.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:22.546Z", "updated_at": "2023-09-12T23:47:22.546Z", "socials": []}, {"id": 244, "name": "<PERSON>", "is_fictional": false, "job": "Convicted murderer", "description": "<PERSON> is a convicted murderer who committed his crimes in the U.S. state of Oregon. Originating from Ypsilanti Township, Michigan, he married <PERSON> at age 19 and they had three children together. He and his family often encountered financial difficulties due to his reckless spending habits.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FChristian%20Longo.png?alt=media&token=9d709a6f-5c77-4e48-b997-44abc4f2547c", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:22.512Z", "updated_at": "2023-09-12T23:47:22.512Z", "socials": []}, {"id": 243, "name": "Anatole France", "is_fictional": false, "job": "French poet", "description": "<PERSON><PERSON><PERSON> was a French poet, journalist, and novelist with several best-sellers. Ironic and skeptical, he was considered in his day the ideal French man of letters.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/c/c1/Anatole_France_young_years.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:22.477Z", "updated_at": "2023-09-12T23:47:22.477Z", "socials": []}, {"id": 242, "name": "<PERSON>", "is_fictional": false, "job": "Actor", "description": "<PERSON> is an English actor, comedian and writer. <PERSON> and <PERSON> are the comic double act <PERSON> and <PERSON>, who starred in A Bit of Fry & Laurie and <PERSON><PERSON> and <PERSON><PERSON>.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/3/38/<PERSON>_<PERSON>_June_2016.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:22.442Z", "updated_at": "2023-09-12T23:47:22.442Z", "socials": []}, {"id": 241, "name": "<PERSON> & <PERSON><PERSON><PERSON>", "is_fictional": false, "description": "<PERSON>'s research focuses on equity in education and how fields become segregated. <PERSON><PERSON><PERSON>'s work empowers students to use computer programming to design games, electronic textiles, and biological applications to promote coding, crafting, and creativity across grades K–16.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:22.404Z", "updated_at": "2023-09-12T23:47:22.404Z", "socials": []}, {"id": 240, "name": "<PERSON>", "is_fictional": false, "description": "American theoretical physicist, known for his work in the path integral formulation of quantum mechanics, the theory of quantum electrodynamics, and the physics of the superfluidity of supercooled liquid helium, as well as in particle physics for which he proposed the parton model.", "image_url": "https://upload.wikimedia.org/wikipedia/en/4/42/<PERSON>_<PERSON>_<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:22.370Z", "updated_at": "2023-09-12T23:47:22.370Z", "socials": []}, {"id": 239, "name": "<PERSON>", "is_fictional": false, "job": "American author", "description": "<PERSON> \"<PERSON>\" <PERSON> is an American motivational speaker, author, former radio DJ, and former television host. He was a member of the Ohio House of Representatives from 1976 to 1981. As a motivational speaker, he uses the catch phrase \"it's possible!\" to encourage people to follow their dreams.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/9/91/<PERSON>_<PERSON>_<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:22.337Z", "updated_at": "2023-09-12T23:47:22.337Z", "socials": []}, {"id": 238, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "job": "French mathematician", "description": "<PERSON><PERSON><PERSON> was a French mathematician, physicist, inventor, writer and Catholic theologian. He was a child prodigy who was educated by his father, a tax collector in Rouen.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/9/98/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>.JPG", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:22.291Z", "updated_at": "2023-09-12T23:47:22.291Z", "socials": []}, {"id": 237, "name": "Albert Einstein & Leopold Infeld", "is_fictional": false, "description": "<PERSON> was a German-born theoretical physicist who developed the theory of relativity, one of the two pillars of modern physics. <PERSON> was a Polish physicist who worked mainly in Poland and Canada.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:22.239Z", "updated_at": "2023-09-12T23:47:22.239Z", "socials": []}, {"id": 236, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "job": "Philosopher", "description": "<PERSON> ben <PERSON>, commonly known as <PERSON><PERSON><PERSON> and also referred to by the acronym <PERSON><PERSON><PERSON>, was a medieval Sephardic Jewish philosopher who became one of the most prolific and influential Torah scholars of the Middle Ages. In his time, he was also a preeminent astronomer and physician.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/0/07/Maimonides-2.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:22.205Z", "updated_at": "2023-09-12T23:47:22.205Z", "socials": []}, {"id": 234, "name": "<PERSON><PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "1769-08-14T23:50:39.000Z", "birth_location": "Ajaccio, France", "death_date": "1821-05-04T23:50:39.000Z", "job": "Statesman", "description": "<PERSON> was a French statesman and military leader who led many successful campaigns during the French Revolution and the French Revolutionary Wars, and was Emperor of the French from 1804 until 1814 and again briefly in 1815 during the Hundred Days.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FNapolé<PERSON> Bonaparte-1604350767998.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:22.172Z", "updated_at": "2023-09-12T23:47:22.172Z", "socials": []}, {"id": 232, "name": "<PERSON>", "is_fictional": false, "job": "Businessman", "description": "<PERSON> is a fictional character in the 2019 TV series Watchmen, played by <PERSON>. He's a former businessman and the vigilante <PERSON><PERSON><PERSON><PERSON>, the \"smartest man in the world\". He is now living as an aristocratic lord of a country manor that turned out to be on Europa.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FJeremy_Irons.jpg?alt=media&token=55692e76-3a4d-4e3f-bbd7-679703b0c810", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:21.134Z", "updated_at": "2023-09-12T23:47:21.134Z", "socials": []}, {"id": 231, "name": "<PERSON><PERSON>", "is_fictional": false, "job": "American-Cuban-French essayist", "description": "<PERSON>, known professionally as <PERSON><PERSON><PERSON> was a French-Cuban American diarist, essayist, novelist, and writer of short stories and erotica. Born to Cuban parents in France, <PERSON><PERSON> was the daughter of composer <PERSON><PERSON><PERSON><PERSON> and <PERSON>, a classically trained singer.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/8/87/<PERSON>_<PERSON>_and_<PERSON>%C3%AF<PERSON>_<PERSON>n_at_daliel%27s_bookstore_in_Berkeley%2C_CA%2C_1946.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:21.095Z", "updated_at": "2023-09-12T23:47:21.095Z", "socials": []}, {"id": 230, "name": "<PERSON><PERSON><PERSON>", "is_fictional": true, "job": "<PERSON>", "description": "<PERSON><PERSON><PERSON> is a fictional character played by <PERSON> in the TV series \"La Révolution\". In the show, he's <PERSON>' son and has a terrible disease. He's impulsive and seek his father admiration.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON><PERSON> Montargis-1604357266612.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:21.061Z", "updated_at": "2023-09-12T23:47:21.061Z", "socials": []}, {"id": 229, "name": "<PERSON><PERSON><PERSON><PERSON>", "is_fictional": false, "job": "Video producer", "description": "<PERSON><PERSON><PERSON><PERSON>, plus connu sous le pseudonyme Seb la Frite ou SEB, est un vidéaste français. En août 2018, il est le treizième youtubeur francophone le plus suivi, avec plus de 4 millions d'abonnés pour environ 468 millions de vues.", "image_url": "https://yt3.ggpht.com/a/AGF-l7-seWpp6mfQyVQT_fZsUBVmAQnOhW652exgsw=s288-c-k-c0xffffffff-no-rj-mo", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:21.011Z", "updated_at": "2023-09-12T23:47:21.011Z", "socials": []}, {"id": 228, "name": "Anita (or) BATESON", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.962Z", "updated_at": "2023-09-12T23:47:20.962Z", "socials": []}, {"id": 227, "name": "<PERSON>", "is_fictional": false, "birth_date": "2021-05-03T00:23:44.476Z", "death_date": "2021-05-03T00:23:44.476Z", "job": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Neuro-Hypnotist", "description": "<PERSON> is a fictional character. <PERSON> the peddler, alias <PERSON><PERSON>, magician, alias Dr. <PERSON>, \"Neuro-Hypnotist\", first met <PERSON> when she was a new housemaid and he was peddling house to house in Toronto. When he called at the <PERSON><PERSON><PERSON> house, he told her he feared she might be in danger there. He's played by <PERSON><PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON><PERSON><PERSON>-1620483908153.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.932Z", "updated_at": "2023-09-12T23:47:20.932Z", "socials": []}, {"id": 226, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "job": "Chief Executive Officer of Microsoft", "description": "<PERSON><PERSON><PERSON> is an engineer and Indian American business executive. He is the chief executive officer of Microsoft, succeeding <PERSON> in 2014. He led a giant round of layoffs and flattened the organization, getting rid of middle managers.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/1/19/<PERSON><PERSON><PERSON>_smiling-print.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.882Z", "updated_at": "2023-09-12T23:47:20.882Z", "socials": []}, {"id": 225, "name": "<PERSON>", "is_fictional": false, "job": "American entrepreneur", "description": "<PERSON> professionally known as <PERSON>, was an American entrepreneur, author and motivational speaker.", "image_url": "https://images-na.ssl-images-amazon.com/images/I/619YaezqqwL._SY355_.png", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.845Z", "updated_at": "2023-09-12T23:47:20.845Z", "socials": []}, {"id": 224, "name": "<PERSON>", "is_fictional": false, "job": "American basketball player", "description": "<PERSON> was an American professional basketball player. As a shooting guard, <PERSON> entered the National Basketball Association directly from high school, and played his entire 20-season professional career in the league with the Los Angeles Lakers.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/8/82/<PERSON>_<PERSON>_2015.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.813Z", "updated_at": "2023-09-12T23:47:20.813Z", "socials": []}, {"id": 223, "name": "<PERSON>", "is_fictional": true, "job": "Chief Deck Officer", "description": "<PERSON> is a fictional character played by  <PERSON> in the film \"Passengers\". He plays as the chief deck officer on the Avalon and helps to repair the ship.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FGus Mancuso-1615241494757.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.776Z", "updated_at": "2023-09-12T23:47:20.776Z", "socials": []}, {"id": 222, "name": "<PERSON>", "is_fictional": false, "job": "Dutch painter", "description": "<PERSON> was a Dutch post-impressionist painter who is among the most famous and influential figures in the history of Western art. In just over a decade, he created about 2,100 artworks, including around 860 oil paintings, most of which date from the last two years of his life.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/4/4c/<PERSON>_<PERSON>_<PERSON>_-_Self-Portrait_-_Google_Art_Project_%28454045%29.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.724Z", "updated_at": "2023-09-12T23:47:20.724Z", "socials": []}, {"id": 221, "name": "<PERSON><PERSON>", "is_fictional": true, "job": "Priest", "description": "<PERSON><PERSON> is a fictional character played by <PERSON><PERSON><PERSON><PERSON> in the TV series \"La Révolution\". In the show, he's a priest close to <PERSON> and he believes that everyone deserves to be saved.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMaxence-1604359755808.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.689Z", "updated_at": "2023-09-12T23:47:20.689Z", "socials": []}, {"id": 220, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "description": "<PERSON><PERSON> was an Indian lawyer, anti-colonial nationalist, and political ethicist, who employed nonviolent resistance to lead the successful campaign for India's independence from British Rule, and in turn inspire movements for civil rights and freedom across the world.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.656Z", "updated_at": "2023-09-12T23:47:20.656Z", "socials": []}, {"id": 219, "name": "<PERSON>", "is_fictional": false, "image_url": "https://qph.fs.quoracdn.net/main-qimg-b9426eb8963387a2855b2d76f6cc925e", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.622Z", "updated_at": "2023-09-12T23:47:20.622Z", "socials": []}, {"id": 218, "name": "<PERSON><PERSON>", "is_fictional": true, "job": "World champion chess player", "description": "<PERSON><PERSON> is a fictional character played by <PERSON><PERSON> in the TV miniseries The Queen's Gambit. In the show, he's the current Soviet-Russian world champion chess player and <PERSON>'s strongest competitor.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FVasily Borgov-1607039449410.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.586Z", "updated_at": "2023-09-12T23:47:20.586Z", "socials": []}, {"id": 217, "name": "Esty", "is_fictional": false, "job": "Housewife", "description": "<PERSON> \"<PERSON><PERSON>\" <PERSON> is a fictional character played by <PERSON><PERSON> in the miniseries \"Unorthodox\". In the miniseries, she's a 19-year-old Jewish woman who runs away from her arranged marriage and Ultra-Orthodox community in Williamsburg, Brooklyn, New York City. She moves to Berlin, where her estranged mother lives, and tries to navigate a secular life and take classes at a music conservatory.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/8/8c/<PERSON><PERSON>_<PERSON>_at_Warsaw_Zoo_%28cropped%29.png", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.531Z", "updated_at": "2023-09-12T23:47:20.531Z", "socials": []}, {"id": 216, "name": "<PERSON>", "is_fictional": true, "job": "Physician", "description": "<PERSON> is a fictional character played by <PERSON> in the TV series \"La Révolution\". In the shows, he's a physician (medical doctor) investigating mysterious deaths and disparitions. Although fictional, this character truly existed between 28 May 1738 – 26 March 1814.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON><PERSON>-1604324182463.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.498Z", "updated_at": "2023-09-12T23:47:20.498Z", "socials": []}, {"id": 215, "name": "<PERSON>", "is_fictional": true, "job": "Lawyer", "description": "<PERSON> is a fictional character played by <PERSON> in the TV series \"Dirty Jonh\". In the show, he's a lawyer specialized in pursuing medical malpactrices. He's married to <PERSON> and has four children.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FD<PERSON>-1604439846183.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.464Z", "updated_at": "2023-09-12T23:47:20.464Z", "socials": []}, {"id": 214, "name": "<PERSON>", "is_fictional": false, "job": "<PERSON><PERSON><PERSON>", "description": "<PERSON> is a fictional character played by <PERSON> in the TV miniseries The Outsider. He's a bouncer at the Peach Crease night club.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/d/d3/<PERSON>_Considine_at_the_%22Tyrannosaur%22_Q%26A_at_the_Quad_in_Derby_%286202793361%29.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.430Z", "updated_at": "2023-09-12T23:47:20.430Z", "socials": []}, {"id": 213, "name": "<PERSON>", "is_fictional": false, "job": "Psychologist", "description": "<PERSON> is a fictional character played by <PERSON> in the TV miniseries The Outsider. He is a psychologist who has the detective <PERSON> as a patient.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSteve-Witting.jpg?alt=media&token=4f57ada6-5011-4722-9d1b-65f0550b3a65", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.398Z", "updated_at": "2023-09-12T23:47:20.398Z", "socials": []}, {"id": 212, "name": "<PERSON>", "is_fictional": false, "description": "<PERSON>, Jr., better known by his stage name <PERSON>, is an American rapper and record producer.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.364Z", "updated_at": "2023-09-12T23:47:20.364Z", "socials": []}, {"id": 210, "name": "<PERSON>", "is_fictional": true, "birth_date": "2021-05-04T18:24:24.935Z", "death_date": "2021-05-04T18:24:24.935Z", "job": "Maid", "description": "<PERSON> is a fictional character, a maid, and friend of <PERSON>. <PERSON> believes in herself and in Canada as a place that a hard working girl can earn her way. She plans to save her wages for a dowry so she can marry a farmer and be mistress of her own home. She's played by  <PERSON> in the miniserie.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMary Whitney-1620483586526.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.330Z", "updated_at": "2023-09-12T23:47:20.330Z", "socials": []}, {"id": 209, "name": "Japanese Proverb", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.296Z", "updated_at": "2023-09-12T23:47:20.296Z", "socials": []}, {"id": 208, "name": "<PERSON>", "is_fictional": false, "job": "American football player", "description": "<PERSON>, nicknamed \"<PERSON> the Dodger\", \"Captain <PERSON>\" and \"Captain <PERSON>\", is an American former professional football player who was a quarterback for the Dallas Cowboys in the National Football League.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/f/f5/<PERSON><PERSON><PERSON>_cowboys_qb.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.262Z", "updated_at": "2023-09-12T23:47:20.262Z", "socials": []}, {"id": 207, "name": "<PERSON>", "is_fictional": true, "birth_date": "2021-03-14T23:24:56.288Z", "death_date": "2021-03-14T23:24:56.288Z", "job": "Dad", "description": "<PERSON> is a widower whose daughter goes to the same school as <PERSON><PERSON> and finds a connection with <PERSON>", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FTravis-1619988513007.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.208Z", "updated_at": "2023-09-12T23:47:20.208Z", "socials": []}, {"id": 206, "name": "<PERSON>", "is_fictional": false, "job": "American tennis player", "description": "<PERSON> was an American professional tennis player who won three Grand Slam titles. <PERSON> was the first black player selected to the United States Davis Cup team and the only black man ever to win the singles title at Wimbledon, the US Open, and the Australian Open. He retired in 1980.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/6/60/<PERSON>_<PERSON>_%28cropped%29.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.173Z", "updated_at": "2023-09-12T23:47:20.173Z", "socials": []}, {"id": 205, "name": "<PERSON>", "is_fictional": false, "job": "<PERSON><PERSON>", "description": "<PERSON>, also referred to as <PERSON> of Nazareth or <PERSON> Christ, was a first-century Jewish preacher and religious leader. He is the central figure of Christianity. Most Christians believe he is the incarnation of <PERSON> the Son and the awaited Messiah (the Christ) prophesied in the Old Testament.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/6/63/Cefal%C3%B9_Pantocrator_retouched.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.124Z", "updated_at": "2023-09-12T23:47:20.124Z", "socials": []}, {"id": 204, "name": "<PERSON>", "is_fictional": true, "job": "Dress designer", "description": "<PERSON> is a fictional character played by <PERSON> in the film <PERSON> Thread. In the film, he creates dresses for members of high society, even royalty. His clients view him as a genius whose creations enable them to become their best selves; but his creativity and charm are matched by his obsessive and controlling personality.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FReynolds Woodcock-1614102104454.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.092Z", "updated_at": "2023-09-12T23:47:20.092Z", "socials": []}, {"id": 203, "name": "St. Jean<PERSON>Baptiste<PERSON><PERSON>", "is_fictional": false, "job": "Catholic saint", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, T.O.S.F., commonly known in English as Saint <PERSON>, was a French parish priest who is venerated in the Catholic Church as a saint and as the patron saint of parish priests.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/3/34/Johnvianney.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.054Z", "updated_at": "2023-09-12T23:47:20.054Z", "socials": []}, {"id": 202, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "job": "French journalist", "description": "<PERSON><PERSON><PERSON> is a french journalist working in the field of Internet and technologies. He hosts \"Plein Écran\" on the french channel LCI.  he also takes part in various prpgrams such as \"Les coulisses de l'économie\" on TF1, \"Le Rendez-vous Tech\" or \"On Refait le Mac\".", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCédric Ingrand-1604420315283.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:20.022Z", "updated_at": "2023-09-12T23:47:20.022Z", "socials": []}, {"id": 200, "name": "<PERSON>", "is_fictional": false, "description": "<PERSON> is an American writer, educator, and activist, best known for his books on public education in the United States.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.989Z", "updated_at": "2023-09-12T23:47:19.989Z", "socials": []}, {"id": 199, "name": "<PERSON>", "is_fictional": false, "job": "<PERSON><PERSON><PERSON>", "description": "<PERSON>, known as <PERSON>, was an Italian polymath of the Renaissance whose areas of interest included invention, drawing, painting, sculpture, architecture, science, music, mathematics, engineering, literature, anatomy, geology, astronomy, botany, paleontology, and cartography. ", "image_url": "https://upload.wikimedia.org/wikipedia/commons/c/cb/<PERSON>_<PERSON>_-_Portrait_of_<PERSON>.png", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.956Z", "updated_at": "2023-09-12T23:47:19.956Z", "socials": []}, {"id": 198, "name": "<PERSON>", "is_fictional": false, "job": "American minister", "description": "<PERSON> was an American minister and author known for his work in popularizing the concept of positive thinking, especially through his best-selling book The Power of Positive Thinking.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/9/9d/<PERSON>_<PERSON>_<PERSON>_NYWTS.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.923Z", "updated_at": "2023-09-12T23:47:19.923Z", "socials": []}, {"id": 197, "name": "<PERSON>'s grandfather", "is_fictional": true, "description": "<PERSON>'s grandfather comes to Japan with his granddaughter, as she has her pianist concert.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.890Z", "updated_at": "2023-09-12T23:47:19.890Z", "socials": []}, {"id": 196, "name": "<PERSON> (old)", "is_fictional": true, "job": "Group leader", "description": "<PERSON> is a fictional character played by <PERSON> in the TV series \"Dark\". This character refers to an old and disfigured <PERSON>. He's also the leader of <PERSON><PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAdam (old)-1604582185919.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.856Z", "updated_at": "2023-09-12T23:47:19.856Z", "socials": []}, {"id": 194, "name": "<PERSON>", "is_fictional": false, "job": "Comedian", "description": "<PERSON> was an English comedian who led a long and successful career in the television and theatre, appearing occasionally in films.", "image_url": "https://m.media-amazon.com/images/M/MV5BOGFlY2M0MjctNjI1Zi00ZDgxLWFiNjQtYjgwMmZhYTY1ZGQ0XkEyXkFqcGdeQXVyNDUzOTQ5MjY@._V1_.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.821Z", "updated_at": "2023-09-12T23:47:19.821Z", "socials": []}, {"id": 193, "name": "<PERSON>", "is_fictional": false, "job": "American essayist", "description": "<PERSON> was an American essayist, lecturer, philosopher, and poet who led the transcendentalist movement of the mid-19th century.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/d/d5/<PERSON>_<PERSON><PERSON>_<PERSON>_ca1857_retouched.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.786Z", "updated_at": "2023-09-12T23:47:19.786Z", "socials": []}, {"id": 191, "name": "<PERSON>", "is_fictional": false, "job": "Pastor", "description": "<PERSON> \"<PERSON>\" <PERSON> is an evangelical Christian pastor, author, educator, and radio preacher. He founded Insight for Living, headquartered in Frisco, Texas, which airs a radio program of the same name on more than 2,000 stations around the world in 15 languages.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.750Z", "updated_at": "2023-09-12T23:47:19.750Z", "socials": []}, {"id": 190, "name": "<PERSON>", "is_fictional": false, "job": "American journalist", "description": "<PERSON> is an American feminist, journalist, and social political activist who became nationally recognized as a leader and a spokeswoman for the American feminist movement in the late 1960s and early 1970s. <PERSON><PERSON> was a columnist for New York magazine, and a co-founder of Ms. magazine.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/a/a6/<PERSON>_<PERSON><PERSON>_%2829126367513%29_%28cropped%29.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.717Z", "updated_at": "2023-09-12T23:47:19.717Z", "socials": []}, {"id": 189, "name": "<PERSON>, Duke of Norfolk", "is_fictional": false, "birth_date": "1472-12-31T23:50:39.000Z", "birth_location": "England", "death_date": "1554-08-24T23:50:39.000Z", "job": "<PERSON>", "description": "<PERSON>, 3rd Duke of Norfolk KG PC, was a prominent English politician and nobleman of the Tudor era. He was an uncle of two of the wives of King <PERSON>, namely <PERSON> and <PERSON>, both of whom were beheaded, and played a major role in the machinations affecting these royal marriages. In the film \"The Other Boleyn Girl\" he is played by <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2<PERSON><PERSON><PERSON>, Duke of Norfolk-1611494859233.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.685Z", "updated_at": "2023-09-12T23:47:19.685Z", "socials": []}, {"id": 188, "name": "<PERSON>", "is_fictional": false, "job": "American journalist", "description": "<PERSON> was an American journalist. He is most widely known for his long career with CBS, first for his \"On the Road\" segments on The CBS Evening News with <PERSON>, and later as the first anchor of CBS News Sunday Morning, a position he held for fifteen years.", "image_url": "https://upload.wikimedia.org/wikipedia/en/2/28/CBSSundayMorningLogo2.gif", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.651Z", "updated_at": "2023-09-12T23:47:19.651Z", "socials": []}, {"id": 187, "name": "<PERSON>", "is_fictional": true, "job": "Neighbor", "description": "<PERSON> is a fictional character played by <PERSON><PERSON> in the TV series \"You Me Her\". In the show, he's one of the <PERSON><PERSON><PERSON><PERSON>'s neighbors and <PERSON>'s best friend.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDave Amari-1608247811348.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.618Z", "updated_at": "2023-09-12T23:47:19.618Z", "socials": []}, {"id": 186, "name": "<PERSON>", "is_fictional": false, "birth_date": "1809-01-18T23:00:00.000Z", "birth_location": "Boston, Massachusetts, United States", "death_date": "1849-10-06T22:00:00.000Z", "job": "American writer", "description": "<PERSON> was an American writer, poet, editor, and literary critic. <PERSON> is best known for his poetry and short stories, particularly his tales of mystery and the macabre.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FEd<PERSON>-1619982678027.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.581Z", "updated_at": "2023-09-12T23:47:19.581Z", "socials": []}, {"id": 185, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "job": "Japanese video game designer", "description": "<PERSON><PERSON><PERSON> is a Japanese video game designer and producer at Nintendo, where he serves as one of its representative directors. He is the creator of some of the most acclaimed and best-selling game franchises of all time, such as <PERSON> and The Legend of Zelda.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/7/78/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_201911.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.530Z", "updated_at": "2023-09-12T23:47:19.530Z", "socials": []}, {"id": 184, "name": "<PERSON>", "is_fictional": false, "job": "16th U.S. President", "description": "<PERSON> was an American statesman and lawyer who served as the 16th president of the United States from March 1861 until his assassination in April 1865. <PERSON> led the nation through the American Civil War, its bloodiest war and its greatest moral, constitutional, and political crisis.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/a/ab/<PERSON>_<PERSON>_O-77_matte_collodion_print.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.496Z", "updated_at": "2023-09-12T23:47:19.496Z", "socials": []}, {"id": 183, "name": "Forest officer 1", "is_fictional": true, "job": "Officer", "description": "This forest guard was doing a night walk on his horse but encounters <PERSON>. He appears on season 1 chapter 6, around the time code: 14:00.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FForest%20officer%201.png?alt=media&token=002454c4-de49-4384-b23d-f72918a3e590", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.462Z", "updated_at": "2023-09-12T23:47:19.462Z", "socials": []}, {"id": 182, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "job": "Austrian neurologist", "description": "<PERSON><PERSON><PERSON> was an Austrian neurologist and the founder of psychoanalysis, a clinical method for treating psychopathology through dialogue between a patient and a psychoanalyst. <PERSON> was born to Galician Jewish parents in the Moravian town of Freiberg, in the Austrian Empire.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/3/36/Sig<PERSON>_<PERSON>%2C_by_<PERSON>_Halberstadt_%28cropped%29.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.426Z", "updated_at": "2023-09-12T23:47:19.426Z", "socials": []}, {"id": 181, "name": "Assistant Director <PERSON>", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.395Z", "updated_at": "2023-09-12T23:47:19.395Z", "socials": []}, {"id": 180, "name": "Etoiles", "is_fictional": false, "birth_date": "1996-08-01T22:00:00.000Z", "birth_location": "Limoges, France", "job": "French streamer", "description": "<PERSON><PERSON><PERSON> is a french Twitch streamer. He's mostly known for his culture streams and his high skills in video games, notably on Super Smash Bros. He regularly co-host \"The Night of Culture\" with <PERSON>, the television host of \"Questions pour un champion\".", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FEtoiles-1604563739020.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.359Z", "updated_at": "2023-09-12T23:47:19.359Z", "socials": []}, {"id": 179, "name": "<PERSON><PERSON>", "is_fictional": false, "job": "American businessman", "description": "<PERSON><PERSON> is an American businessman, investor, author, columnist, and motivational speaker. <PERSON> was raised on Chicago's South side. He began his entrepreneurial career at the age of six selling homemade lotion and hand-painted rocks door-to-door.", "image_url": "https://m.media-amazon.com/images/M/MV5BOThkNzZhMzQtNmIwMS00MzA1LTk3NGUtMDMzYjcwODI3ZWI0XkEyXkFqcGdeQXVyNDUzOTQ5MjY@._V1_SY1000_CR0,0,666,1000_AL_.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.327Z", "updated_at": "2023-09-12T23:47:19.327Z", "socials": []}, {"id": 178, "name": "<PERSON>", "is_fictional": false, "job": "Real-estate developer", "description": "<PERSON> was a real-estate developer in post Civil War Atlanta.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/9/94/<PERSON>_<PERSON><PERSON>_<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.275Z", "updated_at": "2023-09-12T23:47:19.275Z", "socials": []}, {"id": 177, "name": "<PERSON>", "is_fictional": true, "job": "<PERSON>", "description": "<PERSON> is a fictional character played by <PERSON> in the TV series \"La Révolution\". In the show he's <PERSON>'s uncle and <PERSON><PERSON><PERSON> father. He has a manipulator mind and doesn't have a high esteem of other  people.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FC<PERSON>les de Montargis-1604350311429.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.241Z", "updated_at": "2023-09-12T23:47:19.241Z", "socials": []}, {"id": 176, "name": "Virginia Woolf", "is_fictional": false, "job": "Writer", "description": "<PERSON><PERSON><PERSON> was an English writer, considered one of the most important modernist 20th-century authors and also a pioneer in the use of stream of consciousness as a narrative device. <PERSON><PERSON><PERSON> was born into an affluent household in South Kensington, London, the seventh child in a blended family of eight.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/0/0b/<PERSON>_<PERSON>_<PERSON>_-_Virginia_Woolf_in_1902_-_Restoration.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.188Z", "updated_at": "2023-09-12T23:47:19.188Z", "socials": []}, {"id": 175, "name": "<PERSON>", "is_fictional": true, "job": "Writer", "description": "<PERSON> is a successful author of thriller novels in the film \"Deadly illusions\". The character is played by <PERSON><PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMary Morrison-1617622251616.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.154Z", "updated_at": "2023-09-12T23:47:19.154Z", "socials": []}, {"id": 174, "name": "V", "is_fictional": true, "job": "Mercenary", "description": "<PERSON> is a mercenary and the main character in the video game Cyberpunk 2077.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FV-1611515205701.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.122Z", "updated_at": "2023-09-12T23:47:19.122Z", "socials": []}, {"id": 172, "name": "<PERSON>", "is_fictional": false, "job": "Bond salesman", "description": "<PERSON> is a fictional character and narrator in <PERSON><PERSON>'s The Great Gatsby (1925).  In the 2013 film, he is  a would-be writer, <PERSON><PERSON><PERSON>'s friend, and the film's narrator.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fnarrator-nick.jpg?alt=media&token=5e416832-d7f9-4baf-9c05-47512fc08399", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.050Z", "updated_at": "2023-09-12T23:47:19.050Z", "socials": []}, {"id": 171, "name": "<PERSON>", "is_fictional": false, "description": "<PERSON> is a fictionnal character played by <PERSON> in the TV series Warrior Nun.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/b/b8/AlbaBaptista.png", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:19.017Z", "updated_at": "2023-09-12T23:47:19.017Z", "socials": []}, {"id": 169, "name": "<PERSON>", "is_fictional": false, "job": "Austrian neurologist", "description": "<PERSON> was an Austrian neurologist and psychiatrist as well as a Holocaust survivor, surviving Theresienstadt, Kaufering and Türkheim.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/f/fe/<PERSON>_<PERSON>2.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:18.982Z", "updated_at": "2023-09-12T23:47:18.982Z", "socials": []}, {"id": 168, "name": "<PERSON>", "is_fictional": false, "job": "Chiropractor", "description": "<PERSON> is a fictional character played by <PERSON> in the TV series Dead to Me. He's <PERSON>'s semi-identical twin brother and <PERSON>'s new love interest who is a chiropractor.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/f/ff/<PERSON>_<PERSON>_by_<PERSON>_Skidmore.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:18.949Z", "updated_at": "2023-09-12T23:47:18.949Z", "socials": []}, {"id": 167, "name": "Narrator", "is_fictional": true, "job": "Narrator", "description": "The narrator in the video game \"Little Hope\" tells part of the story to the player and help to understand what happened.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FNarrator-1611515776691.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:18.898Z", "updated_at": "2023-09-12T23:47:18.898Z", "socials": []}, {"id": 166, "name": "<PERSON>", "is_fictional": false, "job": "American comedian", "description": "<PERSON>. is an American stand-up comedian, actor, musician, author, and convicted sex offender. <PERSON><PERSON> began his career as a stand-up comic at the hungry i in San Francisco during the 1960s.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/9/9d/2011_<PERSON>_<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:18.859Z", "updated_at": "2023-09-12T23:47:18.859Z", "socials": []}, {"id": 165, "name": "<PERSON>", "is_fictional": true, "job": "Mechanical engineer", "description": "<PERSON> is a fictional character played by <PERSON> in the film \"Passengers\". In the film, he's a mechanical engineer who wakes up 90 years too early on a sleeper ship transporting 5,000 colonists and 258 crew members in hibernation pods. The ship is travelling from Earth to the planet Homestead II.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FJ<PERSON>-1614208672508.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:18.825Z", "updated_at": "2023-09-12T23:47:18.825Z", "socials": []}, {"id": 164, "name": "<PERSON>, Lord <PERSON>", "is_fictional": false, "birth_date": "1809-08-05T22:00:00.000Z", "birth_location": "Somersby, Lincolnshire, England", "death_date": "1892-10-05T22:00:00.000Z", "job": "British poet", "description": "<PERSON>, 1st Baron <PERSON> was a British poet. He was the Poet Laureate during much of <PERSON>'s reign and remains one of the most popular British poets. In 1829, <PERSON><PERSON><PERSON> was awarded the Chancellor's Gold Medal at Cambridge for one of his first pieces, \"Timbuktu\". ", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2<PERSON><PERSON><PERSON>, Lord <PERSON>-1619985246133.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:18.764Z", "updated_at": "2023-09-12T23:47:18.764Z", "socials": []}, {"id": 163, "name": "<PERSON><PERSON>", "is_fictional": false, "job": "Developer", "description": "Creator of FIST OF AWESOME and co-creator of BREW TOWN & MAXIMUM CAR. More beard than man. He/him.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FNicoll Hunt-1611494024829.png?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:18.731Z", "updated_at": "2023-09-12T23:47:18.731Z", "socials": []}, {"id": 161, "name": "<PERSON> of Ockham", "is_fictional": false, "description": "English Franciscan friar, scholastic philosopher, and theologian, who is believed to have been born in Ockham, a small village in Surrey.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:18.698Z", "updated_at": "2023-09-12T23:47:18.698Z", "socials": []}, {"id": 160, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "job": "Streamer", "description": "<PERSON><PERSON><PERSON> is a french Twitch streamer. She plays various video games including adventure, FPS (First Person Shooter) or horror. Her streams contain also manual craft and cooking amoung other activites.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fmaghla.jpg?alt=media&token=57f571dc-9b28-4915-b5af-b52741303259", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:18.648Z", "updated_at": "2023-09-12T23:47:18.648Z", "socials": []}, {"id": 158, "name": "<PERSON><PERSON><PERSON><PERSON>", "is_fictional": false, "description": "Former international human rights lawyer. Lover of planes, puppies, pie and Studs Terkel.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FY-<PERSON><PERSON>-1695221068885.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:18.610Z", "updated_at": "2023-09-12T23:47:18.610Z", "socials": []}, {"id": 156, "name": "Zeus", "is_fictional": true, "job": "Greek god", "description": "<PERSON> is the sky and thunder god in ancient Greek religion, who rules as king of the gods of Mount Olympus. His name is cognate with the first element of his Roman equivalent <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FZeus-1610062988149.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:18.574Z", "updated_at": "2023-09-12T23:47:18.574Z", "socials": []}, {"id": 155, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-04-30T23:37:54.641Z", "death_date": "2024-04-30T23:37:54.641Z", "description": "Quality strip cartoon for whole family since 2001. Director of collection (#Octopus éditions Delcourt).", "image_url": "https://pbs.twimg.com/profile_images/960125649611522050/TGR6i7Zk_400x400.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:18.487Z", "updated_at": "2023-09-12T23:47:18.487Z", "socials": []}, {"id": 154, "name": "<PERSON>", "is_fictional": false, "job": "American business magnate", "description": "Des<PERSON><PERSON><PERSON><PERSON> was an American business magnate, industrial designer, investor, and media proprietor.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/f/f5/<PERSON>_<PERSON>_Headshot_2010-CROP2.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:18.449Z", "updated_at": "2023-09-12T23:47:18.449Z", "socials": []}, {"id": 153, "name": "<PERSON>", "is_fictional": true, "job": "<PERSON>", "description": "<PERSON> is a fictional character played by <PERSON> in the TV series \"La Révolution\". He's a noble who's friend with <PERSON> and doesn't really like <PERSON><PERSON><PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBaron de Lariboise-1604353981909.png?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:18.410Z", "updated_at": "2023-09-12T23:47:18.410Z", "socials": []}, {"id": 151, "name": "<PERSON><PERSON>", "is_fictional": false, "job": "<PERSON>weeper, mercenary", "description": "<PERSON><PERSON> (冴羽獠, <PERSON><PERSON><PERSON>) is a sweeper in Tokyo and the main character of the City Hunter series.\n\nAt the age of three, <PERSON><PERSON> was the only survivor of a plane crash in Central America. He was raised as a guerilla fighter and has no knowledge of his prior identity. After the war, <PERSON><PERSON> makes his way to the United States, before eventually moving to Tokyo.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FRyo Saeba-1611505771313.png?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:18.374Z", "updated_at": "2023-09-12T23:47:18.374Z", "socials": []}, {"id": 150, "name": "<PERSON><PERSON>", "is_fictional": false, "job": "Prime Minister of France", "description": "<PERSON><PERSON><PERSON> is a French politician serving as Prime Minister of France since 15 May 2017 under President <PERSON>. A lawyer by occupation, <PERSON> is a former member of the Union for a Popular Movement, which later became The Republicans.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/d/df/%C3%89<PERSON><PERSON>_<PERSON>_2019_%28cropped%29.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:18.328Z", "updated_at": "2023-09-12T23:47:18.328Z", "socials": []}, {"id": 148, "name": "Dr. <PERSON><PERSON>", "is_fictional": true, "job": "Psychiatrist", "description": "Dr. <PERSON><PERSON> is a psychiatrist helping <PERSON> in her life and psyhcological issues.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:18.289Z", "updated_at": "2023-09-12T23:47:18.289Z", "socials": []}, {"id": 147, "name": "Father <PERSON>", "is_fictional": false, "job": "Father", "description": "<PERSON> is a fictional character played by <PERSON><PERSON><PERSON> in the TV series Warrior Nun.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/6/6d/Trist%C3%A1n_Ulloa.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:18.253Z", "updated_at": "2023-09-12T23:47:18.253Z", "socials": []}, {"id": 146, "name": "Horizon-Gull", "is_fictional": false, "description": "Official YouTube channel of Hacking social blog.", "image_url": "https://yt3.ggpht.com/a/AGF-l79rKQYYjcgr32cZgTpVXqqsa8RprOUV14Lpmw=s288-c-k-c0xffffffff-no-rj-mo", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:18.192Z", "updated_at": "2023-09-12T23:47:18.192Z", "socials": []}, {"id": 145, "name": "<PERSON><PERSON>", "is_fictional": false, "job": "Adviser", "description": "<PERSON><PERSON> is a fictional character played by <PERSON> in the TV series Warrior Nun.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/3/36/<PERSON>_<PERSON>_<PERSON>_28092016_Threepenny_Opera_aus_DSCF2668.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:18.157Z", "updated_at": "2023-09-12T23:47:18.157Z", "socials": []}, {"id": 144, "name": "Serap", "is_fictional": true, "job": "Mother", "description": "<PERSON><PERSON> is a fictional character played by <PERSON><PERSON><PERSON> in the TV series \"At<PERSON><PERSON> (The Gift)\". In the show, she's <PERSON><PERSON><PERSON>'s mother and has a strange history with her own mother.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSerap-1604424045646.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:18.096Z", "updated_at": "2023-09-12T23:47:18.096Z", "socials": []}, {"id": 143, "name": "<PERSON>", "is_fictional": false, "description": "Sir <PERSON> is a British business magnate, investor, author and philanthropist. He founded the Virgin Group in the 1970s, which controls more than 400 companies in various fields. <PERSON><PERSON><PERSON> expressed his desire to become an entrepreneur at a young age.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:18.063Z", "updated_at": "2023-09-12T23:47:18.063Z", "socials": []}, {"id": 142, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "description": "<PERSON>, known professionally as <PERSON><PERSON><PERSON> was a French-Cuban American diarist, essayist, novelist, and writer of short stories and erotica. Born to Cuban parents in France, <PERSON><PERSON> was the daughter of composer <PERSON><PERSON><PERSON><PERSON> and <PERSON>, a classically trained singer.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:18.026Z", "updated_at": "2023-09-12T23:47:18.026Z", "socials": []}, {"id": 140, "name": "<PERSON><PERSON>", "is_fictional": true, "job": "Centaur", "description": "In Greek mythology, <PERSON><PERSON> was held to be the superlative centaur amongst his brethren since he was called the \"wisest and justest of all the centaurs\".", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FChiron-1610063917707.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.971Z", "updated_at": "2023-09-12T23:47:17.971Z", "socials": []}, {"id": 139, "name": "<PERSON>", "is_fictional": true, "job": "Housekeeper", "description": "<PERSON> is a fictional character played by  <PERSON><PERSON><PERSON><PERSON> in the TV series \"The Haunting of Bly Manor\". In the show, she's the housekeeper. She has a good relationship with the house's occupants.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FH<PERSON><PERSON> Grose-1604420780299.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.937Z", "updated_at": "2023-09-12T23:47:17.937Z", "socials": []}, {"id": 138, "name": "<PERSON>", "is_fictional": false, "job": "FBI Agent", "description": "<PERSON>, a special agent in the FBI's Behavio\nral Science Unit, is a fictional character in Mindhunter TV series.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.888Z", "updated_at": "2023-09-12T23:47:17.888Z", "socials": []}, {"id": 137, "name": "<PERSON>", "is_fictional": true, "birth_date": "2021-05-16T22:08:44.257Z", "death_date": "2021-05-16T22:08:44.257Z", "job": "Forgemaster", "description": "<PERSON> is a rivalrous devil forgemaster and fierce loyalist of <PERSON> who helps to lead his army.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FIsaac-*************.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.854Z", "updated_at": "2023-09-12T23:47:17.854Z", "socials": []}, {"id": 136, "name": "<PERSON><PERSON><PERSON><PERSON>", "is_fictional": false, "job": "American financier", "description": "<PERSON> Morgan Sr. was an American financier and banker who dominated corporate finance on Wall Street throughout the Gilded Age.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/4/41/JohnPierpontMorgan.png", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.819Z", "updated_at": "2023-09-12T23:47:17.819Z", "socials": []}, {"id": 135, "name": "<PERSON>", "is_fictional": true, "job": "College professor", "description": "<PERSON> is a fictional character played by <PERSON> in the streaming TV series \"Firefly Lane\". In the show, he's a college professor teaching journalism classes, and has <PERSON><PERSON> as a student.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FChad Wiley-*************.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.768Z", "updated_at": "2023-09-12T23:47:17.768Z", "socials": []}, {"id": 133, "name": "<PERSON>", "is_fictional": true, "job": "Professor", "description": "<PERSON> is one of eight playable characters in Octopath Traveler. He is a professor at the Royal Academy located in Atlasdam, found in the Flatlands. <PERSON> is known for caring greatly about knowledge and his passion for teaching.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F <PERSON>-1604538925294.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.692Z", "updated_at": "2023-09-12T23:47:17.692Z", "socials": []}, {"id": 131, "name": "<PERSON>", "is_fictional": true, "job": "Chess champion player", "description": "<PERSON> is a fictional character played by <PERSON> in the TV series The Queen's Gambit. In the show, he's a champion player <PERSON> defeats in her first tournament and later befriends.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FH<PERSON><PERSON>-1607039087870.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.658Z", "updated_at": "2023-09-12T23:47:17.658Z", "socials": []}, {"id": 130, "name": "<PERSON>", "is_fictional": false, "job": "Realtor", "description": "<PERSON>, is a fictional character played by <PERSON> in the TV series Dead to Me. In the series, she's a realtor whose husband <PERSON> was killed by a hit-and-run driver.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/b/bb/<PERSON>_<PERSON>_2014_Comic_Con_%28cropped%29.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.611Z", "updated_at": "2023-09-12T23:47:17.611Z", "socials": []}, {"id": 129, "name": "<PERSON>", "is_fictional": true, "job": "Engineer", "description": "<PERSON> is a fictional character in the french novel \"Le parfum du bonheur est plus fort sous la pluie\" in which he's <PERSON>'s husband. They've a four years old children and try to navigate their relation's difficulties.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.577Z", "updated_at": "2023-09-12T23:47:17.577Z", "socials": []}, {"id": 128, "name": "Socrates", "is_fictional": false, "job": "Philosopher", "description": "<PERSON><PERSON> was a classical Greek philosopher credited as one of the founders of Western philosophy, and as being the first moral philosopher of the Western ethical tradition of thought.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/a/a4/Socrates_Louvre.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.543Z", "updated_at": "2023-09-12T23:47:17.543Z", "socials": []}, {"id": 127, "name": "<PERSON>. <PERSON>", "is_fictional": false, "job": "Detective", "description": "Detective <PERSON> is a fictional character played by <PERSON> in the TV miniseries The Outsider.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/3/36/<PERSON>_<PERSON>_by_<PERSON>_Ski<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.492Z", "updated_at": "2023-09-12T23:47:17.492Z", "socials": []}, {"id": 126, "name": "<PERSON>", "is_fictional": false, "job": "English novelist", "description": "<PERSON>, known by her pen name <PERSON>, was an English novelist, poet, journalist, translator and one of the leading writers of the Victorian era.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/4/48/<PERSON>_<PERSON>%2C_por_<PERSON>an%C3%A7ois_D%27A<PERSON>_<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.457Z", "updated_at": "2023-09-12T23:47:17.457Z", "socials": []}, {"id": 125, "name": "<PERSON><PERSON>", "is_fictional": true, "birth_date": "2021-05-16T00:09:44.758Z", "death_date": "2021-05-16T00:09:44.758Z", "job": "Diplomate", "description": "<PERSON><PERSON> is the diplomat member of the Council of Sisters. She is in charge of subduing <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLenore-1621855972534.png?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.424Z", "updated_at": "2023-09-12T23:47:17.424Z", "socials": []}, {"id": 124, "name": "<PERSON>", "is_fictional": false, "job": "Indian lawyer", "description": "<PERSON><PERSON> was an Indian lawyer, anti-colonial nationalist, and political ethicist, who employed nonviolent resistance to lead the successful campaign for India's independence from British Rule, and in turn inspire movements for civil rights and freedom across the world.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/7/7a/<PERSON><PERSON><PERSON>-<PERSON>%2C_studio%2C_1931.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.391Z", "updated_at": "2023-09-12T23:47:17.391Z", "socials": []}, {"id": 123, "name": "Zig Ziglar", "is_fictional": false, "job": "American author", "description": "<PERSON> \"<PERSON><PERSON>\" <PERSON> was an American author, salesman, and motivational speaker.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/f/ff/Zig_<PERSON>iglar_at_Get_Motivated_Seminar%2C_Cow_Palace_2009-3-24_3.JPG", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.341Z", "updated_at": "2023-09-12T23:47:17.341Z", "socials": []}, {"id": 122, "name": "<PERSON><PERSON> (fictional)", "is_fictional": false, "job": "Head of studio", "description": "<PERSON><PERSON> is a fictional character played by <PERSON> in the TV miniseries \"Hollywood\". She plays the wife of <PERSON>, the head of Ace Studios, and a former actress.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAvis <PERSON> (fictional)-1692971165614.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.302Z", "updated_at": "2023-09-12T23:47:17.302Z", "socials": []}, {"id": 121, "name": "<PERSON>", "is_fictional": false, "job": "Motivational speaker", "description": "He is the CEO and Founder of Kwik Learning, a leader in accelerated learning with online students of every age and vocation in over 150 countries.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FJ<PERSON>-1689879645546.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.268Z", "updated_at": "2023-09-12T23:47:17.268Z", "socials": []}, {"id": 120, "name": "<PERSON>", "is_fictional": false, "job": "Book editor", "description": "<PERSON> is a fictional character issued by <PERSON> in the film \"The Holiday\".", "image_url": "https://upload.wikimedia.org/wikipedia/commons/9/9a/KateWinsletByAndreaRaffin2011.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.210Z", "updated_at": "2023-09-12T23:47:17.210Z", "socials": []}, {"id": 119, "name": "<PERSON>", "is_fictional": false, "job": "American writer", "description": "<PERSON> was an American writer and lecturer, and the developer of famous courses in self-improvement, salesmanship, corporate training, public speaking, and interpersonal skills.", "image_url": "https://upload.wikimedia.org/wikipedia/en/0/0c/<PERSON>_<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.158Z", "updated_at": "2023-09-12T23:47:17.158Z", "socials": []}, {"id": 118, "name": "<PERSON>", "is_fictional": false, "birth_date": "1854-10-15T23:50:39.000Z", "birth_location": "Westland Row, Dublin, Ireland", "death_date": "1900-11-29T23:50:39.000Z", "job": "Irish poet", "description": "<PERSON><PERSON><PERSON> was an Irish poet and playwright. After writing in different forms throughout the 1880s, the early 1890s saw him become one of the most popular playwrights in London.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FOscar Wilde-1610062539926.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.125Z", "updated_at": "2023-09-12T23:47:17.125Z", "socials": []}, {"id": 117, "name": "<PERSON>", "is_fictional": false, "job": "Playwright", "description": "<PERSON>, known at his insistence simply as <PERSON>, was an Irish playwright, critic, polemicist and political activist. His influence on Western theatre, culture and politics extended from the 1880s to his death and beyond.", "image_url": "https://upload.wikimedia.org/wikipedia/en/3/30/<PERSON>-<PERSON>-ILN-1911-original.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.092Z", "updated_at": "2023-09-12T23:47:17.092Z", "socials": []}, {"id": 116, "name": "Lao Tzu", "is_fictional": false, "job": "Chinese philosopher", "description": "<PERSON><PERSON>, also rendered as <PERSON> and <PERSON><PERSON><PERSON><PERSON>, was an ancient Chinese philosopher and writer. He is the reputed author of the Tao Te Ching, the founder of philosophical Taoism, and a deity in religious Taoism and traditional Chinese religions.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/e/e8/<PERSON>_<PERSON>-<PERSON>_Riding_an_Ox.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.058Z", "updated_at": "2023-09-12T23:47:17.058Z", "socials": []}, {"id": 114, "name": "<PERSON>", "is_fictional": false, "job": "American operatic soprano", "description": "<PERSON> was an American operatic soprano whose peak career was between the 1950s and 1970s. Although she sang a repertoire from Handel and Mozart to <PERSON><PERSON><PERSON>, Massenet and Verdi, she was known for her performances in coloratura soprano roles in live opera and recordings.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/0/04/<PERSON>_<PERSON>_by_<PERSON>_<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:17.000Z", "updated_at": "2023-09-12T23:47:17.000Z", "socials": []}, {"id": 113, "name": "<PERSON>", "is_fictional": false, "job": "Writer", "description": "<PERSON> was a German writer and statesman. His works include: four novels; epic and lyric poetry; prose and verse dramas; memoirs; an autobiography; literary and aesthetic criticism; and treatises on botany, anatomy, and colour.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/0/0e/<PERSON>_%28S<PERSON><PERSON>_1828%29.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:16.965Z", "updated_at": "2023-09-12T23:47:16.965Z", "socials": []}, {"id": 112, "name": "Kalindi", "is_fictional": false, "job": "Rédac & chef de projet cinéma et séries télé", "description": "Rédac & chef de projet cinéma et séries télé pour le magazine MademoiZelle.", "image_url": "https://static.mmzstatic.com/wp-content/uploads/2019/11/trombi-480-kal.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:16.930Z", "updated_at": "2023-09-12T23:47:16.930Z", "socials": []}, {"id": 110, "name": "<PERSON>", "is_fictional": false, "job": "American novelist", "description": "<PERSON> is an American novelist, short story writer, poet, and social activist. In 1982, she wrote the novel The Color Purple, for which she won the National Book Award for hardcover fiction, and the Pulitzer Prize for Fiction. She also wrote the novels <PERSON> and The Third Life of Grange Copeland.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/5/59/<PERSON>_<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:16.872Z", "updated_at": "2023-09-12T23:47:16.872Z", "socials": []}, {"id": 109, "name": "Sofia Seranno", "is_fictional": true, "job": "Dancer", "description": "<PERSON> is a fictional character played by <PERSON><PERSON><PERSON><PERSON> in the film Vanilla Sky. She's a dancer who meets <PERSON> at a party in Manhattan thanks to a friend. They spend the night together in <PERSON>'s apartment.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSofia Seranno-1614032872773.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:16.813Z", "updated_at": "2023-09-12T23:47:16.813Z", "socials": []}, {"id": 108, "name": "<PERSON><PERSON>", "is_fictional": true, "job": "Prison staff", "description": "<PERSON><PERSON> is a fictional character played by <PERSON> in the TV series \"La Révolution\". In the show, she works in the prison brigging goods to the convicts and starts a relation with <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FKatell-1604354821704.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:16.774Z", "updated_at": "2023-09-12T23:47:16.774Z", "socials": []}, {"id": 107, "name": "<PERSON>", "is_fictional": false, "job": "American novelist", "description": "<PERSON> is an American novelist, satirist, and poet, known particularly for her 1973 novel Fear of Flying. The book became famously controversial for its attitudes towards female sexuality and figured prominently in the development of second-wave feminism.", "image_url": "https://upload.wikimedia.org/wikipedia/en/0/0e/<PERSON>_-_1977.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:16.733Z", "updated_at": "2023-09-12T23:47:16.733Z", "socials": []}, {"id": 106, "name": "<PERSON><PERSON><PERSON> (Stupid Economics)", "is_fictional": false, "job": "YouTuber", "description": "<PERSON><PERSON><PERSON> (Stupid Economics) is a french YouTuber talking about economy. He's the main host on the YouTube channel \"Stupid Economics\" and does live streams on Twitch from time to time.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FArnaud (Stupid Economics)-1604564895086.png?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:16.699Z", "updated_at": "2023-09-12T23:47:16.699Z", "socials": []}, {"id": 105, "name": "Sister Mary", "is_fictional": false, "job": "Sister", "description": "Sister <PERSON> alias <PERSON><PERSON> is a fictional character played by <PERSON><PERSON> in the TV series Warrior Nun.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FToya%20Turner.jpg?alt=media&token=82e42c14-e217-4793-b9c3-e439c9d2d29c", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:16.649Z", "updated_at": "2023-09-12T23:47:16.649Z", "socials": []}, {"id": 104, "name": "<PERSON>", "is_fictional": false, "birth_date": "1977-04-10T22:00:00.000Z", "birth_location": "California, U.S.", "job": "Film producer", "description": "<PERSON> is an investor, partner, or advisor to over 20 multi-million dollar businesses. Through his popular book club and podcasts <PERSON> shares advice on how to achieve health, wealth, love, and happiness with 1.4 million people in 40 countries.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FT<PERSON> Lopez-1604528779815.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:16.616Z", "updated_at": "2023-09-12T23:47:16.616Z", "socials": []}, {"id": 103, "name": "<PERSON>", "is_fictional": false, "job": "Irish novelist", "description": "<PERSON> was an Irish novelist, playwright, short story writer, theatre director, poet, and literary translator. A resident of Paris for most of his adult life, he wrote in both French and English.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/8/88/<PERSON>_<PERSON>%2C_Pic%2C_1_%28cropped%29.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:16.585Z", "updated_at": "2023-09-12T23:47:16.585Z", "socials": []}, {"id": 102, "name": "The Cowboy", "is_fictional": true, "birth_date": "2021-03-19T23:14:02.876Z", "death_date": "2021-03-19T23:14:02.876Z", "job": "Cowboy", "description": "He gives advices and warnings to <PERSON> after he quits his job. The Cowboy is a fictional character played by the actor <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Cowboy-1619988033086.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:16.550Z", "updated_at": "2023-09-12T23:47:16.550Z", "socials": []}, {"id": 101, "name": "<PERSON><PERSON><PERSON> Boétie", "is_fictional": false, "description": "<PERSON> or <PERSON><PERSON><PERSON> was a French judge, writer and \"a founder of modern political philosophy in France\". He is best remembered as the great and close friend of the eminent essayist <PERSON> \"in one of history's most notable friendships\", as well as an earlier influence for anarchist thought. ", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:16.499Z", "updated_at": "2023-09-12T23:47:16.499Z", "socials": []}, {"id": 99, "name": "Sir <PERSON>", "is_fictional": false, "job": "Statistician", "description": "<PERSON>, Baron <PERSON>, KCB, CBE was a British statistician who made major contributions in both academia and the Civil Service.", "image_url": "https://upload.wikimedia.org/wikipedia/en/2/2d/Sir_<PERSON>_<PERSON>_in_1980.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:16.451Z", "updated_at": "2023-09-12T23:47:16.451Z", "socials": []}, {"id": 98, "name": "<PERSON><PERSON><PERSON><PERSON>", "is_fictional": false, "job": "Content producer", "description": "Video producer @nowtechtv. French but US childhood. Web video Consultant production.", "image_url": "https://pbs.twimg.com/profile_images/922876724332855296/Jdjy3xn_.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:16.403Z", "updated_at": "2023-09-12T23:47:16.403Z", "socials": []}, {"id": 96, "name": "<PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" <PERSON>", "is_fictional": true, "job": "TV host", "description": "<PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" <PERSON>, is a fictional character played by <PERSON> in the streaming TV series \"Firefly Lane\". In the show, she's a famous host of a daytime talk show known as The Girlfriend Hour.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2<PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" Hart-1614710968345.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:16.364Z", "updated_at": "2023-09-12T23:47:16.364Z", "socials": []}, {"id": 95, "name": "<PERSON>", "is_fictional": false, "job": "Author", "description": "<PERSON> is an author.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:16.308Z", "updated_at": "2023-09-12T23:47:16.308Z", "socials": []}, {"id": 94, "name": "<PERSON>", "is_fictional": false, "job": "American stand-up comedian", "description": "<PERSON> is an American stand-up comedian, author, actress, interviewer, and commentator. Beginning in the late 1980s, she performed a series of one-hour HBO comedy specials. She provided backstage commentary during the 1992 presidential election on The Tonight Show with <PERSON>.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/b/ba/PaulaPoundstoneByPhilKonstantin.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:16.240Z", "updated_at": "2023-09-12T23:47:16.240Z", "socials": []}, {"id": 93, "name": "<PERSON>", "is_fictional": false, "job": "Minister of the Interior of France", "description": "<PERSON> is a French politician who has been serving as the Minister of the Interior since 16 October 2018 and as the Executive Officer of La République En Marche! since 2017.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/8/8e/<PERSON>_<PERSON>_en_2019_%28cropped%29.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:16.206Z", "updated_at": "2023-09-12T23:47:16.206Z", "socials": []}, {"id": 91, "name": "<PERSON><PERSON>", "is_fictional": true, "job": "Prisoner", "description": "<PERSON><PERSON> is a fictional character played by <PERSON><PERSON><PERSON> in the TV series \"La Révolution\". In the show, he's a prisoner and reveals some information to <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FOka-1604355492221.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:16.162Z", "updated_at": "2023-09-12T23:47:16.162Z", "socials": []}, {"id": 90, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-11-17T21:34:36.095Z", "death_date": "2023-11-17T21:34:36.095Z", "job": "Theoretical physicist", "description": "<PERSON> was a German-born theoretical physicist who developed the theory of relativity, one of the two pillars of modern physics. His work is also known for its influence on the philosophy of science.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/3/3e/<PERSON>_1921_by_<PERSON>_<PERSON><PERSON><PERSON><PERSON>_-_restoration.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:16.105Z", "updated_at": "2023-09-12T23:47:16.105Z", "socials": []}, {"id": 89, "name": "<PERSON>", "is_fictional": false, "birth_date": "1932-10-24T00:00:00.000Z", "birth_location": "Salt Lake City, Utah, U.S.", "job": "American educator", "description": "<PERSON> was an American educator, author, businessman, and keynote speaker. His most popular book is The 7 Habits of Highly Effective People.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FStephen R. Covey-1604440890252.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:16.071Z", "updated_at": "2023-09-12T23:47:16.071Z", "socials": []}, {"id": 88, "name": "<PERSON>", "is_fictional": false, "job": "American author", "description": "<PERSON> is an American author of historical fiction, non-fiction, and screenplays.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSteven%20Pressfield.png?alt=media&token=01054716-15a5-4201-b289-9a273f0b7d4a", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:16.010Z", "updated_at": "2023-09-12T23:47:16.010Z", "socials": []}, {"id": 87, "name": "<PERSON>", "is_fictional": false, "job": "Book editor", "description": "<PERSON> is a fictional character played by <PERSON> in the film \"The Holiday\".", "image_url": "https://upload.wikimedia.org/wikipedia/commons/4/4a/<PERSON>_<PERSON>_-_Headshot.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:15.952Z", "updated_at": "2023-09-12T23:47:15.952Z", "socials": []}, {"id": 86, "name": "<PERSON>", "is_fictional": false, "job": "American director", "description": "<PERSON><PERSON> \"<PERSON>\" <PERSON> is an American director, writer, actor, and comedian whose career spans more than six decades. He began his career as a comedy writer in the 1950s, writing jokes and scripts for television and publishing several books of short humor pieces.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/8/89/<PERSON>_<PERSON>_<PERSON>_2016.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:15.916Z", "updated_at": "2023-09-12T23:47:15.916Z", "socials": []}, {"id": 85, "name": "<PERSON>", "is_fictional": true, "job": "Witch", "description": "<PERSON><PERSON><PERSON><PERSON> \"<PERSON>\" <PERSON> is a character featured in the Archie comic book <PERSON> the Teenage Witch. In the Netflix TV series, she's played by <PERSON>. <PERSON> is a full witch (as opposed to half or fully mortal) who lives in the fictional town of Greendale (in the 1990s live-action sitcom, <PERSON> lives in fictional Westbridge, Massachusetts). <PERSON> lives with her niece <PERSON>, sister <PERSON><PERSON><PERSON>, and the family cat <PERSON>, a former witch turned into a cat as punishment for his attempt at world domination.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FHilda Spellman-1610233242773.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:15.880Z", "updated_at": "2023-09-12T23:47:15.880Z", "socials": []}, {"id": 84, "name": "<PERSON>", "is_fictional": false, "job": "Game developer", "description": "<PERSON> is a french game designer, graphic designer, content creator on YouTube and Twitch. She's the author of the game Puppy Cross, a pycross game with dogs. She also gives talks at conferences.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLola_Guilldou.jpg?alt=media&token=dc99d468-45bf-4811-9a90-a738b51236a0", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:15.775Z", "updated_at": "2023-09-12T23:47:15.775Z", "socials": []}, {"id": 82, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "1627-09-26T23:50:39.000Z", "birth_location": "Dijon, France", "death_date": "1704-04-11T23:50:39.000Z", "job": "French orator", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> was a French bishop and theologian, renowned for his sermons and other addresses. He has been considered by many to be one of the most brilliant orators of all time and a masterly French stylist.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>-1611514455547.png?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:15.704Z", "updated_at": "2023-09-12T23:47:15.704Z", "socials": []}, {"id": 81, "name": "Ponce", "is_fictional": false, "birth_date": "1991-09-20T22:00:00.000Z", "birth_location": "Avignon, France", "job": "Streamer", "description": "<PERSON>, also known as the flowers king, is a french Twitch streamer. He plays various video games like Mario Kart, <PERSON><PERSON><PERSON> or Outer Wilds. On mondays, he organizes culture games with his viewers.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPonce-1615712679922.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:15.665Z", "updated_at": "2023-09-12T23:47:15.665Z", "socials": []}, {"id": 80, "name": "<PERSON><PERSON><PERSON>", "is_fictional": true, "birth_date": "2021-05-19T00:57:00.794Z", "death_date": "2021-05-19T00:57:00.794Z", "job": "<PERSON><PERSON>, Priestess", "description": "<PERSON><PERSON><PERSON> is a Speaker Magician and the Elder's granddaughter who wields powerful elemental magic. She travels with <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSypha Belnades-1621854733253.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:15.618Z", "updated_at": "2023-09-12T23:47:15.618Z", "socials": []}, {"id": 79, "name": "<PERSON><PERSON>", "is_fictional": false, "job": "American actress", "description": "<PERSON><PERSON> is an American actress, screenwriter, producer, talk show host, businesswoman, and an occasional singer-songwriter. She began her acting career in 1990, with a guest appearance on the short-lived sitcom True Colors, and subsequently starred in the television series A Different World.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/b/bd/<PERSON><PERSON>_<PERSON>_<PERSON>_at_NY_PaleyFest_2014_for_Gotham.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:15.583Z", "updated_at": "2023-09-12T23:47:15.583Z", "socials": []}, {"id": 78, "name": "Chinese Proverb", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:15.527Z", "updated_at": "2023-09-12T23:47:15.527Z", "socials": []}, {"id": 77, "name": "<PERSON>", "is_fictional": false, "job": "American travel writer", "description": "<PERSON> is an American travel writer, essayist, podcaster, and author. He has written four books, including Vagabonding, <PERSON> Didn't Go There, and Souvenir. His travel writing has appeared in National Geographic Traveler, Outside, Salon.com, Slate.com, The Guardian, and World Hum.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/e/ea/<PERSON>_<PERSON>_at_Book_Passage_in_2008.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:15.474Z", "updated_at": "2023-09-12T23:47:15.474Z", "socials": []}, {"id": 76, "name": "<PERSON>", "is_fictional": false, "job": "Writer", "description": "<PERSON> is a french wirter and author of the book \"Sous couleur de jouer\". His book analyzes video games through psychology and the \"gamification\" concept.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:15.388Z", "updated_at": "2023-09-12T23:47:15.388Z", "socials": []}, {"id": 75, "name": "<PERSON><PERSON>", "is_fictional": false, "job": "Chief Operating Officer of Facebook", "description": "<PERSON><PERSON> is an American technology executive, author, and billionaire. She is the chief operating officer of Facebook and founder of LeanIn.Org. In June 2012, she was elected to Facebook's board of directors by existing board members, becoming the first woman to serve on its board.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/9/9f/<PERSON><PERSON>_<PERSON>_2013.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:15.330Z", "updated_at": "2023-09-12T23:47:15.330Z", "socials": []}, {"id": 74, "name": "<PERSON>", "is_fictional": false, "job": "American singer-songwriter", "description": "<PERSON> is an American singer-songwriter, author, and visual artist who has been a major figure in popular culture for more than fifty years.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/0/02/<PERSON>_<PERSON>_-_Azkena_Rock_Festival_2010_2.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:15.264Z", "updated_at": "2023-09-12T23:47:15.264Z", "socials": []}, {"id": 73, "name": "<PERSON>", "is_fictional": false, "job": "Content producer", "description": "<PERSON> is a french political reporter who created Accropolis, a political news live stream. He worked with known politicians like <PERSON><PERSON>, and at \"La Ville de Paris\". Youth and popular education activist, he wants to democratize politics to help people better understand the news. He's also passionate about video games and web culture.", "image_url": "https://accropolis.fr/wp-content/uploads/2017/03/009-_DSC7280-150dpi-150x150.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:15.216Z", "updated_at": "2023-09-12T23:47:15.216Z", "socials": []}, {"id": 71, "name": "Special Agent <PERSON>", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:15.181Z", "updated_at": "2023-09-12T23:47:15.181Z", "socials": []}, {"id": 70, "name": "<PERSON>", "is_fictional": false, "job": "Founder of IBM", "description": "<PERSON> was an American businessman. He served as the chairman and CEO of International Business Machines. He oversaw the company's growth into an international force from 1914 to 1956. <PERSON> developed IBM's management style and corporate culture from <PERSON>'s training at NCR.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/7/7e/<PERSON>_<PERSON>_<PERSON>_<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:15.132Z", "updated_at": "2023-09-12T23:47:15.132Z", "socials": []}, {"id": 69, "name": "<PERSON>", "is_fictional": false, "job": "Student", "description": "<PERSON> is a fictional character played by <PERSON><PERSON><PERSON> in the TV series Euphor<PERSON>. In the series, she's a recovering teenage drug addict struggling to find her place in the world.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fzendaya.jpg?alt=media&token=2acb3a12-989c-4e15-8f89-788de8cfdf99", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:15.100Z", "updated_at": "2023-09-12T23:47:15.100Z", "socials": []}, {"id": 67, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "job": "Dutch graphic artist", "description": "<PERSON><PERSON><PERSON> was a Dutch graphic artist who made mathematically inspired woodcuts, lithographs, and mezzotints. Despite wide popular interest, <PERSON><PERSON> was for long somewhat neglected in the art world, even in his native Netherlands. He was 70 before a retrospective exhibition was held.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/4/4d/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:15.063Z", "updated_at": "2023-09-12T23:47:15.063Z", "socials": []}, {"id": 66, "name": "<PERSON>", "is_fictional": false, "job": "American academic", "description": "<PERSON> is an American academic and video game designer. He holds a joint professorship in the School of Literature, Media, and Communication and in Interactive Computing in the College of Computing at the Georgia Institute of Technology, where he is the <PERSON> of Liberal Arts Distinguished Chair in Media Studies.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/e/e0/Ibogost_joystick_color_2.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:14.992Z", "updated_at": "2023-09-12T23:47:14.992Z", "socials": []}, {"id": 64, "name": "<PERSON>", "is_fictional": false, "birth_date": "1945-02-05T23:00:00.000Z", "birth_location": "Nine Mile, Jamaïca", "death_date": "1981-05-10T22:00:00.000Z", "job": "Jamaican singer-songwriter", "description": "<PERSON>, <PERSON><PERSON> was a Jamaican singer, songwriter, and musician. Considered one of the pioneers of reggae, his musical career was marked by fusing elements of reggae, ska, and rocksteady, as well as his distinctive vocal and songwriting style.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBob <PERSON>-1611506288364.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:14.934Z", "updated_at": "2023-09-12T23:47:14.934Z", "socials": []}, {"id": 63, "name": "<PERSON>", "is_fictional": false, "job": "Student", "description": "<PERSON> is a fictional character in the Daybreak TV series, where she is a 10 year old genius with mild pyromania.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:14.879Z", "updated_at": "2023-09-12T23:47:14.879Z", "socials": []}, {"id": 62, "name": "<PERSON>", "is_fictional": false, "job": "Commissioner", "description": "Commissaire <PERSON> is a fictional character in the french movie \"La Cité de la peur\" played by <PERSON><PERSON>.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/b/b7/G%C3%A9rard_Darmon_Cannes_2011.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:14.841Z", "updated_at": "2023-09-12T23:47:14.841Z", "socials": []}, {"id": 61, "name": "<PERSON>", "is_fictional": true, "job": "Astronaut", "description": "<PERSON> is a fictional character played by <PERSON> in the TV series Away. In the show, she's a NASA astronaut who is commander of the mission and ship Atlas.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FEmma Green-1607786750446.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:14.791Z", "updated_at": "2023-09-12T23:47:14.791Z", "socials": []}, {"id": 59, "name": "Father", "is_fictional": true, "job": "Retired", "description": "<PERSON> is an English actor (among other jobs) playing <PERSON>'s father in the film \"i'm thinking of ending things\".", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FFather-1604561169043.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:14.757Z", "updated_at": "2023-09-12T23:47:14.757Z", "socials": []}, {"id": 56, "name": "<PERSON>", "is_fictional": false, "job": "English author", "description": "<PERSON> was an English author, screenwriter, essayist, humorist, satirist and dramatist. <PERSON> was author of The Hitchhiker's Guide to the Galaxy, which originated in 1978 as a BBC radio comedy before developing into a \"trilogy\" of five books that sold more than 15 million copies in his lifetime and generated a television series, several stage plays, comics, a video game, and in 2005 a feature film.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/c/c0/<PERSON>_<PERSON><PERSON>_portrait_cropped.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:14.701Z", "updated_at": "2023-09-12T23:47:14.701Z", "socials": []}, {"id": 55, "name": "<PERSON>", "is_fictional": false, "job": "Filmmaker", "description": "<PERSON> is a British-American filmmaker known for making personal, distinctive films within the Hollywood mainstream. His directorial efforts have grossed more than US$5 billion worldwide, garnered 34 Oscar nominations and ten wins.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FChristopher_Nolan_Cannes_2018.jpg?alt=media&token=0c1ee563-d88c-478e-9409-1c1423db97fd", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:14.649Z", "updated_at": "2023-09-12T23:47:14.649Z", "socials": []}, {"id": 54, "name": "The Nerdwriter", "is_fictional": false, "job": "YouTube Channel", "description": "The Nerdwriter is a weekly video essay series that puts ideas to work.", "image_url": "https://yt3.ggpht.com/a/AGF-l7_hJSGF0JQvluUmAEs9tCstkd9SX47z4BetoQ=s288-c-k-c0xffffffff-no-rj-mo", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:14.595Z", "updated_at": "2023-09-12T23:47:14.595Z", "socials": []}, {"id": 53, "name": "Rosa <PERSON>", "is_fictional": false, "job": "American activist", "description": "<PERSON> was an American activist in the civil rights movement best known for her pivotal role in the Montgomery bus boycott. The United States Congress has called her \"the first lady of civil rights\" and \"the mother of the freedom movement\".", "image_url": "https://upload.wikimedia.org/wikipedia/commons/c/c4/Rosaparks.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:14.561Z", "updated_at": "2023-09-12T23:47:14.561Z", "socials": []}, {"id": 52, "name": "<PERSON><PERSON>", "is_fictional": false, "job": "Actress", "description": "<PERSON><PERSON> was a French actress. She appeared in over 125 films since 1961. Among these were five films directed by <PERSON>: Last Year at Marienbad, Muriel, The War Is Over, I Want to Go Home, and <PERSON> Old Song. Born in Paris on 23 September 1925, she died in Galan, Hautes-Pyrénées, on 26 October 2014.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:14.510Z", "updated_at": "2023-09-12T23:47:14.510Z", "socials": []}, {"id": 51, "name": "<PERSON>", "is_fictional": false, "job": "American novelist", "description": "<PERSON> is an American novelist and non-fiction writer. She is also a progressive political activist, public speaker, and writing teacher. <PERSON><PERSON> is based in Marin County, California. Her nonfiction works are largely autobiographical.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/a/a8/<PERSON>-<PERSON>-2013-San-Francisco.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:14.475Z", "updated_at": "2023-09-12T23:47:14.475Z", "socials": []}, {"id": 50, "name": "<PERSON>", "is_fictional": true, "job": "Boyfriend", "description": "<PERSON> is a fictional character played by <PERSON> in the film \"i'm thinking of ending things\". He's <PERSON>'s boyfriend and plan to go visit his parent on their farm.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FJake-1604539864576.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:14.426Z", "updated_at": "2023-09-12T23:47:14.426Z", "socials": []}, {"id": 49, "name": "<PERSON>", "is_fictional": false, "job": "American travel writer", "description": "<PERSON> is an American travel writer and novelist, whose best-known work is The Great Railway Bazaar. He has published numerous works of fiction, some of which were adapted as feature films.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/f/f6/<PERSON><PERSON><PERSON><PERSON>_2008Sep.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:14.394Z", "updated_at": "2023-09-12T23:47:14.394Z", "socials": []}, {"id": 47, "name": "<PERSON>", "is_fictional": false, "description": "<PERSON> is a fictional character which is a victim of sexual assault in the American miniseries Unbelievable.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:14.360Z", "updated_at": "2023-09-12T23:47:14.360Z", "socials": []}, {"id": 46, "name": "<PERSON><PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "1929-02-24T00:00:00.000Z", "birth_location": "Sanok, Poland", "death_date": "2005-02-20T23:00:00.000Z", "job": "Polish painter", "description": "<PERSON><PERSON><PERSON><PERSON> was a Polish painter, photographer and sculptor, specializing in the field of dystopian surrealism. <PERSON><PERSON><PERSON><PERSON> made his paintings and drawings in what he called either a 'Baroque' or a 'Gothic' manner. His creations were made mainly in two periods.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON><PERSON><PERSON>-1607786185626.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:14.308Z", "updated_at": "2023-09-12T23:47:14.308Z", "socials": []}, {"id": 45, "name": "<PERSON>", "is_fictional": false, "job": "Greek philosopher", "description": "<PERSON> was a Greek philosopher and polymath during the Classical period in Ancient Greece. He was the founder of the Lyceum and the Peripatetic school of philosophy and Aristotelian tradition. Along with his teacher <PERSON>, he has been called the \"Father of Western Philosophy\".", "image_url": "https://upload.wikimedia.org/wikipedia/commons/a/ae/<PERSON>_<PERSON>emps_Inv8575.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:14.254Z", "updated_at": "2023-09-12T23:47:14.254Z", "socials": []}, {"id": 44, "name": "<PERSON><PERSON>", "is_fictional": false, "description": "Also known professionally as <PERSON><PERSON><PERSON><PERSON>, he is an American YouTuber, best known for his technology-focused videos.", "image_url": "https://yt3.ggpht.com/a/AGF-l79MlkJGCbk2bScrh1VjeXy1UG59DMs3ZncjCw=s288-c-k-c0xffffffff-no-rj-mo", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:14.206Z", "updated_at": "2023-09-12T23:47:14.206Z", "socials": []}, {"id": 43, "name": "<PERSON>rah Winfrey", "is_fictional": false, "job": "American executive", "description": "<PERSON><PERSON> is an American media executive, actress, talk show host, television producer, and philanthropist.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/b/bf/Oprah_in_2014.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:14.159Z", "updated_at": "2023-09-12T23:47:14.159Z", "socials": []}, {"id": 42, "name": "<PERSON>", "is_fictional": false, "job": "Explorer", "description": "<PERSON> was an Italian explorer and colonizer who completed four voyages across the Atlantic Ocean that opened the New World for conquest and permanent European colonization of the Americas.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/c/c2/Portrait_of_a_Man%2C_Said_to_be_<PERSON>_<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:14.112Z", "updated_at": "2023-09-12T23:47:14.112Z", "socials": []}, {"id": 41, "name": "<PERSON>", "is_fictional": false, "job": "Businessman", "description": "<PERSON> if a fictional character played by <PERSON> in the film \"Meet Joe Black\".", "image_url": "https://upload.wikimedia.org/wikipedia/commons/4/47/AnthonyHopkins10TIFF.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:14.065Z", "updated_at": "2023-09-12T23:47:14.065Z", "socials": []}, {"id": 39, "name": "La chaîne de P.A.U.L", "is_fictional": false, "job": "YouTuber", "description": "La chaîne de P.A.U.L is a french YouTube channel making documentary about celebrities or fictional characters like the Joker, <PERSON> and <PERSON><PERSON><PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLa_chaine_de_paul.jpg?alt=media&token=bfc134c8-ac42-49d2-97d2-8653a42044bc", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:14.027Z", "updated_at": "2023-09-12T23:47:14.027Z", "socials": []}, {"id": 38, "name": "<PERSON><PERSON>", "is_fictional": false, "job": "Projectionist", "description": "<PERSON><PERSON> is a fictional character from the french movie \"La Cité de la peur\", played by <PERSON>.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/2/20/<PERSON>_<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:13.977Z", "updated_at": "2023-09-12T23:47:13.977Z", "socials": []}, {"id": 36, "name": "<PERSON>", "is_fictional": false, "job": "American professor", "description": "<PERSON> was an American professor of literature at Sarah Lawrence College who worked in comparative mythology and comparative religion. His work covers many aspects of the human experience.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON><PERSON>-1699451662031.png?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:13.921Z", "updated_at": "2023-09-12T23:47:13.921Z", "socials": []}, {"id": 34, "name": "<PERSON>", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:13.874Z", "updated_at": "2023-09-12T23:47:13.874Z", "socials": []}, {"id": 32, "name": "<PERSON>", "is_fictional": false, "birth_date": "1533-02-27T23:00:00.000Z", "birth_location": "Château de Montaigne, France", "death_date": "1592-09-12T22:00:00.000Z", "job": "Philosopher", "description": "<PERSON>, Lord of Montaigne was one of the most significant philosophers of the French Renaissance, known for popularizing the essay as a literary genre. His work is noted for its merging of casual anecdotes and autobiography with intellectual insight.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMichel de Montaigne-1690038560262.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:13.814Z", "updated_at": "2023-09-12T23:47:13.814Z", "socials": []}, {"id": 31, "name": "<PERSON>", "is_fictional": false, "job": "American historian", "description": "<PERSON>, Jr. was an American cultural and literary historian, author and university professor. His writings cover a variety of topics, from scholarly works on eighteenth-century English literature to commentary on America's class system.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/6/6c/Pfussell1945.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:13.765Z", "updated_at": "2023-09-12T23:47:13.765Z", "socials": []}, {"id": 30, "name": "<PERSON>", "is_fictional": false, "job": "Author", "description": "<PERSON> is an exoneree, journalist, public speaker, and author of the New York Times best-selling memoir, <PERSON> to <PERSON> (HarperCollins, April 2013). Between 2007 and 2015, she spent nearly four years in an Italian prison and eight years on trial for a murder she didn’t commit. The controversy over <PERSON>’s case made international headlines for nearly a decade, and thrust her into the spotlight, where she was vilified and shamed. <PERSON> now works to shed light on the issues of wrongful conviction, truth seeking, and public shaming, and to inspire people towards empathy and perspective.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/7/72/<PERSON>_<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:13.717Z", "updated_at": "2023-09-12T23:47:13.717Z", "socials": []}, {"id": 29, "name": "<PERSON> Longfellow", "is_fictional": false, "birth_date": "2021-04-22T21:19:52.447Z", "death_date": "2021-04-22T21:19:52.447Z", "job": "American poet", "description": "<PERSON> was an American poet and educator whose works include \"<PERSON>'s Ride\", The Song of Hiawatha, and <PERSON><PERSON><PERSON>. He was the first American to translate <PERSON>'s Divine Comedy and was one of the Fireside Poets from New England. ", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FH<PERSON>ry Wadsworth Longfellow-1619986453240.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:13.670Z", "updated_at": "2023-09-12T23:47:13.670Z", "socials": []}, {"id": 28, "name": "<PERSON> \"<PERSON>\" <PERSON>", "is_fictional": false, "job": "Student", "description": "A graduate student in psychology and escort girl in the TV series You Me Her.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/9/93/<PERSON><PERSON>cilla_Faia_at_TIFF_2014.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:13.607Z", "updated_at": "2023-09-12T23:47:13.607Z", "socials": []}, {"id": 27, "name": "<PERSON><PERSON>", "is_fictional": false, "job": "Game designer", "description": "<PERSON><PERSON> is a Danish game designer, educator, and theorist in the field of video game studies. He is an associate professor at the Danish Design School. <PERSON><PERSON> is co-editor, with <PERSON> and <PERSON>, of the MIT Press Playful Thinking series.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FJesper%20Juul.jpg?alt=media&token=249a4e18-ddf1-4f35-b66a-015c1a0ecc86", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:13.545Z", "updated_at": "2023-09-12T23:47:13.545Z", "socials": []}, {"id": 26, "name": "<PERSON>", "is_fictional": false, "job": "Spanish painter", "description": "<PERSON> was a Spanish painter, sculptor, printmaker, ceramicist, stage designer, poet and playwright who spent most of his adult life in France.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/b/b8/Portrait_de_<PERSON>%2C_1908.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:13.494Z", "updated_at": "2023-09-12T23:47:13.494Z", "socials": []}, {"id": 25, "name": "Dr. <PERSON>", "is_fictional": true, "birth_date": "2021-05-22T22:59:00.974Z", "death_date": "2021-05-22T22:59:00.974Z", "job": "Psychiatrist", "description": "Dr. <PERSON> is <PERSON>'s psychiatrist in the film The Woman in the Window. He's played by <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDr. Karl Landy-1621853816386.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:13.462Z", "updated_at": "2023-09-12T23:47:13.462Z", "socials": []}, {"id": 24, "name": "Claus<PERSON>", "is_fictional": true, "job": "investigator", "description": "<PERSON><PERSON> is a fictional character played by <PERSON> in the TV series \"Dark\". He arrives in the second season as an investigator assisting <PERSON> and the police force.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FClausen-1604581865260.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:13.402Z", "updated_at": "2023-09-12T23:47:13.402Z", "socials": []}, {"id": 23, "name": "<PERSON><PERSON>", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:13.352Z", "updated_at": "2023-09-12T23:47:13.352Z", "socials": []}, {"id": 22, "name": "Dirty Biology", "is_fictional": false, "description": "Science videos on mind fuck and fun subjects.", "image_url": "https://yt3.ggpht.com/a/AGF-l7-kRfn9w-2DZ4PwoWr1glE_ZqweeedJWTM-Tw=s288-c-k-c0xffffffff-no-rj-mo", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:13.285Z", "updated_at": "2023-09-12T23:47:13.285Z", "socials": []}, {"id": 21, "name": "<PERSON>", "is_fictional": false, "job": "Prosecutor", "description": "<PERSON> is a fictional character played by <PERSON> in the TV miniseries The Outsider. He is a legal prosecutor.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMichael-Esper.jpg?alt=media&token=bd47e45e-6ebe-4dfd-bb2c-78c8a90f4814", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:13.231Z", "updated_at": "2023-09-12T23:47:13.231Z", "socials": []}, {"id": 20, "name": "<PERSON>", "is_fictional": false, "job": "American singer", "description": "<PERSON> was an American singer, actor and producer who was one of the most popular and influential musical artists of the 20th century. He is one of the best-selling music artists of all time, having sold more than 150 million records worldwide.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/a/af/<PERSON>_<PERSON>_%2757.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:13.183Z", "updated_at": "2023-09-12T23:47:13.183Z", "socials": []}, {"id": 19, "name": "<PERSON>", "is_fictional": false, "job": "Australian philosopher", "description": "<PERSON> is an Australian moral philosopher. He is the <PERSON> Professor of Bioethics at Princeton University, and a Laureate Professor at the Centre for Applied Philosophy and Public Ethics at the University of Melbourne.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPeter_Singer.jpg?alt=media&token=8f098f79-670e-4f14-809f-2e7fde4705f6", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:13.125Z", "updated_at": "2023-09-12T23:47:13.125Z", "socials": []}, {"id": 18, "name": "<PERSON>", "is_fictional": false, "job": "Lawyer", "description": "<PERSON> is a fictional character played by <PERSON> in the TV miniseries The Outsider. He's the lawyer of <PERSON>.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/4/47/<PERSON>_<PERSON>_%2851526%29.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:13.074Z", "updated_at": "2023-09-12T23:47:13.074Z", "socials": []}, {"id": 17, "name": "<PERSON>", "is_fictional": true, "job": "Executive assistant", "description": "<PERSON> is a fictional character in the french book \"Le parfum du bonheur est plus fort sous la pluie\" having difficulties with her life. She went back to live with her parents with her child after some events with her husband.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:13.005Z", "updated_at": "2023-09-12T23:47:13.005Z", "socials": []}, {"id": 16, "name": "Achilles (<PERSON><PERSON><PERSON>)", "is_fictional": true, "job": "Greek hero", "description": "In Greek mythology, <PERSON> or <PERSON><PERSON><PERSON><PERSON> was a hero of the Trojan War, the greatest of all the Greek warriors, and is the central character of <PERSON>'s Ilia<PERSON>. He was the son of the <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, king of Phthia.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAchilles (Achille)-1610059865454.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:12.970Z", "updated_at": "2023-09-12T23:47:12.970Z", "socials": []}, {"id": 15, "name": "<PERSON>", "is_fictional": false, "job": "American author", "description": "<PERSON> is an American author, life coach, and philanthropist. <PERSON> is known for his infomercials, seminars, and self-help books including the books Unlimited Power and Awaken the Giant Within. In 2015 and 2016 <PERSON> was listed on the Worth Magazine Power 100 list.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/5/5e/<PERSON>_<PERSON>.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:12.770Z", "updated_at": "2023-09-12T23:47:12.770Z", "socials": []}, {"id": 12, "name": "<PERSON>", "is_fictional": false, "job": "American author", "description": "<PERSON> was an American author, political activist, and lecturer. She was the first deaf-blind person to earn a Bachelor of Arts degree.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/3/3e/<PERSON>_<PERSON>_circa_1920_-_restored.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:12.472Z", "updated_at": "2023-09-12T23:47:12.472Z", "socials": []}, {"id": 11, "name": "<PERSON>", "is_fictional": false, "birth_date": "1875-07-27T23:50:39.000Z", "birth_location": "Kesswil, Switzerland", "death_date": "1961-06-05T23:00:00.000Z", "job": "Swiss psychiatrist", "description": "<PERSON>, originally <PERSON>, was a Swiss psychiatrist and psychoanalyst who founded analytical psychology. <PERSON>'s work has been influential in the fields of psychiatry, anthropology, archaeology, literature, philosophy, and religious studies.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FC<PERSON><PERSON>-1610233757176.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:12.125Z", "updated_at": "2023-09-12T23:47:12.125Z", "socials": []}, {"id": 10, "name": "<PERSON>", "is_fictional": false, "birth_date": "1961-01-25T23:00:00.000Z", "birth_location": "Brantford, Ontario, Canada", "death_date": "2021-03-20T16:34:51.887Z", "job": "Canadian ice hockey player", "description": "<PERSON> is a Canadian former professional ice hockey player and former head coach. He played 20 seasons in the National Hockey League for four teams from 1979 to 1999.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FWayne Gretzky-1616258451974.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:12.086Z", "updated_at": "2023-09-12T23:47:12.086Z", "socials": []}, {"id": 9, "name": "<PERSON>", "is_fictional": true, "job": "Scientist", "description": "<PERSON> is a fictional character played by <PERSON> in the film \"i'm thinking of ending things\". She's new <PERSON>'s girlfriend and takes a trip with him to meet his parents.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLucy-1604540417962.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:12.021Z", "updated_at": "2023-09-12T23:47:12.021Z", "socials": []}, {"id": 8, "name": "<PERSON><PERSON> (YouTube)", "is_fictional": false, "image_url": "https://yt3.ggpht.com/a/AGF-l79n-Shx5iu8LuMHoYIrMcYwLCe5sJBPhr83Jw=s288-c-k-c0xffffffff-no-rj-mo", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:11.969Z", "updated_at": "2023-09-12T23:47:11.969Z", "socials": []}, {"id": 6, "name": "<PERSON><PERSON><PERSON>", "is_fictional": true, "job": "Child", "description": "<PERSON><PERSON><PERSON> is a fictional character in the TV series \"La Révolution\" and played by <PERSON><PERSON><PERSON>. She's <PERSON>'s little sister and has the particularity to be muted. She often has nightmares.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMadelaine de Montargis-1603585384266.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:11.916Z", "updated_at": "2023-09-12T23:47:11.916Z", "socials": []}, {"id": 5, "name": "<PERSON><PERSON> (<PERSON><PERSON>)", "is_fictional": false, "description": "<PERSON><PERSON> animates the french podcast Paumé_e_s.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:11.861Z", "updated_at": "2023-09-12T23:47:11.861Z", "socials": []}, {"id": 4, "name": "<PERSON>", "is_fictional": false, "job": "Student", "description": "<PERSON> is a fictional character in Daybreak TV series, where he's a lone survivor seeking his girlfriend in the post-apocalyptic landscape.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:11.802Z", "updated_at": "2023-09-12T23:47:11.802Z", "socials": []}, {"id": 3, "name": "<PERSON>", "is_fictional": false, "job": "Video producer", "description": "<PERSON> is a YouTube creator. He talks about life improvement, work optimisation and social among other topics.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FJ<PERSON><PERSON> Schweitzer-1603630075546.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:11.740Z", "updated_at": "2023-09-12T23:47:11.740Z", "socials": []}, {"id": 2, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "1981-12-01T23:00:00.000Z", "birth_location": "Mississipi, U.S.", "job": "American singer-songwriter", "description": "<PERSON><PERSON><PERSON> is an American singer, songwriter, dancer, and actress. She is credited with influencing the revival of teen pop during the late 1990s and early 2000s, for which she is referred to as the \"Princess of Pop\".", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBrit<PERSON> Spears-1604538125856.jpg?alt=media", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:11.673Z", "updated_at": "2023-09-12T23:47:11.673Z", "socials": []}, {"id": 1, "name": "<PERSON><PERSON>cius", "is_fictional": false, "job": "Chinese philosopher", "description": "<PERSON><PERSON><PERSON> was a Chinese philosopher and politician of the Spring and Autumn period. The philosophy of <PERSON><PERSON><PERSON>, also known as Confucianism, emphasized personal and governmental morality, correctness of social relationships, justice, kindness, and sincerity.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/5/54/Confucius_Tang_Dynasty.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2023-09-12T23:47:11.603Z", "updated_at": "2023-09-12T23:47:11.603Z", "socials": []}]