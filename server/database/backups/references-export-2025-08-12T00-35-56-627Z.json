[{"id": 318, "name": "Disco Elysium", "primary_type": "video_game", "secondary_type": "C-RPG", "views_count": 0, "likes_count": 0, "created_at": "2025-08-11 14:57:44", "updated_at": "2025-08-11 15:56:29", "urls": []}, {"id": 317, "name": "<PERSON><PERSON><PERSON>, l'Univers dévoilé", "primary_type": "documentary", "secondary_type": "Sciences", "views_count": 0, "likes_count": 0, "created_at": "2025-08-11 14:52:13", "updated_at": "2025-08-11 15:56:58", "urls": []}, {"id": 316, "name": "The Old Guard", "primary_type": "film", "secondary_type": "Action, Sciences-Fiction", "views_count": 0, "likes_count": 0, "created_at": "2025-08-11 14:37:49", "updated_at": "2025-08-11 15:57:22", "urls": []}, {"id": 315, "name": "Final Space", "primary_type": "tv_series", "secondary_type": "Science-Fiction, Space", "views_count": 0, "likes_count": 0, "created_at": "2025-08-11 14:32:56", "updated_at": "2025-08-11 15:57:38", "urls": []}, {"id": 314, "name": "Les carnets secrets d'une fille de joie", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2025-08-11 14:09:42", "updated_at": "2025-08-11 14:09:42", "urls": []}, {"id": 313, "name": "<PERSON>", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2025-08-11 14:09:12", "updated_at": "2025-08-11 14:09:12", "urls": []}, {"id": 312, "name": "The Suicide of <PERSON>", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2025-08-11 14:07:26", "updated_at": "2025-08-11 14:07:26", "urls": []}, {"id": 311, "name": "Dr Who", "primary_type": "tv_series", "secondary_type": "Science-Fiction", "views_count": 0, "likes_count": 0, "created_at": "2025-08-11 14:06:08", "updated_at": "2025-08-11 15:57:52", "urls": []}, {"id": 310, "name": "<PERSON> and <PERSON>", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2025-08-11 14:05:36", "updated_at": "2025-08-11 14:05:36", "urls": []}, {"id": 309, "name": "Le podcast de l'entrepreneur", "primary_type": "podcast", "description": "Conseils, Astuces, News, Soutien pour les entrepreneurs. Les episodes sont en francais. Le Podcast de l'entrepreneur est une solution très productive qui vous fera gagner du temps dans votre journée chargée. En effet, lorsque l’on est dans les temps d’attente ou les temps morts (voiture, embouteillages, métro, train…) pour aller travail le podcast est la solution pour continuer d’apprendre, de se former, d’accéder à des informations.\n\nhttps://pca.st/5VM8", "views_count": 0, "likes_count": 0, "created_at": "2025-08-11 13:59:45", "updated_at": "2025-08-11 14:04:39", "urls": []}, {"id": 306, "name": "No et Moi", "primary_type": "other", "release_date": "2024-06-13", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 307, "name": "Procès VA-OM", "primary_type": "other", "release_date": "2024-06-13", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 308, "name": "Épigraphe du Révisor", "primary_type": "other", "release_date": "2023-09-05", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 285, "name": "Journal", "primary_type": "other", "release_date": "2024-06-13", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 286, "name": "It's a Wonderful Life", "primary_type": "other", "release_date": "2024-01-31", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 287, "name": "Fleming: The Man Who Would Be Bond", "primary_type": "other", "release_date": "2024-09-14", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 288, "name": "Radio France", "primary_type": "other", "release_date": "2024-08-09", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 289, "name": "Horses", "primary_type": "other", "description": "Horses is a YouTube channel about philosophy, art and reflections.", "release_date": "2024-02-21", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 290, "name": "Lettre à Laza", "primary_type": "other", "release_date": "2023-08-28", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 291, "name": "Le Grand Livre De L'humour Noir", "primary_type": "other", "release_date": "2024-04-21", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 292, "name": "Dans ces années-là (2008)", "primary_type": "other", "release_date": "2024-06-13", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 293, "name": "Meditations", "primary_type": "other", "release_date": "2024-02-20", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 294, "name": "<PERSON>", "primary_type": "other", "release_date": "2024-01-28", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 295, "name": "Secret Story", "primary_type": "other", "release_date": "2024-05-13", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 296, "name": "Alice aux pays des merveilles", "primary_type": "other", "release_date": "2023-08-28", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 297, "name": "<PERSON>", "primary_type": "other", "release_date": "2024-01-13", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 298, "name": "Le Figaro Littéraire", "primary_type": "other", "release_date": "1951-06-29", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 299, "name": "Les filles du feu", "primary_type": "other", "release_date": "2024-06-13", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 300, "name": "The Killer", "primary_type": "other", "release_date": "2023-12-12", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 301, "name": "Une vie", "primary_type": "other", "release_date": "2024-06-13", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 302, "name": "L'Avant-Scène n°303/304", "primary_type": "other", "release_date": "1983-09-04", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 303, "name": "<PERSON>", "primary_type": "other", "release_date": "2024-04-27", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 304, "name": "Les Disparus De Bas-Vourlans", "primary_type": "other", "release_date": "2024-06-20", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 305, "name": "Mille et une pensées (2005)", "primary_type": "other", "release_date": "2024-06-13", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 282, "name": "Spider-<PERSON>: Into the Spider-verse", "primary_type": "other", "release_date": "2023-11-16", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:17", "updated_at": "2025-08-08 13:02:17", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 283, "name": "Leave The World Behind", "primary_type": "other", "release_date": "2023-12-11", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:17", "updated_at": "2025-08-08 13:02:17", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 284, "name": "<PERSON><PERSON><PERSON>", "primary_type": "other", "release_date": "2024-04-23", "views_count": 0, "likes_count": 0, "created_at": "2025-08-08 13:02:17", "updated_at": "2025-08-08 13:02:17", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 260, "name": "The screenwriter looks at the screenwriter", "primary_type": "other", "release_date": "1972-07-30", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 261, "name": "Notes pour trop tard", "primary_type": "other", "release_date": "2016-12-31", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 262, "name": "The craft of the screenwriter", "primary_type": "other", "release_date": "1981-07-25", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 263, "name": "Premier placet du Tartuffe", "primary_type": "other", "release_date": "1664-07-26", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 264, "name": "Detroit: Become Human", "primary_type": "other", "release_date": "2023-03-18", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 265, "name": "Les sciences face aux créationnismes", "primary_type": "other", "release_date": "2023-07-29", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 266, "name": "Re:zero", "primary_type": "other", "release_date": "2021-06-13", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 267, "name": "Apollo 13", "primary_type": "other", "release_date": "2024-06-07", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 268, "name": "Fireship", "primary_type": "other", "release_date": "2023-05-20", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 269, "name": "Peur de l'échec", "primary_type": "other", "release_date": "2008-12-31", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 270, "name": "Les Cahiers de la Bande Dessinée n°22", "primary_type": "other", "release_date": "1973-07-30", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 271, "name": "<PERSON><PERSON><PERSON>", "primary_type": "other", "release_date": "2010-12-31", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 272, "name": "À l'heure où je me couche", "primary_type": "other", "release_date": "2021-10-24", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 273, "name": "The Mozilla Blog", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 274, "name": "Le pouvoir des fables", "primary_type": "other", "release_date": "1668-07-30", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 275, "name": "Death proof, <PERSON>", "primary_type": "other", "release_date": "2024-07-21", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 276, "name": "De la poésie dramatique", "primary_type": "other", "release_date": "1981-07-25", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 277, "name": "Construire un récit", "primary_type": "other", "release_date": "2023-07-26", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 278, "name": "San (La fête est finie)", "primary_type": "other", "release_date": "2016-12-31", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 279, "name": "Star Wars A new hope", "primary_type": "other", "release_date": "2024-06-07", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 280, "name": "<PERSON><PERSON><PERSON>", "primary_type": "other", "release_date": "2022-05-17", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 281, "name": "L'Île des chasseurs d'oiseaux", "primary_type": "other", "release_date": "2023-07-01", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:49", "updated_at": "2025-08-03 05:59:49", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 234, "name": "Predestination", "primary_type": "other", "release_date": "2021-09-04", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 235, "name": "The Endless", "primary_type": "other", "release_date": "2022-12-08", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 236, "name": "Blast", "primary_type": "other", "release_date": "2023-03-23", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 237, "name": "Quand l'histoire fait date", "primary_type": "other", "release_date": "2023-02-16", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 238, "name": "The woman who lived across the street", "primary_type": "other", "release_date": "2022-01-30", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 239, "name": "MADiSON", "primary_type": "other", "description": "MADiSON is a first person psychological horror game that delivers an immersive and terrifying experience. With the help of an instant camera, connect the human world with the beyond, take pictures and develop them by yourself.", "release_date": "2022-07-06", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 240, "name": "Atypical", "primary_type": "other", "release_date": "2021-07-15", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 241, "name": "Mr. <PERSON>'s Phone", "primary_type": "other", "release_date": "2023-02-10", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 242, "name": "Fight Club", "primary_type": "other", "release_date": "2022-02-20", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 243, "name": "L'art poétique", "primary_type": "other", "release_date": "1674-07-30", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 244, "name": "L'opinion publique", "primary_type": "other", "release_date": "2023-07-26", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 245, "name": "La poétique", "primary_type": "other", "release_date": "1852-07-25", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 246, "name": "<PERSON><PERSON>, Romance sans paroles", "primary_type": "other", "release_date": "2024-06-13", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 247, "name": "Jupiter's Legacy", "primary_type": "other", "release_date": "2021-05-11", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 248, "name": "Don't Look Up", "primary_type": "other", "release_date": "2023-03-22", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 249, "name": "<PERSON><PERSON>", "primary_type": "other", "release_date": "2021-11-21", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 250, "name": "The Curious Case of <PERSON>", "primary_type": "other", "release_date": "2025-01-06", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 251, "name": "Kung fu panda", "primary_type": "other", "release_date": "2023-07-27", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 252, "name": "À l'heure où j'me couche", "primary_type": "other", "release_date": "2014-12-31", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 253, "name": "Dispatch from Elsewhere", "primary_type": "other", "release_date": "2022-11-12", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 254, "name": "The Shape of Water", "primary_type": "other", "release_date": "2023-03-13", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 255, "name": "Le Tartuffe", "primary_type": "other", "release_date": "2023-07-26", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 256, "name": "L'île des Chasseurs d'oiseaux", "primary_type": "other", "release_date": "2023-07-03", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 257, "name": "Correspondances", "primary_type": "other", "release_date": "1852-07-25", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 258, "name": "Midnight Mass", "primary_type": "other", "release_date": "2021-09-29", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 259, "name": "The Prestige", "primary_type": "other", "release_date": "2022-02-19", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:48", "updated_at": "2025-08-03 05:59:48", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 223, "name": "Pantheon", "primary_type": "other", "release_date": "2023-06-02", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:47", "updated_at": "2025-08-03 05:59:47", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 224, "name": "Things heard and seen", "primary_type": "other", "release_date": "2021-05-29", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:47", "updated_at": "2025-08-03 05:59:47", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 225, "name": "Le gai savoir", "primary_type": "other", "release_date": "1882-07-31", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:47", "updated_at": "2025-08-03 05:59:47", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 226, "name": "Wednesday", "primary_type": "other", "release_date": "2023-01-14", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:47", "updated_at": "2025-08-03 05:59:47", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 227, "name": "Into the Night", "primary_type": "other", "release_date": "2021-09-19", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:47", "updated_at": "2025-08-03 05:59:47", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 228, "name": "MaleVolent", "primary_type": "other", "release_date": "2023-01-26", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:47", "updated_at": "2025-08-03 05:59:47", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 229, "name": "The Sinner", "primary_type": "other", "release_date": "2023-03-11", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:47", "updated_at": "2025-08-03 05:59:47", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 230, "name": "Downsizing", "primary_type": "other", "release_date": "2023-02-02", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:47", "updated_at": "2025-08-03 05:59:47", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 231, "name": "Les Revues du Monde", "primary_type": "other", "release_date": "2023-02-10", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:47", "updated_at": "2025-08-03 05:59:47", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 232, "name": "The Midnight Club", "primary_type": "other", "release_date": "2022-10-11", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:47", "updated_at": "2025-08-03 05:59:47", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 233, "name": "Constantine", "primary_type": "other", "release_date": "2023-05-28", "views_count": 0, "likes_count": 0, "created_at": "2025-08-03 05:59:47", "updated_at": "2025-08-03 05:59:47", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 79, "name": "We Will Rock You", "primary_type": "music", "description": "\"We Will Rock You\" is a song by the British rock band Queen for their 1977 album News of the World, written by guitarist <PERSON>.[3] Rolling Stone ranked it number 330 of \"The 500 Greatest Songs of All Time\" in 2004,[4] and it placed at number 146 on the Songs of the Century list in 2001. In 2009, \"We Will Rock You\" was inducted into the Grammy Hall of Fame.", "release_date": "2024-07-22T19:43:51.266Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FWe-Will-Rock-You-1721677667800.png?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-07-22T19:47:46.490Z", "updated_at": "2024-07-22T19:47:46.490Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FWe-Will-Rock-You-1721677667800.png?alt=media", "imageName": "We-Will-Rock-You-1721677667800.png", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/We_Will_Rock_You", "youtube": ""}}, {"id": 27, "name": "The X-Files", "primary_type": "tv_series", "description": "The X-Files is an American science fiction drama television series created by <PERSON>. The original television series aired from September 1993 to May 2002 on Fox. During its original run, the program spanned nine seasons, with 202 episodes. A short tenth season consisting of six episodes ran from January to February 2016. Following the ratings success of this revival, The X-Files returned for an eleventh season of ten episodes, which ran from January to March 2018. In addition to the television series, two feature films have been released: The 1998 film The X-Files and the stand-alone film The X-Files: I Want to Believe, released in 2008, six years after the original television run had ended.", "release_date": "2024-07-22T19:39:47.750Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe-X-Files-1721677320790.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-07-22T19:41:59.549Z", "updated_at": "2024-07-22T19:41:59.549Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe-X-Files-1721677320790.jpg?alt=media", "imageName": "The-X-Files-1721677320790.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_X-Files", "youtube": ""}}, {"id": 123, "name": "Toy Story", "primary_type": "film", "description": "Toy Story is a 1995 American animated comedy film produced by Pixar Animation Studios for Walt Disney Pictures. The first installment in the franchise of the same name, it was the first entirely computer-animated feature film, as well as the first feature film from Pixar. The film was directed by <PERSON> (in his feature directorial debut), written by <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON> based on a story by <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, produced by <PERSON> and <PERSON>, and features the voices of <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON>.", "release_date": "2024-07-22T19:34:52.384Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FToy-Story-1721677176656.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-07-22T19:39:29.454Z", "updated_at": "2024-07-22T19:39:29.454Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FToy-Story-1721677176656.jpg?alt=media", "imageName": "Toy-Story-1721677176656.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Toy_Story", "youtube": ""}}, {"id": 65, "name": "Star Wars: Episode V – The Empire Strikes Back", "primary_type": "film", "description": "The Empire Strikes Back (also known as Star Wars: Episode V – The Empire Strikes Back) is a 1980 American epic space opera film directed by <PERSON><PERSON> from a screenplay by <PERSON> and <PERSON>, based on a story by <PERSON>. The sequel to Star Wars (1977),[b] it is the second film in the Star Wars film series and the fifth chronological chapter of the \"Skywalker Saga\". Set three years after the events of Star Wars, the film recounts the battle between the malevolent Galactic Empire, led by the Emperor, and the Rebel Alliance, led by <PERSON> and Princess <PERSON>. As the Empire goes on the offensive, <PERSON> trains to master the Force so he can confront the Emperor's powerful disciple, <PERSON><PERSON>. The ensemble cast includes <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>.", "release_date": "2024-07-22T19:28:35.302Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FStar-Wars--Episode-V---The-Empire-Strikes-Back-1721676852123.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-07-22T19:34:05.807Z", "updated_at": "2024-07-22T19:34:05.807Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FStar-Wars--Episode-V---The-Empire-Strikes-Back-1721676852123.jpg?alt=media", "imageName": "Star-Wars--Episode-V---The-Empire-Strikes-Back-1721676852123.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Empire_Strikes_Back", "youtube": ""}}, {"id": 198, "name": "Guardians of the Galaxy", "primary_type": "film", "description": "Guardians of the Galaxy (retroactively referred to as Guardians of the Galaxy Vol. 1) is a 2014 American superhero film based on the Marvel Comics superhero team of the same name. Produced by Marvel Studios and distributed by Walt Disney Studios Motion Pictures, it is the 10th film in the Marvel Cinematic Universe (MCU). Directed by <PERSON>, who wrote the screenplay with <PERSON>, it features an ensemble cast including <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON> as the titular Guardians, along with <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. In the film, <PERSON> (<PERSON>) and a group of extraterrestrial criminals go on the run after stealing a powerful artifact.", "release_date": "2024-07-22T18:48:20.299Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FGuardians-of-the-Galaxy-1721674301222.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-07-22T18:51:33.551Z", "updated_at": "2024-07-22T18:51:33.551Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FGuardians-of-the-Galaxy-1721674301222.jpg?alt=media", "imageName": "Guardians-of-the-Galaxy-1721674301222.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Guardians_of_the_Galaxy_(film)", "youtube": ""}}, {"id": 104, "name": "Friends", "primary_type": "tv_series", "description": "Friends is an American television sitcom created by <PERSON> and <PERSON>, which aired on NBC from September 22, 1994, to May 6, 2004, lasting ten seasons.[1] With an ensemble cast starring <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON>, the show revolves around six friends in their 20s and early 30s who live in Manhattan, New York City. The original executive producers were <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>.", "release_date": "2024-07-22T18:37:50.651Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FFriends-1721673625141.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-07-22T18:40:24.161Z", "updated_at": "2024-07-22T18:40:24.161Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FFriends-1721673625141.jpg?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Friends", "youtube": ""}}, {"id": 146, "name": "The Big Bang Theory", "primary_type": "tv_series", "description": "The Big Bang Theory is an American television sitcom created by <PERSON> and <PERSON>, both of whom served as executive producers and head writers on the series, along with <PERSON>. It aired on CBS from September 24, 2007, to May 16, 2019, running for 12 seasons and 279 episodes.", "release_date": "2024-07-22T18:35:09.894Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe-Big-Bang-Theory-1721673462653.png?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-07-22T18:37:41.519Z", "updated_at": "2024-07-22T18:37:41.519Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe-Big-Bang-Theory-1721673462653.png?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Big_Bang_Theory", "youtube": ""}}, {"id": 121, "name": "The Simpsons", "primary_type": "tv_series", "description": "The Simpsons is an American animated sitcom created by <PERSON> for the Fox Broadcasting Company. Developed by <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, the series is a satirical depiction of American life, epitomized by the <PERSON> family, which consists of <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Set in the fictional town of Springfield, it caricatures society, Western culture, television, and the human condition.\n\n", "release_date": "2024-07-22T18:27:49.400Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe-Simpsons-1721673224686.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-07-22T18:33:43.277Z", "updated_at": "2024-07-22T18:33:43.277Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe-Simpsons-1721673224686.jpg?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Simpsons", "youtube": ""}}, {"id": 134, "name": "Star Trek", "primary_type": "tv_series", "secondary_type": "Movie", "description": "Star Trek is an American science fiction television series created by <PERSON> that follows the adventures of the starship USS Enterprise (NCC-1701) and its crew. It acquired the retronym of Star Trek: The Original Series (TOS) to distinguish the show within the media franchise that it began.", "release_date": "2024-07-22T18:23:59.835Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FStar-Trek-1721672837182.png?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-07-22T18:27:15.989Z", "updated_at": "2024-07-22T18:27:15.989Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FStar-Trek-1721672837182.png?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Star_Trek:_The_Original_Series", "youtube": ""}}, {"id": 91, "name": "Game of Thrones", "primary_type": "tv_series", "description": "Game of Thrones is an American fantasy drama television series created by <PERSON> and <PERSON><PERSON> <PERSON><PERSON> for HBO. It is an adaptation of A Song of Ice and Fire, a series of fantasy novels by <PERSON>, the first of which is A Game of Thrones. The show premiered on HBO in the United States on April 17, 2011, and concluded on May 19, 2019, with 73 episodes broadcast over eight seasons.", "release_date": "2024-07-22T18:20:27.032Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FGame-of-Thrones-1721672583872.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-07-22T18:23:02.635Z", "updated_at": "2024-07-22T18:23:02.635Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FGame-of-Thrones-1721672583872.jpg?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "<PERSON> is the head of <PERSON> Stark, Lord of Winterfell, and Warden of the North.", "youtube": ""}}, {"id": 101, "name": "Breaking Bad", "primary_type": "tv_series", "description": "Breaking Bad is an American crime drama television series created and produced by <PERSON> for AMC. Set and filmed in Albuquerque, New Mexico, the series follows <PERSON> (<PERSON>), an underpaid, dispirited high-school chemistry teacher struggling with a recent diagnosis of stage-three lung cancer. <PERSON> turns to a life of crime and partners with a former student, <PERSON> (<PERSON>), to produce and distribute methamphetamine to secure his family's financial future before he dies, while navigating the dangers of the criminal underworld. Breaking Bad premiered on AMC on January 20, 2008, and concluded on September 29, 2013, after five seasons consisting of 62 episodes.", "release_date": "2024-07-22T18:16:17.717Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBreaking-Bad-1721672429969.png?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-07-22T18:20:23.421Z", "updated_at": "2024-07-22T18:20:23.421Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBreaking-Bad-1721672429969.png?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Breaking_Bad", "youtube": ""}}, {"id": 210, "name": "How I Met Your Mother", "primary_type": "tv_series", "release_date": "2024-07-22T18:00:27.104Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FHow-I-Met-Your-Mother-1721674652525.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-07-22T18:06:20.676Z", "updated_at": "2024-07-22T18:06:20.676Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FHow-I-Met-Your-Mother-1721674652525.jpg?alt=media", "imageName": "How-I-Met-Your-Mother-1721674652525.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 162, "name": "Star Wars", "primary_type": "film", "description": "Star Wars is an American epic space opera media franchise created by <PERSON>, which began with the eponymous 1977 film[a] and quickly became a worldwide pop culture phenomenon. The franchise has been expanded into various films and other media, including television series, video games, novels, comic books, theme park attractions, and themed areas, comprising an all-encompassing fictional universe.[b] Star Wars is one of the highest-grossing media franchises of all time.", "release_date": "2024-07-22T15:37:29.427Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FStar-Wars-1721674761864.png?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-07-22T15:39:07.445Z", "updated_at": "2024-07-22T15:39:07.445Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FStar-Wars-1721674761864.png?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Star_Wars", "youtube": ""}}, {"id": 61, "name": "<PERSON> and the Goblet of Fire", "primary_type": "film", "secondary_type": "Book", "description": "<PERSON> and the Goblet of Fire is a 2005 fantasy film directed by <PERSON> from a screenplay by <PERSON>, based on the 2000 novel of the same name by <PERSON><PERSON><PERSON><PERSON>. It is the sequel to <PERSON> and the Prisoner of Azkaban (2004) and the fourth instalment in the <PERSON> film series. The film stars <PERSON> as <PERSON>, alongside <PERSON> and <PERSON> as <PERSON>'s best friends <PERSON> and <PERSON><PERSON><PERSON> respectively. The story follows <PERSON>'s fourth year at Hogwarts as he is chosen by the Goblet of Fire to compete in the Triwizard Tournament.", "release_date": "2024-07-22T14:39:54.035Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F-<PERSON>-<PERSON>-and-the-Goblet-of-Fire-1721674880953.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-07-22T14:42:35.501Z", "updated_at": "2024-07-22T14:42:35.501Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F-<PERSON>-<PERSON>-and-the-Goblet-of-Fire-1721674880953.jpg?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_<PERSON>_and_the_Goblet_of_Fire_(film)", "youtube": ""}}, {"id": 195, "name": "<PERSON> and the Prisoner of Azkaban", "primary_type": "film", "secondary_type": "Book", "description": "<PERSON> and the Prisoner of Azkaban is a 2004 fantasy film directed by <PERSON> from a screenplay by <PERSON>, based on the 1999 novel of the same name by <PERSON><PERSON> <PERSON><PERSON>. It is the sequel to <PERSON> and the Chamber of Secrets (2002) and the third instalment in the <PERSON> film series. The film stars <PERSON> as <PERSON>, alongside <PERSON> and <PERSON> as <PERSON>'s best friends <PERSON> and <PERSON><PERSON><PERSON> respectively. It chronicles <PERSON>'s third year at Hogwarts and his quest to uncover the truth about his past, including the connection recently-escaped Azkaban prisoner <PERSON> has to <PERSON> and his deceased parents.", "release_date": "2024-07-22T14:35:24.378Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON><PERSON>-<PERSON>-and-the-Prisoner-of-Azkaban-1721674937660.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-07-22T14:38:55.010Z", "updated_at": "2024-07-22T14:38:55.010Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON><PERSON>-<PERSON>-and-the-Prisoner-of-Azkaban-1721674937660.jpg?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_<PERSON>_and_the_Prisoner_of_Azkaban_(film)", "youtube": ""}}, {"id": 48, "name": "The Lion King", "primary_type": "film", "description": "In the Pride Lands of Tanzania, a pride of lions rule over the kingdom from Pride Rock. King <PERSON><PERSON><PERSON> and Queen <PERSON><PERSON>'s newborn son, <PERSON><PERSON>, is presented to the gathering animals by <PERSON><PERSON><PERSON> the mandrill, the kingdom's shaman and advisor. <PERSON><PERSON><PERSON>'s younger brother, <PERSON><PERSON>, covets the throne.\nAfter <PERSON><PERSON> grows into a cub, <PERSON><PERSON><PERSON> shows him the Pride Lands and explains the responsibilities of kingship and the \"circle of life,\" which connects all living things. One day, <PERSON><PERSON> and his best friend <PERSON><PERSON> explore an elephant graveyard, where the two are chased by three spotted hyenas named <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> <PERSON><PERSON><PERSON> is alerted by his majordomo, the hornbill <PERSON><PERSON><PERSON>, and rescues the cubs. Though disappointed with <PERSON><PERSON> for disobeying him and endangering himself and <PERSON><PERSON>, <PERSON><PERSON><PERSON> forgives him and explains that the great kings of the past watch over them from the night sky, from which he will one day watch over <PERSON><PERSON>. <PERSON><PERSON>, having planned the attack, visits the hyenas and convinces them to help him kill both <PERSON><PERSON><PERSON> and <PERSON><PERSON> in exchange for hunting rights in the Pride Lands.", "release_date": "2024-07-22T14:16:32.210Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe-Lion-King-1721675125871.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-07-22T14:20:40.922Z", "updated_at": "2024-07-22T14:20:40.922Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe-Lion-King-1721675125871.jpg?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Lion_King", "youtube": ""}}, {"id": 183, "name": "<PERSON>", "primary_type": "film", "secondary_type": "Novel", "description": "<PERSON> G<PERSON> is a 1994 American comedy-drama film directed by <PERSON> and written by <PERSON>. It is an adaptation of the 1986 novel of the same name by <PERSON>, and stars <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>.", "release_date": "2024-07-22T09:06:23.950Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FForrest-Gump-1721675203501.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-07-22T09:12:21.096Z", "updated_at": "2024-07-22T09:12:21.096Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FForrest-Gump-1721675203501.jpg?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_<PERSON>", "youtube": ""}}, {"id": 196, "name": "WeCrashed", "primary_type": "tv_series", "secondary_type": "Podcast", "description": "WeCrashed is an American drama miniseries that premiered on Apple TV+ on March 18, 2022. The series stars <PERSON> and <PERSON> as <PERSON> and <PERSON><PERSON><PERSON>, the real-life married couple at the heart of WeWork, a coworking space company which claimed a valuation of $47 billion (in an internally produced prospectus) in 2019, before crashing as a result of financial revelations. The series is based on the podcast WeCrashed: The Rise and Fall of WeWork by Wondery.\n\n", "release_date": "2024-07-17T19:31:33.967Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FWeCrashed-1721675589580.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-07-21T15:47:45.375Z", "updated_at": "2024-07-21T15:47:45.375Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FWeCrashed-1721675589580.jpg?alt=media", "imageName": "WeCrashed-1721675589580.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/WeCrashed", "youtube": ""}}, {"id": 181, "name": "Summer Game Fest", "primary_type": "other", "secondary_type": "Event", "description": "Summer Game Fest is an annual live video game event organized and hosted by game journalist <PERSON>.\n\n- It takes place over multiple live streams during the summer period in North America.\n\n- The main highlight is the \"main show\" which showcases upcoming major game releases through trailers and announcements. This is usually held on the first day.\n\n- After the main show, there is a \"Day of the Devs\" stream focused on highlighting upcoming indie games.\n\n- Other publisher-specific streams and events also occur in the days following the main show.\n\n- It was created in 2020 following the cancellation of major gaming events like E3 due to COVID-19.\n\nBusiness Model\n\n- Publishers pay fees to have trailers aired during the main show, with rates estimated around $250,000 for a 1-minute trailer in 2024.\n\n- A few \"free slots\" are reserved for smaller indie studios during the main show.\n\n- The 2023 event reportedly made around $9.65 million from trailer fees alone.", "release_date": "2020-04-30T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSummer-Game-Fest-1718015779871.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-06-10T10:36:11.256Z", "updated_at": "2024-06-10T10:36:11.256Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSummer-Game-Fest-1718015779871.jpg?alt=media", "imageName": "Summer-Game-Fest-1718015779871.jpg", "imdb": "", "instagram": "https://www.instagram.com/summergamefest", "netflix": "", "prime_video": "", "twitch": "https://www.twitch.tv/thegameawards", "twitter": "https://twitter.com/summergamefest", "website": "https://www.summergamefest.com/", "wikipedia": "https://en.wikipedia.org/wiki/Summer_Game_Fest", "youtube": "https://youtu.be/pZzia5NrMuU"}}, {"id": 25, "name": "Can art be popular?", "primary_type": "other", "release_date": "1924-07-31T23:00:00.000Z", "views_count": 0, "likes_count": 0, "created_at": "2024-06-05T13:19:08.513Z", "updated_at": "2024-06-05T13:19:08.514Z", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 201, "name": "Arte", "primary_type": "media_stream", "secondary_type": "TV", "description": "ARTE is a European public service television channel dedicated to cultural programming. Here are some key facts about ARTE:\n\n- 🇫🇷🇩🇪 It was created in 1992 as a symbol of Franco-German friendship, initiated by French President <PERSON> and German Chancellor <PERSON>.\n\n- ARTE is a joint venture between France and Germany, with separate companies in each country contributing to programming and operations.\n\n- 📺 It broadcasts cultural programs focused on topics like arts, literature, philosophy, history, and sciences from a European perspective.\n\n- ARTE airs in French and German with subtitles in the other language, as well as the original language when possible.\n\n- In addition to its TV channel, ARTE has an online streaming platform called arte.tv offering live and catch-up content.\n\n- 🚀 It launched an online opera platform in 2018, streaming new opera productions from across Europe with subtitles in six languages.\n\n- ARTE Concert is its platform dedicated to streaming live music performances, festivals, and cultural events.\n\n- The channel is available across Europe via satellite, cable, and digital terrestrial television.\n\n- While viewership is higher in France than Germany, ARTE has won numerous prestigious awards for its programming over the years.\n", "release_date": "1992-05-29T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FArte-1717548371782.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-06-05T00:46:02.362Z", "updated_at": "2024-06-05T00:46:02.363Z", "urls": {"amazon": "", "facebook": "https://www.facebook.com/artetv", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FArte-1717548371782.jpg?alt=media", "imageName": "Arte-1717548371782.jpg", "imdb": "", "instagram": "https://www.instagram.com/artefr", "netflix": "", "prime_video": "", "twitch": "https://www.twitch.tv/artefr", "twitter": "https://twitter.com/ARTEfr", "website": "https://www.arte.tv", "wikipedia": "https://en.wikipedia.org/wiki/Arte", "youtube": "https://www.youtube.com/@arte"}}, {"id": 80, "name": "Les Contemplations", "primary_type": "book", "secondary_type": "Poetry", "description": "\"Les Contemplations\" is a major poetry collection by <PERSON>, published in 1856. Here are some key facts about this seminal work:\n\n- It consists of 158 poems divided into six books, most written between 1841 and 1855, though some date back to 1830.\n\n- The collection is deeply autobiographical, with memory playing a central role. It serves as a poetic autobiography, with <PERSON> experimenting with the memoir genre in verse.[1][2] \n\n- Many poems pay tribute to <PERSON>'s daughter <PERSON><PERSON><PERSON><PERSON><PERSON>, who drowned accidentally in the Seine in 1843. Her tragic death profoundly marked the work and its structure.[1][3]\n\n- While continuing the lyricism of earlier works like Les Rayons et les Ombres, Les Contemplations also marked a shift towards a darker, more introspective poetry, representing a second poetic rebirth for <PERSON>.\n\n- The collection explores themes of love, joy, death, mourning, and a mystical faith. <PERSON> affirms his belief in the immortality of the soul and a form of metempsychosis.\n\n- Beyond the personal, Les Contemplations also serve as a mirror reflecting the collective soul, with a critical eye on 19th century society, its inequalities and misery.\n\n- It is considered one of the major works of French Romantic poetry, illustrating <PERSON>'s lyrical genius at its apex.", "release_date": "1855-12-31T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLes-Contemplations-1716733966974.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-05-26T14:32:37.133Z", "updated_at": "2024-05-26T14:32:37.134Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLes-Contemplations-1716733966974.jpg?alt=media", "imageName": "Les-Contemplations-1716733966974.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Les_Contemplations", "youtube": ""}}, {"id": 34, "name": "A Murder at the End of the World", "primary_type": "tv_series", "secondary_type": "Psychological thriller, Murder mystery", "description": "«A Murder at the End of the World» is an American psychological thriller drama television miniseries created by <PERSON><PERSON> and <PERSON><PERSON> for FX. It stars <PERSON> as an amateur detective who attempts to solve a murder at an isolated Arctic retreat in Iceland. The supporting cast includes <PERSON><PERSON> herself, <PERSON> and <PERSON>.\n\nThe series follows <PERSON><PERSON>, an amateur detective, who is invited, along with eight other guests, by a reclusive billionaire to participate in a retreat at an isolated Arctic compound in Iceland. When one of the other guests is found dead, <PERSON><PERSON> must use all of her skills to prove it was murder against a tide of competing interests and before the killer takes another life.", "release_date": "2023-11-13T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FA Murder at the End of the World-1705915480068.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-01-22T09:24:33.884Z", "updated_at": "2024-01-22T09:24:33.885Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FA Murder at the End of the World-1705915480068.jpg?alt=media", "imageName": "A Murder at the End of the World-1705915480068.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/A_Murder_at_the_End_of_the_World", "youtube": ""}}, {"id": 137, "name": "Ma<PERSON> talks!", "primary_type": "other", "secondary_type": "Cinema", "description": "MAMET TALKS! by <PERSON> is a list of thoughts of <PERSON> about screenwriting.", "release_date": "2003-07-31T22:00:00.000Z", "views_count": 0, "likes_count": 0, "created_at": "2024-01-22T09:03:07.535Z", "updated_at": "2024-01-22T09:03:07.535Z", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 219, "name": "Positif n°102", "primary_type": "book", "secondary_type": "Cinema", "description": "Cinema review by <PERSON><PERSON><PERSON>.", "release_date": "1969-07-31T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPositif n°102-1705701522892.webp?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-01-19T21:58:41.411Z", "updated_at": "2024-01-19T21:58:41.411Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPositif n°102-1705701522892.webp?alt=media", "imageName": "Positif n°102-1705701522892.webp", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 7, "name": "Le cinéma américain par ses auteurs", "primary_type": "book", "secondary_type": "Cinema", "description": "A book written by <PERSON>.", "release_date": "1977-07-31T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLe cinéma américain par ses auteurs-1705701299960.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-01-19T21:54:53.917Z", "updated_at": "2024-01-19T21:54:53.917Z", "urls": {"amazon": "https://www.amazon.fr/Cin%C3%A9ma-am%C3%A9ricain-par-ses-auteurs/dp/B0014MCT5C", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLe cinéma américain par ses auteurs-1705701299960.jpg?alt=media", "imageName": "Le cinéma américain par ses auteurs-1705701299960.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 16, "name": "Bérénice", "primary_type": "writings", "secondary_type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Berenice is a five-act tragedy by the French 17th-century playwright <PERSON>. Berenice was not played often between the 17th and the 20th centuries. It was premiered on 21 November 1670 by the Comédiens du Roi at the Hôtel de Bourgogne.", "release_date": "1670-07-31T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBérénice-1705688781306.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-01-19T18:26:19.413Z", "updated_at": "2024-01-19T18:26:19.413Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBérénice-1705688781306.jpg?alt=media", "imageName": "Bérénice-1705688781306.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/B%C3%A9r%C3%A9nice", "youtube": ""}}, {"id": 132, "name": "The Portable <PERSON>", "primary_type": "book", "secondary_type": "Poetry, Humorous fiction", "description": "This collection ranges over the verse, stories, essays, and journalism of one of the twentieth century's most quotable authors.", "release_date": "1944-05-14T22:00:00.000Z", "image_url": "https://www.cdiscount.com/pdt2/5/3/2/1/700x700/9780143039532/rw/the-portable-dorothy-parker.jpg", "views_count": 0, "likes_count": 0, "created_at": "2024-01-19T18:15:37.106Z", "updated_at": "2024-01-19T18:15:37.106Z", "urls": {"amazon": "https://www.amazon.fr/Portable-<PERSON>-<PERSON>-non-massicot%C3%A9/dp/0143039539", "facebook": "", "image": "https://www.cdiscount.com/pdt2/5/3/2/1/700x700/9780143039532/rw/the-portable-dorothy-parker.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 90, "name": "L'écriture de scénarios", "primary_type": "book", "secondary_type": "Cinema", "description": "With his recognized practical sense and rigorous precision, <PERSON><PERSON><PERSON> invites you to discover the secrets of dramaturgy, its fundamental rules, as well as the keys, tools and tricks that will help you approach screenwriting as simply as possible. From the idea for your film or series, to finding a producer, or even selling your project, this book guides you step by step, without ever losing you along the way. Numerous exercises and their corrected versions, as well as a well-stocked address book, are additional assets designed to give you the best chance of success.", "release_date": "2018-09-10T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FL'écriture de scénarios-1705686507944.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-01-19T17:48:21.231Z", "updated_at": "2024-01-19T17:48:21.231Z", "urls": {"amazon": "https://www.amazon.fr/L%C3%A9criture-sc%C3%A9nar<PERSON>-<PERSON>-<PERSON>/dp/2844811817", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FL'écriture de scénarios-1705686507944.jpg?alt=media", "imageName": "L'écriture de scénarios-1705686507944.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 31, "name": "All The Lights We Cannot See", "primary_type": "tv_series", "secondary_type": "Drama", "description": "All the Light We Cannot See is an American drama limited series directed by <PERSON> and developed by <PERSON> for Netflix. Based on <PERSON>'s Pulitzer Prize winning novel of the same name, it stars <PERSON>, <PERSON> and <PERSON>. \n\nThe four-part series follows the stories of a blind French girl named <PERSON><PERSON><PERSON><PERSON> and a German soldier named <PERSON>, whose paths cross in occupied France during World War II.", "release_date": "2023-11-01T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAll The Lights We Cannot See-1704196230440.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-01-02T11:50:24.408Z", "updated_at": "2024-01-02T11:50:24.408Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAll The Lights We Cannot See-1704196230440.jpg?alt=media", "imageName": "All The Lights We Cannot See-1704196230440.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/81083008", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/All_the_Light_We_Cannot_See_(miniseries)", "youtube": "https://www.youtube.com/watch?v=ePLIObDy_HI"}}, {"id": 200, "name": "The Crowded Room", "primary_type": "tv_series", "secondary_type": "Psychological thriller", "description": "The Crowded Room is an American psychological thriller television miniseries created by <PERSON><PERSON><PERSON> and inspired by the 1981 non-fiction novel «The Minds of <PERSON>» by <PERSON>. <PERSON>, <PERSON>, and <PERSON> lead a supporting cast that includes <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>.\n\nThe series follows <PERSON> (Holland) after he was arrested for his involvement in a New York City shooting in 1979. <PERSON> unveils his life through a series of interviews with interrogator <PERSON><PERSON> (<PERSON><PERSON><PERSON>), and slowly details to <PERSON><PERSON>, and the audience, his mysterious past that led him to the fateful incident.", "release_date": "2023-06-08T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Crowded Room-1704119430996.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-01-01T14:30:29.366Z", "updated_at": "2024-01-01T14:30:29.366Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Crowded Room-1704119430996.jpg?alt=media", "imageName": "The Crowded Room-1704119430996.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "https://www.apple.com/fr/tv-pr/originals/the-crowded-room/", "wikipedia": "https://en.wikipedia.org/wiki/The_Crowded_Room", "youtube": "https://www.youtube.com/watch?v=4w1xZA7pX2c"}}, {"id": 176, "name": "Les visiteurs de Cannes", "primary_type": "book", "secondary_type": "Cinematography", "description": "On the occasion of the 45th Cannes Film Festival, its general delegate, <PERSON>, decided to pay tribute to all those who, film after film, have brought the festival to life: the great directors from all over the world. Testimonials from <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>... and many others.", "release_date": "1992-08-03T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLes visiteurs de Cannes-1704119075511.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-01-01T14:24:34.014Z", "updated_at": "2024-01-01T14:24:34.015Z", "urls": {"amazon": "https://www.amazon.fr/visiteurs-Cannes-Cin%C3%A9astes-%C3%A0-loeuvre/dp/2218051877", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLes visiteurs de Cannes-1704119075511.jpg?alt=media", "imageName": "Les visiteurs de Cannes-1704119075511.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 169, "name": "Préface aux pièces plaisantes", "primary_type": "book", "secondary_type": "Essay", "description": "One of <PERSON>'s most glittering comedies, <PERSON> and the Man is a burlesque of Victorian attitudes to heroism, war and empire. In the contrast between <PERSON><PERSON><PERSON><PERSON>, the mercenary soldier, and the brave leader, <PERSON><PERSON><PERSON>, the true nature of valour is revealed. <PERSON> mocks deluded idealism in Candida, when a young poet becomes infatuated with the wife of a Socialist preacher. The Man of Destiny is a witty war of words between <PERSON> and a 'strange lady', while in the exuberant farce You Never Can Tell a divided family is reunited by chance. Although <PERSON> intended Plays Pleasant to be gentler comedies than those in their companion volume, Plays Unpleasant, their prophetic satire is sharp and provocative.", "release_date": "1898-08-03T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPréface aux pièces plaisantes-1704118718971.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-01-01T14:18:28.345Z", "updated_at": "2024-01-01T14:18:28.345Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPréface aux pièces plaisantes-1704118718971.jpg?alt=media", "imageName": "Préface aux pièces plaisantes-1704118718971.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 75, "name": "Les vrais penseurs de notre temps", "primary_type": "book", "secondary_type": "Intellectual life", "description": "From the Big Bang to Chinese philosophy, from the origins of language to liberal economics, from genetics to spirituality, the author of The New Wealth of Nations explores the characteristic intellectual theories of our time. Twenty-eight encounters, twenty-eight portraits - <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>... - take us on a discovery of the most topical debates in world scientific and philosophical thought.", "release_date": "1989-08-03T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLes vrais penseurs de notre temps -1704115930035.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-01-01T13:32:08.758Z", "updated_at": "2024-01-01T13:32:08.758Z", "urls": {"amazon": "https://www.amazon.fr/vrais-penseurs-notre-temps/dp/2213023239", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLes vrais penseurs de notre temps -1704115930035.jpg?alt=media", "imageName": "Les vrais penseurs de notre temps -1704115930035.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 147, "name": "La Zone Du Dehors", "primary_type": "book", "secondary_type": "Anticipation", "description": "La Zone du Dehors is a science-fiction novel by French writer <PERSON>, published in two volumes, Les Clameurs and La Volte by Cylibris in 1999, then in a single volume entitled La Zone du dehors by the same publisher in 2001.", "release_date": "1998-12-31T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLa Zone Du Dehors-1704115501991.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-01-01T13:24:53.589Z", "updated_at": "2024-01-01T13:24:53.589Z", "urls": {"amazon": "https://www.fnac.com/a8080394/<PERSON>-<PERSON><PERSON>o-La-Zone-du-Dehors", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLa Zone Du Dehors-1704115501991.jpg?alt=media", "imageName": "La Zone Du Dehors-1704115501991.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "https://lavolte.net/livres/la-zone-du-dehors/", "wikipedia": "https://fr.wikipedia.org/wiki/La_Zone_du_Dehors", "youtube": ""}}, {"id": 73, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "primary_type": "book", "secondary_type": "Novel", "description": "«<PERSON><PERSON><PERSON><PERSON><PERSON> zéro» is a french novel written by <PERSON>, published on 2017 by <PERSON> in Éditions Eyrolles. It's her first novel.\n\nIt tells the story of <PERSON><PERSON><PERSON>, chief financial officer in a startup, whose life is changed the day her friend asks for help. From this day, a long trip will begin.", "release_date": "2017-07-12T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FKilomètre zéro-1704114319359.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2024-01-01T13:05:12.257Z", "updated_at": "2024-01-01T13:05:12.257Z", "urls": {"amazon": "https://www.amazon.fr/Kilom%C3%A8tre-z%C3%A9ro-chemin-du-bonheur/dp/229021051X", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FKilomètre zéro-1704114319359.jpg?alt=media", "imageName": "Kilomètre zéro-1704114319359.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://fr.wikipedia.org/wiki/Kilom%C3%A8tre_z%C3%A9ro_(livre)", "youtube": ""}}, {"id": 33, "name": "Ondine", "primary_type": "writings", "secondary_type": "Drama", "description": "Ondine is a play written in 1938 by French dramatist <PERSON>, based on the 1811 novella Undine by the German Romantic <PERSON> that tells the story of <PERSON> and <PERSON><PERSON>. <PERSON> is a knight-errant who has been sent off on a quest by his betrothed. In the forest he meets and falls in love with <PERSON><PERSON>, a water sprite who is attracted to the world of mortal man. The subsequent marriage of people from different worlds is, of course, folly.", "release_date": "1939-05-03T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FOndine-1702428123219.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2023-12-13T00:42:01.824Z", "updated_at": "2023-12-13T00:42:01.824Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FOndine-1702428123219.jpg?alt=media", "imageName": "Ondine-1702428123219.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Ondine%20(play)", "youtube": ""}}, {"id": 142, "name": "Interpretation of fairy tales – L'interprétation des contes de fées", "primary_type": "book", "secondary_type": "Psychology", "description": "A Jungian psychologist argues how careful analyses of fairy tales like <PERSON> and the Beast can lead to a deeper understanding of human psychology.\n\nOf the various types of mythological literature, fairy tales are the simplest and purest expressions of the collective unconscious and thus offer the clearest understanding of the basic patterns of the human psyche. Every people or nation has its own way of experiencing this psychic reality, and so a study of the world's fairy tales yields a wealth of insights into the archetypal experiences of humankind.", "release_date": "1970-08-08T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FInterpretation of fairy tales – L'interprétation des contes de fées-1702427688094.png?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2023-12-13T00:34:45.579Z", "updated_at": "2023-12-13T00:34:45.580Z", "urls": {"amazon": "https://www.amazon.com/Interpretation-Fairy-Tales-<PERSON>-<PERSON>-<PERSON>/dp/0877735263", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FInterpretation of fairy tales – L'interprétation des contes de fées-1702427688094.png?alt=media", "imageName": "Interpretation of fairy tales – L'interprétation des contes de fées-1702427688094.png", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 36, "name": "Un passage in Entracte", "primary_type": "other", "description": "This is a reference cited in «La Dramaturgie» by <PERSON><PERSON><PERSON>, but we couldn't find more information beside the title and the publication date, for the moment.", "release_date": "1928-05-12T23:00:00.000Z", "views_count": 0, "likes_count": 0, "created_at": "2023-12-13T00:28:05.710Z", "updated_at": "2023-12-13T00:28:05.710Z", "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 130, "name": "The Rose Tattoo – La rose tato<PERSON>e", "primary_type": "writings", "secondary_type": "Drama", "description": "The Rose Tattoo is a three-act play written by <PERSON> in 1949 and 1950; after its Chicago premiere on December 29, 1950, he made further revisions to the play for its Broadway premiere on February 2, 1951, and its publication by New Directions the following month. A film adaptation was released in 1955.", "release_date": "1950-08-08T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Rose <PERSON>ttoo – La rose tato<PERSON>e-1702426185875.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2023-12-13T00:09:44.627Z", "updated_at": "2023-12-13T00:09:44.627Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Rose <PERSON>ttoo – La rose tato<PERSON>e-1702426185875.jpg?alt=media", "imageName": "The Rose Tattoo – La rose tato<PERSON>e-1702426185875.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_<PERSON>_<PERSON>o", "youtube": ""}}, {"id": 163, "name": "Le sentiment même de soi - The Feeling of What Happens, Body and Emotion in the Making of Consciousness", "primary_type": "book", "secondary_type": "Psychology", "description": "'The Feeling of What Happens will change your experience of yourself' New York Times Where do our emotions come from? What does it mean to be conscious? At its core, human consciousness is awareness of the feeling, experiencing self, the 'very thought of' oneself.", "release_date": "1999-08-08T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLe sentiment même de soi - The Feeling of What Happens, Body and Emotion in the Making of Consciousness -1702425804333.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2023-12-13T00:03:22.873Z", "updated_at": "2023-12-13T00:03:22.873Z", "urls": {"amazon": "https://www.amazon.fr/Feeling-What-Happens-Emotion-Consciousness/dp/0156010755", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLe sentiment même de soi - The Feeling of What Happens, Body and Emotion in the Making of Consciousness -1702425804333.jpg?alt=media", "imageName": "Le sentiment même de soi - The Feeling of What Happens, Body and Emotion in the Making of Consciousness -1702425804333.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 22, "name": "<PERSON><PERSON><PERSON><PERSON>", "primary_type": "book", "secondary_type": "Interview", "description": "«Entretiens» is a book based on a serie of audio recordings between <PERSON> and <PERSON> on a 8-days period. In the 30 hours of conversation, <PERSON> and <PERSON><PERSON><PERSON> discussed the whole work of the film director. The book is probably one of the most famous reference in cinema.", "release_date": "1966-08-08T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FEntretiens-1702425521373.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2023-12-12T23:58:36.022Z", "updated_at": "2023-12-12T23:58:36.022Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FEntretiens-1702425521373.jpg?alt=media", "imageName": "Entretiens-1702425521373.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 122, "name": "L'Insoutenable légèreté de l'être - Nesnesitelná lehkost bytí", "primary_type": "book", "secondary_type": "Philosophical fiction", "description": "The Unbearable Lightness of Being is a 1984 novel by <PERSON>, about two women, two men, a dog, and their lives in the 1968 Prague Spring period of Czechoslovak history. Although written in 1982, the novel was not published until two years later, in a French translation", "release_date": "1984-08-08T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FL'Insoutenable légèreté de l'être - Nesnesitelná lehkost bytí-1702423822440.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2023-12-12T23:30:21.124Z", "updated_at": "2023-12-12T23:30:21.124Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FL'Insoutenable légèreté de l'être - Nesnesitelná lehkost bytí-1702423822440.jpg?alt=media", "imageName": "L'Insoutenable légèreté de l'être - Nesnesitelná lehkost bytí-1702423822440.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Unbearable_Lightness_of_Being", "youtube": ""}}, {"id": 207, "name": "Le son interdit", "primary_type": "music", "secondary_type": "RAP", "description": "«Le son interdit» is a single released on October 16, 2020 by ZeratoR, DFG & MisterMV. It is part of a donation goal during ZEVENT – a caritative french event organized by ZeratoR.", "release_date": "2020-10-15T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLe son interdit-1702423442933.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2023-12-12T23:23:56.996Z", "updated_at": "2023-12-12T23:23:56.996Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLe son interdit-1702423442933.jpg?alt=media", "imageName": "Le son interdit-1702423442933.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": "https://youtu.be/9GXrE6xHkeM"}}, {"id": 52, "name": "Le petit prince", "primary_type": "book", "secondary_type": "Novel", "description": "The Little Prince (French: <PERSON> Petit <PERSON>, pronounced [lə p(ə)ti pʁɛ̃s]) is a novella written and illustrated by French aristocrat, writer, and military pilot <PERSON>. \n\nIt was first published in English and French in the United States by Reynal & Hitchcock in April 1943 and was published posthumously in France following liberation; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'s works had been banned by the Vichy Regime. \n\nThe story follows a young prince who visits various planets, including Earth, and addresses themes of loneliness, friendship, love, and loss. Despite its style as a children's book, The Little Prince makes observations about life, adults, and human nature.", "release_date": "1943-03-31T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLe petit prince-1702130435864.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2023-12-09T14:00:29.451Z", "updated_at": "2023-12-09T14:00:29.451Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLe petit prince-1702130435864.jpg?alt=media", "imageName": "Le petit prince-1702130435864.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The%20Little%20Prince", "youtube": ""}}, {"id": 108, "name": "<PERSON><PERSON><PERSON>, <PERSON>", "primary_type": "music", "secondary_type": "R&B", "description": "«<PERSON><PERSON><PERSON>, <PERSON>» is the fifth track on the album «Metropolis: Suite I (The Chase)» by <PERSON><PERSON><PERSON>.\n\nReleased on August 24, 2007 on Bad Boy Records, and produced by <PERSON><PERSON><PERSON>, <PERSON> Z, and <PERSON>, the EP constitutes the first installment of <PERSON><PERSON><PERSON>'s seven-part Metropolis conceptual series.\n\nMetropolis: Suite I (The Chase) is the first installment of <PERSON><PERSON><PERSON>'s seven-part Metropolis conceptual series, inspired by <PERSON>'s science fiction classic film, Metropolis (1927). It follows a fictional tale of android <PERSON><PERSON><PERSON> who is mass-produced in the year 2719 for a market filled with severe social stratification.[citation needed] <PERSON><PERSON><PERSON> falls in love with a human, and is sentenced to disassembly.", "release_date": "2008-08-24T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSincerely, Jane-1702128812032.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2023-12-09T13:33:26.589Z", "updated_at": "2023-12-09T13:33:26.589Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSincerely, Jane-1702128812032.jpg?alt=media", "imageName": "Sincerely, Jane-1702128812032.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Metropolis:_Suite_I_(The_Chase)", "youtube": "https://www.youtube.com/watch?v=b_WhE7mBwK8"}}, {"id": 85, "name": "<PERSON><PERSON><PERSON><PERSON>", "primary_type": "music", "secondary_type": "Baroque Pop", "description": "<PERSON><PERSON><PERSON><PERSON> is a track on the album «Notre-Dame-des-Sept-Douleurs» released on June 26, 2020 by Klô Pelgag. The track is produced by <PERSON><PERSON><PERSON><PERSON> and Pelgag and it lasts 4:29. You can hear in the music these different genres: Chamber pop, Baroque pop, Art pop, Electrorock.", "release_date": "2020-02-05T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FRémora-1702125074034.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2023-12-09T12:31:07.885Z", "updated_at": "2023-12-09T12:31:07.885Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FRémora-1702125074034.jpg?alt=media", "imageName": "Rémora-1702125074034.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Notre-Dame-des-Sept-Douleurs_(album)", "youtube": "https://www.youtube.com/watch?v=Y9gJLi5uVHQ&pp=ygUGcmVtb3Jh"}}, {"id": 188, "name": "The Changeling", "primary_type": "tv_series", "secondary_type": "Horror fantasy", "description": "The Changeling is an American horror fantasy television series created by <PERSON> and directed by <PERSON><PERSON> based on the novel of the same name by <PERSON> for Apple TV+. The series premiered on September 8, 2023, with the first three episodes.", "release_date": "2023-10-19T17:24:07.509Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Changeling-1697897363020.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2023-10-21T14:09:21.774Z", "updated_at": "2023-10-21T14:09:21.774Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Changeling-1697897363020.jpg?alt=media", "imageName": "The Changeling-1697897363020.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": "https://www.youtube.com/watch?v=i3jjAm2dBeo"}}, {"id": 1, "name": "Three Thousand Years of Longing", "primary_type": "film", "secondary_type": "Fantasy romantic drama", "description": "Three Thousand Years of Longing is a 2022 fantasy romantic drama film directed and produced by <PERSON>. Written by <PERSON> and <PERSON>, it is based on the 1994 short story \"The Djinn in the Nightingale's Eye\" by <PERSON><PERSON> <PERSON><PERSON> and stars <PERSON><PERSON><PERSON> as a djinn who is unleashed from a bottle by a professor (<PERSON><PERSON><PERSON>) and tells her stories from his thousands of years of existence. The film is dedicated to <PERSON>'s mother <PERSON>, as well as <PERSON><PERSON>, relative of producer <PERSON>.", "release_date": "2022-05-19T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThree Thousand Years of Longing-1697896773706.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2023-10-21T13:59:26.248Z", "updated_at": "2023-10-21T13:59:26.248Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThree Thousand Years of Longing-1697896773706.jpg?alt=media", "imageName": "Three Thousand Years of Longing-1697896773706.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Three_Thousand_Years_of_Longing", "youtube": "http://www.youtube.com/watch?v=oHDAwvxeAoc"}}, {"id": 3, "name": "The Fall of the House of Usher", "primary_type": "tv_series", "secondary_type": "Ghotic horror", "description": "The Fall of the House of Usher is an American gothic horror drama television miniseries created by <PERSON>. Loosely based on the short story of the same name and other works by <PERSON>, it premiered on Netflix with all eight episodes released at once on October 12, 2023.\n\nThe story is about the CEO of a corrupt pharmaceutical company faces his questionable past when his children start dying in mysterious and brutal ways.", "release_date": "2023-10-11T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Fall of the House of Usher-1697893764051.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2023-10-21T13:09:17.734Z", "updated_at": "2023-10-21T13:09:17.734Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Fall of the House of Usher-1697893764051.jpg?alt=media", "imageName": "The Fall of the House of Usher-1697893764051.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/81414665", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Fall_of_the_House_of_Usher_(miniseries)", "youtube": ""}}, {"id": 161, "name": "BEEF", "primary_type": "tv_series", "secondary_type": "Tragicomedy", "description": "Bee<PERSON> is a 2023 American comedy-drama television limited series created by Korean director <PERSON> for Netflix. It stars <PERSON> and <PERSON> as <PERSON> and <PERSON>, two strangers whose involvement in a road rage incident escalates into a prolonged feud. Appearing in supporting roles are <PERSON>, <PERSON>, <PERSON>, and <PERSON>.", "release_date": "2023-04-05T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBEEF-1695650709768.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2023-09-25T14:05:02.864Z", "updated_at": "2023-09-25T14:05:02.864Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBEEF-1695650709768.jpg?alt=media", "imageName": "BEEF-1695650709768.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/81447461", "prime_video": "", "twitch": "", "twitter": "", "website": "https://www.netflix.com/tudum/beef", "wikipedia": "https://en.wikipedia.org/wiki/Beef_(TV_series)", "youtube": ""}}, {"id": 6, "name": "Voyage au pays des maths", "primary_type": "media_stream", "secondary_type": "Sciences", "description": "\"Voyage au pays des maths\" is an Arte animated mini-serie documentary created by <PERSON> ni 2021. This documentary explores mathematical concepts and explains them in simple words.\n\nA season 2 is in production as of 2023.", "release_date": "2021-09-22T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FVoyage au pays des maths-1695636047828.png?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2023-09-25T10:00:23.990Z", "updated_at": "2023-09-25T10:00:23.991Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FVoyage au pays des maths-1695636047828.png?alt=media", "imageName": "Voyage au pays des maths-1695636047828.png", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "https://www.arte.tv/fr/videos/RC-021426/voyages-au-pays-des-maths/", "wikipedia": "https://fr.wikipedia.org/wiki/Voyages_au_pays_des_maths", "youtube": "https://www.youtube.com/playlist?list=PLCwXWOyIR22veT31gK5JwmqxuVc0Uoy8a"}}, {"id": 43, "name": "The Expanse", "primary_type": "tv_series", "secondary_type": "Science-Fiction", "description": "The Expanse is an American science fiction television series developed by <PERSON> and <PERSON> for the Syfy network, and is based on the series of novels of the same name by <PERSON>. \n\nThe series is set in a future where humanity has colonized the Solar System. It follows a disparate band of protagonists—United Nations Security Council member <PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON>), police detective <PERSON><PERSON> (<PERSON>), ship's officer <PERSON> (<PERSON>) and his crew—as they unwittingly unravel and place themselves at the center of a conspiracy that threatens the system's fragile state of cold war, while dealing with existential crises brought forth by newly discovered alien technology.", "release_date": "2015-12-14T00:00:00.692Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Expanse-1695342946385.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2023-09-22T00:35:39.630Z", "updated_at": "2023-09-22T00:35:39.630Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Expanse-1695342946385.jpg?alt=media", "imageName": "The Expanse-1695342946385.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Expanse_(TV_series)", "youtube": ""}}, {"id": 23, "name": "<PERSON> (play)", "primary_type": "writings", "secondary_type": "Drama", "description": "<PERSON> Joan is a play by <PERSON> about 15th-century French military figure <PERSON>. Premiering in 1923, three years after her canonization by the Roman Catholic Church, the play reflects <PERSON>'s belief that the people involved in <PERSON>'s trial acted according to what they thought was right. He wrote in his preface to the play:\n\n❝ There are no villains in the piece. Crime, like disease, is not interesting: it is something to be done away with by general consent, and that is all [there is] about it. It is what men do at their best, with good intentions, and what normal men and women find that they must and will do in spite of their intentions, that really concern us. ❞\n\n<PERSON> characterised <PERSON> as \"A Chronicle Play in 6 Scenes and an Epilogue \". <PERSON>, a simple peasant girl, claims to experience visions of <PERSON>, <PERSON>, and the archangel <PERSON>, which she says were sent by <PERSON> to guide her conduct.", "release_date": "1924-08-08T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSaint <PERSON> (play)-16***********.gif?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2023-09-20T15:30:31.843Z", "updated_at": "2023-09-20T15:30:31.843Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSaint <PERSON> (play)-16***********.gif?alt=media", "imageName": "<PERSON> (play)-16***********.gif", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Saint%20Joan%20(play)", "youtube": ""}}, {"id": 107, "name": "Poetics (<PERSON>)", "primary_type": "book", "secondary_type": "Dramatic theory", "description": "<PERSON>'s Poetics is the earliest surviving work of Greek dramatic theory and the first extant philosophical treatise to focus on literary theory.\n\nIn this text <PERSON> offers an account of ποιητική, which refers to poetry and more literally \"the poetic art,\" deriving from the term for \"poet; author; maker,\" ποιητής. <PERSON> divides the art of poetry into verse drama (comedy, tragedy, and the satyr play), lyric poetry, and epic.\n\nThe genres all share the function of mimesis, or imitation of life, but differ in three ways that <PERSON> describes:\n\n• Differences in music rhythm, harmony, meter, and melody.\n• Difference of goodness in the characters.\n• Difference in how the narrative is presented: telling a story or acting it out.\n\nThe surviving book of Poetics is primarily concerned with drama; the analysis of tragedy constitutes the core of the discussion.", "release_date": "2023-08-16T14:41:55.092Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPoetics (<PERSON>)-*************.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2023-09-19T15:13:25.520Z", "updated_at": "2023-09-19T15:13:25.520Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPoetics (<PERSON>)-*************.jpg?alt=media", "imageName": "Poetics (<PERSON>)-*************.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Poetics_(<PERSON>)", "youtube": ""}}, {"id": 167, "name": "Panic", "primary_type": "tv_series", "secondary_type": "Drama", "description": "Panic is an American teen drama streaming television series created and written by <PERSON> based on her 2014 novel of the same name. The series stars <PERSON>, <PERSON>, and <PERSON>. The series premiered on Amazon Prime Video on May 28, 2021. In August 2021, the series was canceled after one season.\n\nThe plot: Every summer in a small Texas town, graduating seniors compete in a series of challenges, which they believe is their only chance to escape their circumstances and make their lives better.", "release_date": "2021-05-27T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPanic-1695134511962.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2023-09-19T14:41:46.182Z", "updated_at": "2023-09-19T14:41:46.182Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPanic-1695134511962.jpg?alt=media", "imageName": "Panic-1695134511962.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Panic_(TV_series)", "youtube": ""}}, {"id": 199, "name": "Tao, zen and existential psychotherapy", "primary_type": "other", "secondary_type": "Metapshychiatry", "description": "The purpose of this paper is to try to explain the psychotherapeutic process in a human life.", "release_date": "1959-08-16T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FTao, zen and existential psychotherapy-1695133132963.png?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2023-09-19T14:18:46.290Z", "updated_at": "2023-09-19T14:18:46.290Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FTao, zen and existential psychotherapy-1695133132963.png?alt=media", "imageName": "Tao, zen and existential psychotherapy-1695133132963.png", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "https://www.jstage.jst.go.jp/article/psysoc/2/4/2_1959.236/_pdf/-char/ja", "wikipedia": "", "youtube": ""}}, {"id": 89, "name": "L'esthétique - Ästhetik", "primary_type": "other", "secondary_type": "Art", "description": "Lectures on Aesthetics (LA; German: Vorlesungen über die Ästhetik, VÄ) is a compilation of notes from university lectures on aesthetics given by <PERSON> in Heidelberg in 1818 and in Berlin in 1820/21, 1823, 1826 and 1828/29. It was compiled in 1835 by his student <PERSON>, using <PERSON><PERSON>'s own hand-written notes and notes his students took during the lectures, but <PERSON><PERSON>'s work may render some of <PERSON><PERSON>'s thought more systematic than <PERSON><PERSON>'s initial presentation.\n\n<PERSON><PERSON> develops his account of art as a mode of absolute spirit that he calls \"the beautiful ideal\".", "release_date": "1832-08-16T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FL'esthétique - Ästhetik-*************.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2023-09-19T13:22:47.585Z", "updated_at": "2023-09-19T13:22:47.585Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FL'esthétique - Ästhetik-*************.jpg?alt=media", "imageName": "L'esthétique - Ästhetik-*************.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Lectures_on_Aesthetics", "youtube": ""}}, {"id": 70, "name": "Castlevania", "primary_type": "other", "secondary_type": "Fantasy", "description": "Inspired by the popular video game series, this anime series is a dark medieval fantasy. It follows the last surviving member of the disgraced Belmont clan, <PERSON>, trying to save Eastern Europe from extinction at the hands of <PERSON>. As <PERSON> and his legion of vampires prepare to rid the world of humanity's stain, <PERSON> is no longer alone, and he and his misfit comrades race to find a way to save mankind from the grief-maddened <PERSON>.", "release_date": "2017-07-06T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCastlevania-1621854733462.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-05-24T11:12:10.232Z", "updated_at": "2021-05-24T11:12:10.232Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCastlevania-1621854733462.jpg?alt=media", "image_name": "Castlevania-1621854733462.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/title/80095241", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Castlevania_(TV_series)", "youtube": ""}}, {"id": 37, "name": "The Woman in the Window", "primary_type": "film", "secondary_type": "Mystery/Drama", "description": "Agoraphobic Dr. <PERSON> witnesses something she shouldn't while keeping tabs on the <PERSON> family, the seemingly picture perfect clan that lives across the way.", "release_date": "2021-05-13T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Woman in the Window-1621853815774.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-05-24T10:56:52.757Z", "updated_at": "2021-05-24T10:56:52.757Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Woman in the Window-1621853815774.jpg?alt=media", "image_name": "The Woman in the Window-1621853815774.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/81092222", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Woman_in_the_Window_(2021_film)", "youtube": "https://youtu.be/v_0GJg_Jnlo"}}, {"id": 59, "name": "Les zécolonomiks", "primary_type": "other", "secondary_type": "Goods", "description": "Les Z’écolonomiks is an online shop sellings goods with an ecology intention. They distribute durable, sustainable, vegan, and zero waste products like soaps, bags, or cosmetics.", "release_date": "2018-08-31T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLes zécolonomiks-1619991291517.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-05-02T21:34:48.469Z", "updated_at": "2021-05-02T21:34:48.469Z", "urls": {"amazon": "", "facebook": "https://www.facebook.com/LesZecolonomiks/", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLes zécolonomiks-1619991291517.jpg?alt=media", "image_name": "Les zécolonomiks-1619991291517.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.les-zecolonomiks.com/", "wikipedia": "", "youtube": ""}}, {"id": 63, "name": "Mulholland Drive", "primary_type": "film", "secondary_type": "Mystery, Thriller", "description": "<PERSON>, a dark-haired amnesiac, and <PERSON>, a perky blonde actress, team up to find clues related to <PERSON>'s accident and ascertain her true identity.", "release_date": "2001-05-15T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMulholland Drive-1619988032653.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-05-02T20:40:29.567Z", "updated_at": "2021-05-02T20:40:29.567Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMulholland Drive-1619988032653.jpg?alt=media", "image_name": "Mulholland Drive-1619988032653.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/60021646", "prime_video": "https://www.primevideo.com/dp/amzn1.dv.gti.f0b31af3-62f9-02cf-bde9-3fd2f12dd6a1?autoplay=1&ref_=atv_cf_strg_wb", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Mulholland_Drive_(film)", "youtube": "https://www.youtube.com/watch?v=epu30GulbT8"}}, {"id": 203, "name": "<PERSON><PERSON>", "primary_type": "tv_series", "secondary_type": "Drama", "description": "Based on the 1996 <PERSON> novel of the same name, \"<PERSON><PERSON> Grace\" tells the story of young <PERSON>, a poor Irish immigrant and domestic servant in Upper Canada who is accused and convicted of the 1843 murder of her employer and his housekeeper. St<PERSON><PERSON> <PERSON> is also convicted of the crime. <PERSON><PERSON><PERSON><PERSON><PERSON> is hanged, but <PERSON> is sentenced to life in prison, leading her to become one of the most notorious women of the period in Canada. The story is based on actual 19th-century events.", "release_date": "2017-10-29T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FA<PERSON>s Grace-1619982677806.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-05-02T19:11:13.586Z", "updated_at": "2021-05-02T19:11:13.586Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FA<PERSON>s Grace-1619982677806.jpg?alt=media", "image_name": "<PERSON><PERSON>-1619982677806.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80119411", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(miniseries)", "youtube": ""}}, {"id": 204, "name": "Deadly illusions", "primary_type": "film", "secondary_type": "Thriller drama", "description": "Deadly Illusions is a 2021 American thriller drama film written and directed by <PERSON> and starring <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>.", "release_date": "2021-03-17T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDeadly illusions-1617622252496.png?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-04-05T11:30:48.298Z", "updated_at": "2021-04-05T11:30:48.298Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDeadly illusions-1617622252496.png?alt=media", "image_name": "Deadly illusions-1617622252496.png", "imdb": "https://www.imdb.com/title/tt7897330/", "instagram": "", "netflix": "https://www.netflix.com/title/81346196", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Deadly_Illusions", "youtube": ""}}, {"id": 119, "name": "The One", "primary_type": "tv_series", "secondary_type": "Science fiction drama", "description": "Love — and lies — spiral when a DNA researcher helps discover a way to find the perfect partner, and creates a bold new matchmaking service.", "release_date": "2021-03-11T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe One-1617369336140.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-04-02T13:15:31.546Z", "updated_at": "2021-04-02T13:15:31.546Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe One-1617369336140.jpg?alt=media", "image_name": "The One-1617369336140.jpg", "imdb": "https://www.imdb.com/title/tt13879466/", "instagram": "", "netflix": "https://www.netflix.com/title/80199029", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_One_(TV_series)", "youtube": ""}}, {"id": 139, "name": "Le Monde", "primary_type": "other", "secondary_type": "Social liberalism, social democracy", "description": "Le Monde is a French daily afternoon newspaper. It is the main publication of Le Monde Group and reported an average circulation of 323,039 copies per issue in 2009, about 40,000 of which were sold abroad.", "release_date": "1944-12-18T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLe Monde-1615245285739.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-03-08T23:14:44.098Z", "updated_at": "2021-03-08T23:14:44.098Z", "urls": {"amazon": "", "facebook": "https://www.facebook.com/lemonde.fr", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLe Monde-1615245285739.jpg?alt=media", "image_name": "Le Monde-1615245285739.jpg", "imdb": "", "instagram": "https://www.instagram.com/lemondefr/", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "https://twitter.com/lemondefr", "website": "https://www.lemonde.fr/", "wikipedia": "https://en.wikipedia.org/wiki/Le_Monde", "youtube": "https://www.youtube.com/channel/UCYpRDnhk5H8h16jpS84uqsA"}}, {"id": 184, "name": "<PERSON>", "primary_type": "film", "secondary_type": "Supernatural horror drama", "description": "Carrie is a 2013 American supernatural horror drama film directed by <PERSON>. It is the third film adaptation (the others were in 1976 and 2002) of <PERSON>'s 1974 novel of the same name and the fourth film in the Carrie franchise. It's the story of <PERSON>, a disturbed religious fanatic, who sits alone in her home on her bed and gives birth to a baby girl. She intends to kill the infant but changes her mind. Years later, her daughter <PERSON>, a shy, unassertive girl, experiences her first menstrual period, while showering, after the gym at school.", "release_date": "2013-10-06T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCarrie-1615244670568.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-03-08T23:04:26.580Z", "updated_at": "2021-03-08T23:04:26.580Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCarrie-1615244670568.jpg?alt=media", "image_name": "Carrie-1615244670568.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/70251537", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_(2013_film)", "youtube": ""}}, {"id": 178, "name": "La Nuit de la Culture", "primary_type": "other", "secondary_type": "Culture", "description": "\"La Nuit de la Culture\" is a program hosted by the french streamer <PERSON><PERSON><PERSON> on Twitch. He watches and plays with his viewers to \"Question pour un champion\", a french TV program from France 3. Because he can interacts with the chat, they can dig a subject and better understand a cultural material.", "release_date": "2019-04-30T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLa Nuit de la Culture-1614726478718.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-03-02T23:07:54.845Z", "updated_at": "2021-03-02T23:07:54.846Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLa Nuit de la Culture-1614726478718.jpg?alt=media", "image_name": "La Nuit de la Culture-1614726478718.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "https://www.twitch.tv/etoiles", "twitter": "", "website": "", "wikipedia": "https://fr.wikipedia.org/wiki/Etoiles_(streameur)", "youtube": ""}}, {"id": 98, "name": "Behind Her Eyes", "primary_type": "other", "secondary_type": "Supernatural psychological thriller", "description": "Behind Her Eyes is a British supernatural psychological thriller web series created by <PERSON>, based on the 2017 novel of the same name by <PERSON>, that premiered on Netflix on 17 February 2021. The plot tells a story of a single mother who enters a world of twisted mind games when she begins an affair with her psychiatrist boss while secretly befriending his mysterious wife.", "release_date": "2021-02-16T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBehind Her Eyes-1614721714859.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-03-02T21:48:31.257Z", "updated_at": "2021-03-02T21:48:31.258Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBehind Her Eyes-1614721714859.jpg?alt=media", "image_name": "Behind Her Eyes-1614721714859.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/title/80244630", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Behind_Her_Eyes_(British_TV_series)", "youtube": ""}}, {"id": 67, "name": "Firefly Lane", "primary_type": "other", "secondary_type": "Drama", "description": "Firefly Lane is an American drama streaming television series created by <PERSON> for Netflix. The series is based on the novel of the same name by <PERSON><PERSON>. The plot tells the story of <PERSON><PERSON> and <PERSON> who meet as young girls on Firefly Lane and become inseparable friends throughout 30 years of ups and downs.", "release_date": "2021-02-02T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FFirefly Lane-1614710083338.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-03-02T18:34:39.036Z", "updated_at": "2021-03-02T18:34:39.036Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FFirefly Lane-1614710083338.jpg?alt=media", "image_name": "Firefly Lane-1614710083338.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80994340", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Firefly_Lane", "youtube": ""}}, {"id": 53, "name": "Passengers", "primary_type": "film", "secondary_type": "Science fiction romance", "description": "Passengers is a 2016 American science fiction romance film directed by <PERSON><PERSON> and written by <PERSON>, partially based on the 1950s EC Comics story '50 Girls 50'. The plot os the film is: During a voyage to a distant colony planet, <PERSON>'s hypersleep pod malfunctions, waking him up.", "release_date": "2016-12-21T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPassengers-1614208672500.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-02-24T23:17:48.928Z", "updated_at": "2021-02-24T23:17:48.928Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPassengers-1614208672500.jpg?alt=media", "image_name": "Passengers-1614208672500.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80117456", "prime_video": "https://www.primevideo.com/dp/amzn1.dv.gti.50b53a56-01d6-bd25-1c5f-98f6cd3da59b", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Passengers_(2016_film)", "youtube": "https://www.youtube.com/watch?v=T_NgkMq53yo"}}, {"id": 221, "name": "Malcolm & Marie", "primary_type": "film", "secondary_type": "Black-and-white romantic drama", "description": "<PERSON> & Marie is a 2021 American black-and-white romantic drama film written, produced and directed by <PERSON>. The film stars <PERSON> and <PERSON><PERSON><PERSON> (who both also produced) as the title characters, a writer-director and his girlfriend, whose relationship is tested on the night of his latest film's premiere.", "release_date": "2021-02-04T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMalcolm & Marie-1614202345522.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-02-24T21:32:21.316Z", "updated_at": "2021-02-24T21:32:21.316Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMalcolm & Marie-1614202345522.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/title/81344370", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_%26_<PERSON>", "youtube": ""}}, {"id": 109, "name": "Phantom Thread", "primary_type": "film", "secondary_type": "Historical romance drama", "description": "Phantom Thread is a 2017 American historical drama film written and directed by <PERSON>, and starring <PERSON>, <PERSON> and <PERSON>. Set in 1950s London, it stars <PERSON><PERSON><PERSON> as an haute couture dressmaker who takes a young waitress, played by <PERSON><PERSON><PERSON>, as his muse.", "release_date": "2017-12-24T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPhantom Thread-1614102103927.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-02-23T17:41:40.228Z", "updated_at": "2021-02-23T17:41:40.228Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPhantom Thread-1614102103927.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80195447", "prime_video": "https://www.primevideo.com/dp/amzn1.dv.gti.52b6390a-2a07-3ddf-ed48-a07adc6c732c", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Phantom_Thread", "youtube": "https://www.youtube.com/watch?v=FxxDPcj-4WA"}}, {"id": 116, "name": "Vanilla Sky", "primary_type": "film", "secondary_type": "Science fiction psychological thriller", "description": "Vanilla Sky is a 2001 American science fiction psychological thriller film directed, written, and co-produced by <PERSON>. It is an English-language adaptation of <PERSON>'s 1997 Spanish film Open Your Eyes, which was written by <PERSON><PERSON><PERSON><PERSON> and <PERSON>, with <PERSON><PERSON><PERSON><PERSON> reprising her role from the original film. The film has been described as \"an odd mixture of science fiction, romance and reality warp\".", "release_date": "2002-01-22T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FVanilla Sky-1613600263078.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-02-10T21:05:57.553Z", "updated_at": "2021-02-10T21:05:57.553Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FVanilla Sky-1613600263078.jpg?alt=media", "image_name": "Vanilla Sky-1613600263078.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/60021786", "prime_video": "https://www.primevideo.com/dp/amzn1.dv.gti.d4ac18b8-4219-cfab-cd14-b1957e850d2c", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Vanilla_Sky", "youtube": "https://www.youtube.com/watch?v=jHfnCsmUBAo"}}, {"id": 66, "name": "The Dark Pictures Anthology: Little Hope", "primary_type": "other", "secondary_type": "Survival, Horror", "description": "The Dark Pictures Anthology: Little Hope, also known simply as Little Hope, is an interactive drama survival horror video game developed by Supermassive Games and published by Bandai Namco Entertainment. ", "release_date": "2020-10-29T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Dark Pictures Anthology: Little Hope-1611515777046.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-01-24T19:16:14.733Z", "updated_at": "2021-01-24T19:16:14.733Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Dark Pictures Anthology: Little Hope-1611515777046.jpg?alt=media", "image_name": "The Dark Pictures Anthology: <PERSON>-1611515777046.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.thedarkpictures.com/little-hope", "wikipedia": "https://en.wikipedia.org/wiki/The_Dark_Pictures_Anthology:_<PERSON>_<PERSON>", "youtube": "https://www.youtube.com/channel/UCFPrg1ERpYha46QVHTF75HA"}}, {"id": 54, "name": "Cyberpunk 2077", "primary_type": "other", "secondary_type": "Action role playing game", "description": "Cyberpunk 2077 is a 2020 action role-playing video game developed and published by CD Projekt. The story takes place in Night City, an open world set in the Cyberpunk universe.", "release_date": "2020-12-09T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCyberpunk 2077-1611515204246.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-01-24T19:06:42.112Z", "updated_at": "2021-01-24T19:06:42.112Z", "urls": {"amazon": "", "facebook": "https://www.facebook.com/CyberpunkGame", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCyberpunk 2077-1611515204246.jpg?alt=media", "image_name": "Cyberpunk 2077-1611515204246.jpg", "imdb": "", "instagram": "https://www.instagram.com/CyberpunkGame/", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "https://twitter.com/CyberpunkGame", "website": "https://www.cyberpunk.net", "wikipedia": "https://en.wikipedia.org/wiki/Cyberpunk_2077", "youtube": "https://www.youtube.com/channel/UC4zyoIAzmdsgpDZQfO1-lSA"}}, {"id": 187, "name": "Livre IV de l’Histoire des variations des églises protestantes", "primary_type": "book", "secondary_type": "Religious", "description": "A work from <PERSON> about anti Anti-Protestanism written between 1682 and 1688. It has 15 books. After pastor <PERSON>'s answer, <PERSON><PERSON><PERSON> wrote a sequel: \"les Avertissements aux protestants sur les lettres du ministre Jurieu contre l'Histoire des variations\".", "release_date": "1687-12-31T23:50:39.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLivre IV de l’Histoire des variations des églises protestantes-1611514456664.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-01-24T18:54:12.345Z", "updated_at": "2021-01-24T18:54:12.345Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLivre IV de l’Histoire des variations des églises protestantes-1611514456664.jpg?alt=media", "image_name": "Livre IV de l’Histoire des variations des églises protestantes-1611514456664.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://fr.wikipedia.org/wiki/Histoire_des_variations_des_%C3%89glises_protestantes", "youtube": ""}}, {"id": 164, "name": "City Hunter: .357 Magnum", "primary_type": "film", "secondary_type": "Action, animation", "description": "When a foreign dignitary is assassinated at a piano recital, and the pianist, along with her grandfather, are kidnapped, <PERSON> and his assistant <PERSON><PERSON> set off to rescue them.", "release_date": "1989-06-16T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fcity_hunter_amour_destin_magnum_357.jpg?alt=media&token=068138f5-57bc-461f-a2e1-e73d44f1ed6e", "views_count": 0, "likes_count": 0, "created_at": "2021-01-24T16:06:34.644Z", "updated_at": "2021-01-24T16:06:34.644Z", "urls": {"amazon": "https://www.amazon.fr/City-Hunter-amour-destin-magnum/dp/B000N6U2P4", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fcity_hunter_amour_destin_magnum_357.jpg?alt=media&token=068138f5-57bc-461f-a2e1-e73d44f1ed6e", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/60027409", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 179, "name": "The Other Boleyn Girl", "primary_type": "film", "secondary_type": "Romance, Drama", "description": "Centred on the the court of <PERSON>, this historical drama presents a calculating <PERSON>, who usurps her older sister <PERSON> as the king's mistress and ultimately desires to be his new queen.", "release_date": "2008-02-14T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Other Boleyn Girl-1611494860201.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-01-24T13:27:36.346Z", "updated_at": "2021-01-24T13:27:36.346Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Other Boleyn Girl-1611494860201.jpg?alt=media", "image_name": "The Other Boleyn Girl-1611494860201.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "https://www.primevideo.com/dp/amzn1.dv.gti.caac56bd-ae9b-e92a-5559-5fb8824f9305", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Other_<PERSON><PERSON><PERSON>_Girl_(2008_film)", "youtube": "https://www.youtube.com/watch?v=VXaOTiQMLOc"}}, {"id": 60, "name": "Letter to <PERSON><PERSON>", "primary_type": "other", "secondary_type": "Sciences", "description": "<PERSON>'s letter to <PERSON> talking about relativity theory.", "release_date": "1941-12-31T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLetter to <PERSON><PERSON>nc<PERSON>-1616030124745.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-01-14T22:39:47.972Z", "updated_at": "2021-01-14T22:39:47.972Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLetter to <PERSON><PERSON>nc<PERSON>-1616030124745.jpg?alt=media", "image_name": "Letter to <PERSON><PERSON>-1616030124745.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>", "youtube": ""}}, {"id": 150, "name": "Le parfum du bonheur est plus fort sous la pluie", "primary_type": "book", "secondary_type": "Novel", "description": "\"Le parfum du bonheur est plus fort sous la pluie\" is a french novel written by <PERSON><PERSON>. The novel tells the story of <PERSON> who finds herself alone with her 4-years old boy one day, and decides to go living back with her parents. Each chapter takes the reader back to her previous life.", "release_date": "2017-05-02T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLe parfum du bonheur est plus fort sous la pluie-1610239722430.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-01-10T00:48:39.162Z", "updated_at": "2021-01-10T00:48:39.162Z", "urls": {"amazon": "https://www.amazon.com/parfum-bonheur-plus-pluie-French/dp/2253088110", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLe parfum du bonheur est plus fort sous la pluie-1610239722430.jpg?alt=media", "image_name": "Le parfum du bonheur est plus fort sous la pluie-1610239722430.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.livredepoche.com/livre/le-parfum-du-bonheur-est-plus-fort-sous-la-pluie-9782253088110", "wikipedia": "", "youtube": ""}}, {"id": 211, "name": "Insomnie avec...", "primary_type": "media_stream", "secondary_type": "Interview", "description": "\"Insomnie avec...\" is a french YouTube interview series presented by <PERSON> and produced by FranceTV Slash. Interviews are informal and take place in two beds in which the guest talks about their life and career.", "release_date": "2013-09-12T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FInsomnie avec...-1610238225287.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-01-10T00:23:40.926Z", "updated_at": "2021-01-10T00:23:40.926Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FInsomnie avec...-1610238225287.jpg?alt=media", "image_name": "Insomnie avec...-1610238225287.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": "https://www.youtube.com/user/montelesonfrance4"}}, {"id": 58, "name": "Chilling Adventures of Sabrina", "primary_type": "tv_series", "secondary_type": "Supernatural horror, Dark fantasy", "description": "This adaptation of the \"<PERSON> the Teenage Witch\" tale is a dark coming-of-age story that traffics in horror and the occult. In the reimagined origin story, <PERSON> wrestles to reconcile her dual nature ― half-witch, half-mortal ― while standing against the evil forces that threaten her, her family ― including aunts <PERSON> and <PERSON><PERSON><PERSON> and the daylight world humans inhabit. <PERSON><PERSON><PERSON> (\"Mad Men\") leads the cast in the titular role of the show that is based on a comic series of the same name.", "release_date": "2018-10-25T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FChilling Adventures of Sabrina-1610233243606.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-01-09T23:00:39.218Z", "updated_at": "2021-01-09T23:00:39.218Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FChilling Adventures of Sabrina-1610233243606.jpg?alt=media", "image_name": "Chilling Adventures of Sabrina-1610233243606.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80223989", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Chilling_Adventures_of_<PERSON>_(TV_series)", "youtube": ""}}, {"id": 148, "name": "Celeste", "primary_type": "other", "secondary_type": "2D Platformer", "description": "Celeste is a 2018 platforming video game designed, directed and written by <PERSON> and programmed by <PERSON><PERSON> and <PERSON>. It is a fully-fleshed version of the 2016 game of the same name, which was made in four days solely by <PERSON><PERSON> and <PERSON> during a game jam.", "release_date": "2018-01-24T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCeleste-1610064909414.png?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-01-08T00:15:06.093Z", "updated_at": "2021-01-08T00:15:06.093Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCeleste-1610064909414.png?alt=media", "image_name": "Celeste-1610064909414.png", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "http://www.celestegame.com/", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_(video_game)", "youtube": ""}}, {"id": 86, "name": "Blood of Zeus", "primary_type": "other", "secondary_type": "Fantasy, action, adventure", "description": "Chronicles the illegitimate son of <PERSON>, a young man tasked with saving heaven and earth despite the interference of a vengeful goddess and her monstrous forces.", "release_date": "2020-10-26T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBlood of Zeus-1610062988568.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-01-07T23:43:06.336Z", "updated_at": "2021-01-07T23:43:06.336Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBlood of Zeus-1610062988568.jpg?alt=media", "image_name": "Blood of Zeus-1610062988568.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/81001988", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Blood_of_Zeus", "youtube": ""}}, {"id": 220, "name": "<PERSON>", "primary_type": "media_stream", "secondary_type": "Philosophy, psychologie, social", "description": "<PERSON> is the channel of a french YouTuber of the same name, talking about psychology, philosophy, social, among other subjects. He has a videos serie on philosophy where he explains concepts with a modern take.", "release_date": "2013-07-17T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCyrus North-1610062241274.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-01-07T23:30:37.525Z", "updated_at": "2021-01-07T23:30:37.525Z", "urls": {"amazon": "", "facebook": "https://www.facebook.com/cyrusnorth/", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCyrus North-1610062241274.jpg?alt=media", "image_name": "Cyrus North-1610062241274.jpg", "imdb": "", "instagram": "https://www.instagram.com/cyrusnorth/", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "https://twitter.com/cyrusnorth", "website": "", "wikipedia": "https://fr.wikipedia.org/wiki/Cyrus_<PERSON>", "youtube": "https://www.youtube.com/channel/UCah8C0gmLkdtvsy0b2jrjrw"}}, {"id": 143, "name": "Hades", "primary_type": "other", "secondary_type": "Roguelite action RPG", "description": "Hades is a roguelike action role-playing video game developed and published by Supergiant Games. The game was released for Microsoft Windows, macOS, and Nintendo Switch on September 17, 2020, which followed an early access release from December 2018.", "release_date": "2020-09-16T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FHades-1610059865722.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2021-01-07T22:51:02.082Z", "updated_at": "2021-01-07T22:51:02.082Z", "urls": {"amazon": "", "facebook": "https://www.facebook.com/supergiantgames", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FHades-1610059865722.jpg?alt=media", "image_name": "Hades-1610059865722.jpg", "imdb": "", "instagram": "https://www.instagram.com/supergiantgames/", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "https://twitter.com/SupergiantGames", "website": "https://www.supergiantgames.com/games/hades/", "wikipedia": "https://en.wikipedia.org/wiki/Hades_(video_game)", "youtube": "https://www.youtube.com/user/supergiantgames"}}, {"id": 180, "name": "The Tyger", "primary_type": "writings", "description": "\"The Tyger\" is a poem by the English poet <PERSON>, published in 1794 as part of his Songs of Experience collection. It has been the subject of both literary criticism and many adaptations, including various musical versions.", "release_date": "1793-12-31T23:50:39.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Tyger-1608725589382.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-12-23T12:13:08.043Z", "updated_at": "2020-12-23T12:13:08.043Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Tyger-1608725589382.jpg?alt=media", "image_name": "The Tyger-1608725589382.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Tyger", "youtube": ""}}, {"id": 55, "name": "Auguries of Innocence", "primary_type": "writings", "description": "\"Auguries of Innocence\" is a poem by <PERSON>, from a notebook of his now known as the Pickering Manuscript.[1] It is assumed to have been written in 1803, but was not published until 1863 in the companion volume to <PERSON>'s biography of <PERSON>.", "release_date": "1862-12-31T23:50:39.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAuguries of Innocence-1608725587040.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-12-23T12:13:05.513Z", "updated_at": "2020-12-23T12:13:05.513Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAuguries of Innocence-1608725587040.jpg?alt=media", "image_name": "Auguries of Innocence-1608725587040.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Auguries_of_Innocence", "youtube": ""}}, {"id": 18, "name": "A Poison Tree", "primary_type": "writings", "description": "\"A Poison Tree\" is a poem written by <PERSON>, published in 1794 as part of his Songs of Experience collection. It describes the narrator's repressed feelings of anger towards an individual, emotions which eventually lead to murder.", "release_date": "1783-12-31T23:50:39.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FA Poison Tree-1608725578743.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-12-23T12:12:56.391Z", "updated_at": "2020-12-23T12:12:56.391Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FA Poison Tree-1608725578743.jpg?alt=media", "image_name": "A Poison Tree-1608725578743.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/A_Poison_Tree", "youtube": ""}}, {"id": 2, "name": "The Marriage of Heaven and Hell", "primary_type": "book", "secondary_type": "Poetry", "description": "The Marriage of Heaven and Hell is a book by the English poet and printmaker <PERSON>. It is a series of texts written in imitation of biblical prophecy but expressing <PERSON>'s own intensely personal Romantic and revolutionary beliefs.", "release_date": "1793-12-31T23:50:39.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Marriage of Heaven and Hell-1608725577602.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-12-23T12:12:54.365Z", "updated_at": "2020-12-23T12:12:54.365Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Marriage of Heaven and Hell-1608725577602.jpg?alt=media", "image_name": "The Marriage of Heaven and Hell-1608725577602.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Marriage_of_Heaven_and_Hell", "youtube": ""}}, {"id": 209, "name": "The Divine Image", "primary_type": "writings", "description": "\"The Divine Image\" is a poem by the English poet <PERSON> from his book Songs of Innocence, not to be confused with \"A Divine Image\" from Songs of Experience. It was later included in his joint collection Songs of Innocence and of Experience.", "release_date": "1788-12-31T23:50:39.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Divine Image-1608725575591.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-12-23T12:12:51.855Z", "updated_at": "2020-12-23T12:12:51.855Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Divine Image-1608725575591.jpg?alt=media", "image_name": "The Divine Image-1608725575591.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Divine_Image", "youtube": ""}}, {"id": 172, "name": "Away", "primary_type": "tv_series", "secondary_type": "Science fiction drama", "description": "Away is an American science fiction drama streaming television series, starring <PERSON> and created by <PERSON> that premiered on Netflix on September 4, 2020. In October 2020, the series was canceled after one season...", "release_date": "2020-09-03T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAway-1607786752277.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-12-12T15:25:48.690Z", "updated_at": "2020-12-12T15:25:48.690Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAway-1607786752277.jpg?alt=media", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80214512", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Away_(TV_series)", "youtube": ""}}, {"id": 92, "name": "<PERSON>", "primary_type": "film", "secondary_type": "Romantic thriller", "description": "Rebecca is a 2020 British romantic thriller film directed by <PERSON> from a screenplay by <PERSON>, <PERSON>, and <PERSON>. The film is based on the 1938 novel of the same name by <PERSON>.\n\nThe plot is: while working as a companion to Mrs. <PERSON>, a rich American woman on holiday in Monte Carlo, an unnamed naïve young woman in her early 20s becomes acquainted with a wealthy Englishman, <PERSON>, a recent widower.", "release_date": "2020-10-15T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FRebecca-1607785191775.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-12-12T14:59:47.720Z", "updated_at": "2020-12-12T14:59:47.720Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FRebecca-1607785191775.jpg?alt=media", "image_name": "Rebecca-1607785191775.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/81002196", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_(2020_film)", "youtube": ""}}, {"id": 124, "name": "The Queen's Gambit", "primary_type": "tv_series", "secondary_type": "Historical drama", "description": "The Queen's Gambit is an American coming-of-age period drama streaming television miniseries based on <PERSON>'s 1983 novel of the same name, created for Netflix by <PERSON> and <PERSON>, and written and directed by the former. \n\nBeginning mid-1950s and proceeding into the 1960s, the story is about an orphaned chess prodigy on her rise to becoming the world's greatest chess player while struggling with emotional problems and drug and alcohol dependency.", "release_date": "2020-10-22T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Queen's Gambit-1607037282219.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-12-03T23:14:38.572Z", "updated_at": "2020-12-03T23:14:38.572Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Queen's Gambit-1607037282219.jpg?alt=media", "image_name": "The Queen's Gambit-1607037282219.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/title/80234304", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Queen%27s_Gambit_(miniseries)", "youtube": ""}}, {"id": 64, "name": "Dark", "primary_type": "tv_series", "secondary_type": "Sciences fiction thriller", "description": "Dark is a German science fiction thriller streaming television series co-created by <PERSON><PERSON> and <PERSON><PERSON><PERSON>.[5][6][7] It ran for three seasons from 2017 to 2020.\n\nThe plot is: When two children go missing in a small German town, its sinful past is exposed along with the double lives and fractured relationships that exist among four families as they search for the kids.\n\nThe mystery-drama series introduces an intricate puzzle filled with twists that includes a web of curious characters, all of whom have a connection to the town's troubled history -- whether they know it or not. The story includes supernatural elements that tie back to the same town in 1986. \"Dark\" represents the first German original series produced for Netflix.", "release_date": "2017-11-30T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDark-1604581863605.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-11-05T13:11:01.763Z", "updated_at": "2020-11-05T13:11:01.763Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDark-1604581863605.jpg?alt=media", "image_name": "Dark-1604581863605.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/title/80100172", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Dark_(TV_series)", "youtube": ""}}, {"id": 20, "name": "Modiiie", "primary_type": "other", "secondary_type": "Social/Psychology/Video games", "description": "<PERSON><PERSON><PERSON><PERSON> is a french Twitch streamer who has a channel talking about social sciences, psychology and video games. She also works as a teacher in political science. She's very active in multiple fields like social documentaries on YouTube, a personal blog, and live reading.", "release_date": "2012-12-31T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FModiiie-1604581157013.png?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-11-05T12:59:13.811Z", "updated_at": "2020-11-05T12:59:13.811Z", "urls": {"amazon": "", "facebook": "https://www.facebook.com/Modiie/", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FModiiie-1604581157013.png?alt=media", "image_name": "Modiiie-1604581157013.png", "imdb": "", "instagram": "https://www.instagram.com/m0diie/", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "https://www.twitch.tv/modiiie", "twitter": "https://twitter.com/m0diie", "website": "https://carnetmodiie.com/", "wikipedia": "", "youtube": "https://www.youtube.com/c/modiie"}}, {"id": 45, "name": "ZLAN", "primary_type": "other", "secondary_type": "Video games", "description": "ZLAN is a LAN video games competition organized by ZeratoR and ZQSD Productions. This competition has been shaped for live streaming and a lot of streamers take part in the competition. The event occur in a weekend with almost 200 players competing in duos on 10 selected games. There're always unexpected games like mental math or dictation. The four first teams share a 50k cash-prize.", "release_date": "2019-05-10T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FZLAN-1604580113107.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-11-05T12:41:49.873Z", "updated_at": "2020-11-05T12:41:49.873Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FZLAN-1604580113107.jpg?alt=media", "image_name": "ZLAN-1604580113107.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "https://twitter.com/zlan_fr", "website": "http://z-lan.fr/", "wikipedia": "", "youtube": ""}}, {"id": 103, "name": "Simba", "primary_type": "music", "secondary_type": "RAP", "description": "\"Simba\" is the 12th track on the PNL's album \"Que la famille\". The instrumental used is a “beat type” made by <PERSON><PERSON>zy Beats, and named Better Days.", "release_date": "2015-03-01T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSimba-1604576692379.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-11-05T11:44:48.969Z", "updated_at": "2020-11-05T11:44:48.969Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSimba-1604576692379.jpg?alt=media", "image_name": "Simba-1604576692379.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": "https://www.youtube.com/watch?v=6NonS60QIJA"}}, {"id": 205, "name": "Stupid Economics", "primary_type": "other", "secondary_type": "Economy", "description": "Stupid Economics is a french YouTube channel talking about economy with motion design, Monopoly money or investigations. The goal is to invite new people to be interested in the monetary mechanism.", "release_date": "2015-06-18T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FStupid Economics-1604564895283.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-11-05T08:28:11.650Z", "updated_at": "2020-11-05T08:28:11.650Z", "urls": {"amazon": "", "facebook": "https://www.facebook.com/Stupideconomics/", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FStupid Economics-1604564895283.jpg?alt=media", "image_name": "Stupid Economics-1604564895283.jpg", "imdb": "", "instagram": "https://www.instagram.com/stupideconomics", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "https://www.twitch.tv/stupideconomics", "twitter": "https://twitter.com/stupid_eco", "website": "https://en.tipeee.com/stupid-economics", "wikipedia": "", "youtube": "https://www.youtube.com/channel/UCyJDHgrsUKuWLe05GvC2lng"}}, {"id": 165, "name": "Quoi de neuf docteur ?", "primary_type": "other", "secondary_type": "Psychology/Social", "description": "\"Quoi de neuf docteur ?\" is a french live stream hosted by <PERSON><PERSON><PERSON><PERSON><PERSON> (DFG) and broadcasted on Twitch. Replays are also available on YouTube. In his stream, D<PERSON><PERSON> invites a personality and ask intimate questions about their life, work and well-being. Previously on Eclypsia, the broadcast is now available on DFG personal channels since 2020.", "release_date": "2015-09-09T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FQuoi%20de%20neuf%20docteur%20%3F.jpg?alt=media&token=87500e7c-6318-4fcd-9396-f679017877f8", "views_count": 0, "likes_count": 0, "created_at": "2020-11-05T08:08:55.564Z", "updated_at": "2020-11-05T08:08:55.564Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FQuoi%20de%20neuf%20docteur%20%3F.jpg?alt=media&token=87500e7c-6318-4fcd-9396-f679017877f8", "image_name": "Quoi de neuf docteur ?.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "https://www.twitch.tv/DrFeelGood", "twitter": "", "website": "", "wikipedia": "", "youtube": "https://www.youtube.com/playlist?list=PLx3rX2jtuB9-yp2EOBpeV-aSQEL_zYcQ9"}}, {"id": 149, "name": "I'm Thinking of Ending Things", "primary_type": "film", "secondary_type": "Thriller/Horror", "description": "I'm Thinking of Ending Things (stylized in lowercase as i'm thinking of ending things) is a 2020 American psychological horror film written and directed by <PERSON>. The film is based on the 2016 novel of the same name by <PERSON> and stars <PERSON>, <PERSON>, <PERSON> and <PERSON>.\n\nThe plot is: Full of misgivings, a young woman travels with her new boyfriend to his parents' secluded farm.", "release_date": "2020-09-03T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FI'm Thinking of Ending Things-1604539864719.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-11-05T01:31:01.080Z", "updated_at": "2020-11-05T01:31:01.080Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FI'm Thinking of Ending Things-1604539864719.jpg?alt=media", "image_name": "I'm Thinking of Ending Things-1604539864719.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80211559", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/I%27m_Thinking_of_Ending_Things_(film)", "youtube": ""}}, {"id": 222, "name": "Octopath Traveler", "primary_type": "other", "secondary_type": "Turn-based RPG", "description": "Octopath Traveler is a turn-based role-playing video game developed by Square Enix, in collaboration with Acquire. The game was released for the Nintendo Switch in July 2018, for Microsoft Windows in June 2019, and for Stadia in April 2020.", "release_date": "2018-07-12T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FOctopath Traveler-1604538925226.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-11-05T01:15:23.293Z", "updated_at": "2020-11-05T01:15:23.293Z", "urls": {"amazon": "https://www.amazon.fr/Nintendo-0045496422158-Octopath-Traveler/dp/B07BLD5NTQ", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FOctopath Traveler-1604538925226.jpg?alt=media", "image_name": "Octopath Traveler-1604538925226.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.nintendo.com/games/detail/octopath-traveler-switch/", "wikipedia": "https://en.wikipedia.org/wiki/Octopath_Traveler", "youtube": ""}}, {"id": 206, "name": "Oops!...I Did It Again", "primary_type": "music", "secondary_type": "Pop", "description": "“Oops!… I Did It Again” is a song recorded by <PERSON><PERSON><PERSON> for her second studio album, Oops!… I Did It Again (2000).\n\nIt was nominated for the Grammy Award for Best Female Pop Vocal Performance during the 2001 ceremony. Commercially, the track peaked at number nine on the U.S. Billboard Hot 100.\n\nProducer and writer <PERSON> intended this to be his version of <PERSON><PERSON>’s “Woman In Love.”\n\nLyrically, the song refers to a female who views love as a game, and she decides to use that to her advantage by playing with her lover's emotions. Its bridge features a dialogue which references the 1997 film Titanic.", "release_date": "2000-03-26T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FOops!...I Did It Again-1604538125262.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-11-05T01:02:01.818Z", "updated_at": "2020-11-05T01:02:01.818Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FOops!...I Did It Again-1604538125262.jpg?alt=media", "image_name": "Oops!...I Did It Again-1604538125262.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Oops!..._I_Did_It_Again_(song)", "youtube": "https://www.youtube.com/watch?v=CduA0TULnow"}}, {"id": 186, "name": "Chernobyl", "primary_type": "other", "secondary_type": "Historical drama", "description": "Chernobyl is a 2019 historical drama television miniseries that revolves around the nuclear disaster of the same name in 1986 and the cleanup efforts that followed. The series was created and written by <PERSON> and directed by <PERSON>.\n\nThe plot is: In April 1986, the city of Chernobyl in the Soviet Union suffers one of the worst nuclear disasters in the history of mankind. Consequently, many heroes put their lives on the line to save Europe.", "release_date": "2019-05-05T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FChernobyl-1604527872714.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-11-04T22:11:09.011Z", "updated_at": "2020-11-04T22:11:09.011Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FChernobyl-1604527872714.jpg?alt=media", "image_name": "Chernobyl-1604527872714.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "https://www.primevideo.com/dp/amzn1.dv.gti.32b68305-7d82-b7c6-e639-2951b785bedb", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Chernobyl_(miniseries)", "youtube": ""}}, {"id": 19, "name": "Ratched", "primary_type": "tv_series", "secondary_type": "Psychological thriller", "description": "Ratched is an American psychological thriller streaming television series about the character of the same name from <PERSON>'s 1962 novel One Flew Over the Cuckoo's Nest. Created by <PERSON> and developed by <PERSON>, the series stars <PERSON> in the title role and serves as a prequel to the novel.\n\nThe plot is a young nurse at a mental institution becomes jaded and bitter before turning into a full-fledged monster to her patients.", "release_date": "2020-07-17T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FRatched-1604526838388.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-11-04T21:53:54.767Z", "updated_at": "2020-11-04T21:53:54.767Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FRatched-1604526838388.jpg?alt=media", "image_name": "Ratched-1604526838388.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80213445", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Ratched_(TV_series)", "youtube": ""}}, {"id": 128, "name": "<PERSON>", "primary_type": "tv_series", "secondary_type": "True-crime anthology", "description": "\"Dirty John\" is an American true crime anthology television series, based on the podcast of the same name by <PERSON>, that premiered on November 25, 2018, on Bravo. \n\nIt explores the theme of love gone wrong, set against the backdrop of sunny Southern California. In the course of each season, viewers can follow a relationship from promising beginning to catastrophic end, raising the question of whether it's possible to really know someone, even the person one loves most. Through exploring the nuances and reframing the narratives of well-known cases, the series serves to remind the audience that there are two sides to every story, and people are often not quite as they seem.", "release_date": "2018-11-24T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDirty John-1604439845864.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-11-03T21:44:02.622Z", "updated_at": "2020-11-03T21:44:02.622Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDirty John-1604439845864.jpg?alt=media", "image_name": "Dirty John-1604439845864.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80241855", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_John_(TV_series)", "youtube": ""}}, {"id": 218, "name": "The Gift (Atiye)", "primary_type": "tv_series", "secondary_type": "Psychological thriller", "description": "The Gift (known in Turkey as At<PERSON><PERSON>) is a Turkish psychological thriller Netflix series starring <PERSON><PERSON>. It was written by <PERSON> and <PERSON><PERSON><PERSON>. The first season consists of 8 episodes and became available for streaming on Netflix on December 27, 2019.[1][2] The series is an adaptation of the novel <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> by <PERSON><PERSON><PERSON><PERSON>. The show tells the story of a painter in Istanbul embarks on a personal journey as she unearths universal secrets about an Anatolian archaeological site and its link to her past.", "release_date": "2019-12-26T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Gift (Atiye)-1604423035059.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-11-03T17:03:51.375Z", "updated_at": "2020-11-03T17:03:51.375Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Gift (Atiye)-1604423035059.jpg?alt=media", "image_name": "The Gift (Atiye)-1604423035059.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/title/81037848", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Gift_(Turkish_TV_series)", "youtube": ""}}, {"id": 24, "name": "Achieve Your Goals with <PERSON>", "primary_type": "podcast", "secondary_type": "Productivity", "description": "Achieve Your Goals with <PERSON> is a weekly podcast dedicated to empowering and equipping you with practical advice and strategies to achieve your goals and dreams. ", "release_date": "2013-12-31T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAchieve Your Goals with <PERSON>-1604422130539.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-11-03T16:48:46.796Z", "updated_at": "2020-11-03T16:48:46.796Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAchieve Your Goals with <PERSON>-1604422130539.jpg?alt=media", "image_name": "Achieve Your Goals with <PERSON>-1604422130539.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://halelrod.com/podcast/", "wikipedia": "", "youtube": ""}}, {"id": 141, "name": "Layers of Fear 2", "primary_type": "other", "secondary_type": "Horror", "description": "Layers of Fear 2 is a first-person psychological horror game with an emphasis on exploration and story. Players control a Hollywood actor who heeds the call of an enigmatic director to take on the lead role in a film shot aboard an ocean liner.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLayers of Fear 2-1604323296334.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-11-02T13:21:32.251Z", "updated_at": "2020-11-02T13:21:32.251Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLayers of Fear 2-1604323296334.jpg?alt=media", "image_name": "Layers of Fear 2-1604323296334.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://layersoffear2.com/", "wikipedia": "https://en.wikipedia.org/wiki/Layers_of_Fear", "youtube": ""}}, {"id": 4, "name": "Better Ideas", "primary_type": "media_stream", "secondary_type": "Lifestyle", "description": "Better ideas is a YouTube channel about life improvement, work optimisation and social among other topics. It's hosted by <PERSON>.", "release_date": "2017-01-22T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBetter Ideas-1603630075114.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-10-25T12:47:51.731Z", "updated_at": "2020-10-25T12:47:52.000Z", "urls": {"amazon": "", "facebook": "https://www.facebook.com/groups/179287156102224/", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBetter Ideas-1603630075114.jpg?alt=media", "image_name": "Better Ideas-1603630075114.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "https://twitter.com/joeysch<PERSON>tz", "website": "https://www.patreon.com/betterideastv/creators", "wikipedia": "", "youtube": "https://www.youtube.com/channel/UCtUId5WFnN82GdDy7DgaQ7w"}}, {"id": 56, "name": "La Révolution", "primary_type": "tv_series", "secondary_type": "Supernatural drama", "description": "La Révolution is a 2020 French-language supernatural drama series produced for Netflix starring <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>. In a reimagined history of the French Revolution, <PERSON> discovers that a virus is spreading among the nobility causing them to murder commoners.", "release_date": "2020-10-15T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLa Révolution-1603585384125.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-10-25T00:23:00.091Z", "updated_at": "2020-10-25T00:23:00.091Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLa Révolution-1603585384125.jpg?alt=media", "image_name": "La Révolution-1603585384125.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80992775", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/La_R%C3%A9volution", "youtube": ""}}, {"id": 213, "name": "<PERSON><PERSON><PERSON> puissa<PERSON>", "primary_type": "other", "secondary_type": "Culture and society", "description": "Femmes puissantes is a France Inter radio show on saturday, 12 p.m., hosted by <PERSON><PERSON><PERSON>. She receives a woman guest to discuss her life, projects, and goals.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FFemmes%20puissantes.jpg?alt=media&token=dbd8c401-45ff-4a1e-9b97-2339e7b71900", "views_count": 0, "likes_count": 0, "created_at": "2020-10-23T14:01:16.861Z", "updated_at": "2020-10-23T14:01:16.861Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FFemmes%20puissantes.jpg?alt=media&token=dbd8c401-45ff-4a1e-9b97-2339e7b71900", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.franceinter.fr/emissions/femmes-puissantes", "wikipedia": "", "youtube": ""}}, {"id": 145, "name": "The Haunting of Bly Manor", "primary_type": "tv_series", "secondary_type": "Horror", "description": "The Haunting of Bly Manor is an American supernatural horror drama television series, created by <PERSON> for Netflix, and loosely based on <PERSON>'s work, particularly his 1898 novella The Turn of the Screw. It is the follow-up series to The Haunting of Hill House and the second entry in The Haunting anthology series.", "release_date": "2020-10-08T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fthe_haunting_of_bly_manor.jpg?alt=media&token=f94d3bd5-bfb8-4d78-a373-4325723c6efa", "views_count": 0, "likes_count": 0, "created_at": "2020-10-21T23:44:46.777Z", "updated_at": "2020-10-21T23:44:46.777Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fthe_haunting_of_bly_manor.jpg?alt=media&token=f94d3bd5-bfb8-4d78-a373-4325723c6efa", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/81237854", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Haunting_of_Bly_Manor", "youtube": ""}}, {"id": 38, "name": "Animal Liberation", "primary_type": "book", "secondary_type": "Philosophy", "description": "Animal Liberation: A New Ethics for Our Treatment of Animals is a 1975 book by Australian philosopher <PERSON>. It is widely considered within the animal liberation movement to be the founding philosophical statement of its ideas. ", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAnimal_Liberation_1975.jpg?alt=media&token=5f85e1fa-5c79-46d8-9a82-e60d4c11fa86", "views_count": 0, "likes_count": 0, "created_at": "2020-09-22T11:58:49.591Z", "updated_at": "2020-09-22T11:58:49.591Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAnimal_Liberation_1975.jpg?alt=media&token=5f85e1fa-5c79-46d8-9a82-e60d4c11fa86", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Animal_Liberation_(book)", "youtube": ""}}, {"id": 11, "name": "The Man Who Laughs", "primary_type": "book", "secondary_type": "Novel", "description": "The Man Who Laughs is a novel by <PERSON>, originally published in April 1869 under the French title L'Homme qui rit. It takes place in England in the 1680s and 1700s, during the reigns of <PERSON> and <PERSON>, respectively, and depicts England's royalty and aristocracy of the time as cruel and power-hungry.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMan_Who_Laughs_1869.jpg?alt=media&token=1de1a823-6eb8-4731-97c6-67d95c289a59", "views_count": 0, "likes_count": 0, "created_at": "2020-09-22T11:52:02.363Z", "updated_at": "2020-09-22T11:52:02.363Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMan_Who_Laughs_1869.jpg?alt=media&token=1de1a823-6eb8-4731-97c6-67d95c289a59", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Man_Who_Laughs", "youtube": ""}}, {"id": 166, "name": "La chaîne de P.A.U.L", "primary_type": "media_stream", "secondary_type": "Documentary", "description": "La chaîne de P.A.U.L is a french YouTube channel making documentary about celebrities or fictional characters like the Joker, <PERSON> and <PERSON><PERSON><PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLa_chaine_de_paul.jpg?alt=media&token=bfc134c8-ac42-49d2-97d2-8653a42044bc", "views_count": 0, "likes_count": 0, "created_at": "2020-09-22T11:38:52.213Z", "updated_at": "2020-11-01T23:00:00.000Z", "urls": {"amazon": "", "facebook": "https://www.facebook.com/PAUL-vid%C3%A9os-1488687308084372/?ref=bookmarks", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLa_chaine_de_paul.jpg?alt=media&token=bfc134c8-ac42-49d2-97d2-8653a42044bc", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "https://twitter.com/PAUL_videos", "website": "https://fr.tipeee.com/p-a-u-l", "wikipedia": "", "youtube": "https://www.youtube.com/channel/UCS-NkJiYhYku2YtKi2W4p3w"}}, {"id": 50, "name": "<PERSON> Rire Jau<PERSON> (duo)", "primary_type": "media_stream", "secondary_type": "Sketches", "description": "<PERSON> Rire <PERSON> is two french YouTubers, <PERSON> and <PERSON>, which make humorous sketches videos on YouTube.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLe_Rire_Jaune.jpg?alt=media&token=aef2303c-4ae7-417e-bb5a-e34661e1b727", "views_count": 0, "likes_count": 0, "created_at": "2020-09-22T11:30:14.888Z", "updated_at": "2020-09-22T11:30:14.888Z", "urls": {"amazon": "", "facebook": "https://www.facebook.com/LeRireJauneOfficiel/", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLe_Rire_Jaune.jpg?alt=media&token=aef2303c-4ae7-417e-bb5a-e34661e1b727", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "https://www.twitch.tv/henrirejaune", "twitter": "https://twitter.com/SuperKevinTran", "website": "", "wikipedia": "https://fr.wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_(duo)", "youtube": "https://www.youtube.com/user/LeRiiiiiiiireJaune"}}, {"id": 74, "name": "GJow", "primary_type": "other", "secondary_type": "Gaming", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FGJow.png?alt=media&token=8ebe427e-e146-4fc6-8516-80d51935f4dd", "views_count": 0, "likes_count": 0, "created_at": "2020-09-22T11:30:12.473Z", "updated_at": "2020-09-22T11:30:12.473Z", "urls": {"amazon": "", "facebook": "https://www.facebook.com/GJowTv", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FGJow.png?alt=media&token=8ebe427e-e146-4fc6-8516-80d51935f4dd", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "https://www.twitch.tv/gjow", "twitter": "https://twitter.com/GJowTv", "website": "https://fr.tipeee.com/gjow-tv", "wikipedia": "", "youtube": "https://www.youtube.com/c/GJowTv"}}, {"id": 168, "name": "The Adventures of <PERSON><PERSON><PERSON> Finn", "primary_type": "book", "secondary_type": "Novel", "description": "Adventures of Huckleberry Finn is a novel by <PERSON>, first published in the United Kingdom in December 1884 and in the United States in February 1885.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FHuckleberry_Finn.jpg?alt=media&token=3a5f091c-b6e4-435e-b5ab-3514ecddcae8", "views_count": 0, "likes_count": 0, "created_at": "2020-09-22T10:53:29.656Z", "updated_at": "2020-09-22T10:53:29.657Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FHuckleberry_Finn.jpg?alt=media&token=3a5f091c-b6e4-435e-b5ab-3514ecddcae8", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Adventures_of_<PERSON><PERSON><PERSON>_<PERSON>", "youtube": ""}}, {"id": 62, "name": "Notion", "primary_type": "other", "secondary_type": "Knowloedge management", "description": "Notion is an application that provides components such as databases, kanban boards, wikis, calendars and reminders. Users can connect these components to create their own systems for knowledge management, note taking, data management, project management, among others.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FNotion_app_logo.png?alt=media&token=756b0e18-09c9-409d-bba1-c45cddfc7eef", "views_count": 0, "likes_count": 0, "created_at": "2020-09-19T18:40:12.182Z", "updated_at": "2020-09-19T18:40:12.182Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FNotion_app_logo.png?alt=media&token=756b0e18-09c9-409d-bba1-c45cddfc7eef", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.notion.so/", "wikipedia": "https://en.wikipedia.org/wiki/Notion_(app)", "youtube": ""}}, {"id": 113, "name": "Le RDV Tech", "primary_type": "podcast", "secondary_type": "Technologies", "description": "The RDV Tech is a fench news podcast on technologies, gadgets, internet.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Frdv_tech.png?alt=media&token=2255192b-d7cf-4136-8525-c5257a081eea", "views_count": 0, "likes_count": 0, "created_at": "2020-09-15T10:37:32.280Z", "updated_at": "2020-09-16T10:37:32.000Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Frdv_tech.png?alt=media&token=2255192b-d7cf-4136-8525-c5257a081eea", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://frenchspin.fr/category/le-rdv-tech/", "wikipedia": "", "youtube": ""}}, {"id": 81, "name": "Watchmen", "primary_type": "tv_series", "secondary_type": "Superhero drama", "description": "Watchmen is an American superhero drama limited television series based on the 1986 DC Comics series Watchmen, created by <PERSON> and <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fwatchmen_tv_series.jpg?alt=media&token=301432ef-0ef1-4eda-bb3d-2c0c24597019", "views_count": 0, "likes_count": 0, "created_at": "2020-09-15T09:58:59.541Z", "updated_at": "2020-09-15T09:58:59.541Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fwatchmen_tv_series.jpg?alt=media&token=301432ef-0ef1-4eda-bb3d-2c0c24597019", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.hbo.com/watchmen", "wikipedia": "https://en.wikipedia.org/wiki/Watchmen_(TV_series)", "youtube": ""}}, {"id": 111, "name": "Euphoria", "primary_type": "tv_series", "secondary_type": "Teen drama", "description": "Euphoria is an American teen drama television series created by <PERSON>, loosely based on the Israeli miniseries of the same name. Euphoria follows a group of high school students through their experiences of sex, drugs, friendships, love, identity and trauma. The series stars <PERSON><PERSON><PERSON> and premiered on HBO on June 16, 2019.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Feuphoria.jpg?alt=media&token=25af48b5-a810-4cf2-91f2-1831ad591bda", "views_count": 0, "likes_count": 0, "created_at": "2020-08-15T13:12:40.263Z", "updated_at": "2020-08-16T13:12:40.000Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Feuphoria.jpg?alt=media&token=25af48b5-a810-4cf2-91f2-1831ad591bda", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "https://twitter.com/euphoriaHBO", "website": "https://www.hbo.com/euphoria", "wikipedia": "https://en.wikipedia.org/wiki/Euphoria_(American_TV_series)", "youtube": "https://www.youtube.com/c/EuphoriaHBO"}}, {"id": 95, "name": "The Outsider", "primary_type": "tv_series", "secondary_type": "Horror, Crime, Drama", "description": "The Outsider is an American horror crime drama miniseries based on the 2018 novel of the same name by <PERSON>. It was ordered to series on December 3, 2018,[1] after being optioned as a miniseries by Media Rights Capital in June 2018.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe-Outsider.jpg?alt=media&token=5f1a3e47-4436-446d-9ed0-34905b5b3518", "views_count": 0, "likes_count": 0, "created_at": "2020-08-08T19:49:28.128Z", "updated_at": "2020-08-08T19:49:28.128Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe-Outsider.jpg?alt=media&token=5f1a3e47-4436-446d-9ed0-34905b5b3518", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "https://www.primevideo.com/detail/0GDRPSMGO410GZ30OGL0WNZPZD/ref=atv_sr_def_c_unkc__1_1_1?sr=1-1&pageTypeIdSource=ASIN&pageTypeId=B085L8BJN1&qid=1596958135", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Outsider_(miniseries)", "youtube": ""}}, {"id": 182, "name": "ZeratoR (Twitch)", "primary_type": "other", "secondary_type": "Video games", "description": "The ZeratoR TV is a french Twitch channel animated by <PERSON><PERSON><PERSON><PERSON> and his team. The channel broadcast various content from ESport events to independent games discovery. There are also special events like the ZEvent, ZLan and Trackmania Cup.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FZeratoR-Twitch.png?alt=media&token=9c88756e-43af-428d-8df4-209d4a27ada1", "views_count": 0, "likes_count": 0, "created_at": "2020-08-07T10:19:20.634Z", "updated_at": "2020-08-07T10:19:20.634Z", "urls": {"amazon": "", "facebook": "https://www.facebook.com/ZeratoR", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FZeratoR-Twitch.png?alt=media&token=9c88756e-43af-428d-8df4-209d4a27ada1", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "https://www.twitch.tv/zerator", "twitter": "https://twitter.com/ZeratoR", "website": "https://www.zerator.com/", "wikipedia": "https://fr.wikipedia.org/wiki/ZeratoR", "youtube": "https://www.youtube.com/channel/UCZ_oIYI9ZNpOfWbpZxWNuRQ"}}, {"id": 158, "name": "The Anatomy of Story", "primary_type": "book", "secondary_type": "Screenplay", "description": "Based on the lessons in his award-winning class, Great Screenwriting, The Anatomy of Story draws on a broad range of philosophy and mythology, offering fresh techniques and insightful anecdotes alongside <PERSON><PERSON><PERSON>'s own unique approach for how to build an effective, multifaceted narrative. ", "release_date": "2021-03-13T16:19:37.761Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Anatomy of Story-1615661667393.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-07-30T15:30:31.626Z", "updated_at": "2020-07-30T15:30:31.627Z", "urls": {"amazon": "https://www.amazon.com/Anatomy-Story-Becoming-Master-Storyteller-ebook/dp/B0052Z3M8A", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Anatomy of Story-1615661667393.jpg?alt=media", "image_name": "The Anatomy of Story-1615661667393.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://truby.com/the-anatomy-of-story/", "wikipedia": "", "youtube": ""}}, {"id": 216, "name": "<PERSON><PERSON><PERSON>", "primary_type": "media_stream", "secondary_type": "Cinematography", "description": "Clararunaway is a french YouTube channel speaking about cinema and TV shows. From fun facts about Interstellar to how to deal with the death of an actor in a production, the channel cover different aspects of this art.", "release_date": "2023-12-18T13:16:11.456Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fclararunaway.jpg?alt=media&token=ccb17c73-7ad1-43ec-956c-fd1029cb2693", "views_count": 0, "likes_count": 0, "created_at": "2020-07-30T15:30:24.583Z", "updated_at": "2020-07-30T15:30:24.583Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fclararunaway.jpg?alt=media&token=ccb17c73-7ad1-43ec-956c-fd1029cb2693", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "https://twitter.com/clararunaway", "website": "", "wikipedia": "", "youtube": "https://www.youtube.com/c/clararunaway/"}}, {"id": 32, "name": "<PERSON>", "primary_type": "tv_series", "secondary_type": "Fantasy drama", "description": "Warrior Nun is an American fantasy drama web television series created by <PERSON> based on the comic book character <PERSON> by <PERSON>.\n\nOriginally developed as a feature film adaptation, the idea was re-imagined as a television series for Netflix when the service had given the production a series order for a first season. Filming takes place in multiple locations in Spain. ", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fwarrior-nun.jpg?alt=media&token=ff2d9c8d-40b3-477d-a40d-a4b155259cc5", "views_count": 0, "likes_count": 0, "created_at": "2020-07-07T12:31:30.680Z", "updated_at": "2020-07-07T12:31:30.680Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fwarrior-nun.jpg?alt=media&token=ff2d9c8d-40b3-477d-a40d-a4b155259cc5", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80242724", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Warrior_Nun_(TV_series)", "youtube": ""}}, {"id": 106, "name": "The Art of Failure: An <PERSON>y on the Pain of Playing Video Games", "primary_type": "book", "secondary_type": "Video Games", "description": "We may think of video games as being \"fun,\" but in The Art of Failure, <PERSON><PERSON> claims that this is almost entirely mistaken. When we play video games, our facial expressions are rarely those of happiness or bliss. Instead, we frown, grimace, and shout in frustration as we lose, or die, or fail to advance to the next level. Humans may have a fundamental desire to succeed and feel competent, but game players choose to engage in an activity in which they are nearly certain to fail and feel incompetent. So why do we play video games even though they make us unhappy? <PERSON><PERSON> examines this paradox.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe%20Art%20of%20Failure.jpg?alt=media&token=cc5ba6f3-5487-47f0-90ce-683f2d2da033", "views_count": 0, "likes_count": 0, "created_at": "2020-07-01T22:50:01.795Z", "updated_at": "2020-07-01T22:50:01.795Z", "urls": {"amazon": "https://www.amazon.com/Art-Failure-Playing-Playful-Thinking/dp/0262529955", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe%20Art%20of%20Failure.jpg?alt=media&token=cc5ba6f3-5487-47f0-90ce-683f2d2da033", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://mitpress.mit.edu/books/art-failure", "wikipedia": "", "youtube": ""}}, {"id": 214, "name": "Dead to Me", "primary_type": "tv_series", "secondary_type": "Dark comedy", "description": "Dead to Me is an American dark comedy web television series created by <PERSON> and executively produced by <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. The series premiered on May 3, 2019, on Netflix and stars <PERSON> and <PERSON> as two grieving women who bond during therapy.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fdead_to_me.jpg?alt=media&token=3a232685-ef0d-4cc1-80b1-4e6090b2a59b", "views_count": 0, "likes_count": 0, "created_at": "2020-07-01T22:28:00.017Z", "updated_at": "2020-07-01T22:28:00.017Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fdead_to_me.jpg?alt=media&token=3a232685-ef0d-4cc1-80b1-4e6090b2a59b", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80219707", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Dead_to_Me_(TV_series)", "youtube": ""}}, {"id": 82, "name": "<PERSON>", "primary_type": "film", "secondary_type": "Documentary", "description": "The story of the trial, conviction and acquittal of <PERSON> for the murder of an exchange student in Italy.", "release_date": "2023-12-18T15:37:30.212Z", "image_url": "https://upload.wikimedia.org/wikipedia/en/f/f8/<PERSON>_<PERSON>_%28film%29.png", "views_count": 0, "likes_count": 0, "created_at": "2020-07-01T22:17:33.716Z", "updated_at": "2020-07-01T22:17:33.716Z", "urls": {"amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/f/f8/<PERSON>_<PERSON>_%28film%29.png", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80081155", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_<PERSON>_(film)", "youtube": ""}}, {"id": 100, "name": "True Story", "primary_type": "film", "secondary_type": "Drama", "description": "A disgraced New York Times journalist is accused of lying in a cover article. His troubles increase when he learns that he is a victim of identity theft and the man who stole his name is in jail.", "image_url": "https://upload.wikimedia.org/wikipedia/en/9/92/True_Story_poster.jpg", "views_count": 0, "likes_count": 0, "created_at": "2020-06-30T20:18:21.942Z", "updated_at": "2020-06-30T20:18:21.942Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/9/92/True_Story_poster.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/True_Story_(film)", "youtube": ""}}, {"id": 174, "name": "<PERSON><PERSON>", "primary_type": "film", "secondary_type": "Black comedy-drama ", "description": "<PERSON><PERSON><PERSON>, a fading cinema superhero, plans to resurrect his career with a passionate Broadway production. However, during rehearsals, his co-star is injured forcing him to hire a new actor.", "image_url": "https://upload.wikimedia.org/wikipedia/en/6/63/<PERSON><PERSON>_poster.png", "views_count": 0, "likes_count": 0, "created_at": "2020-06-28T09:11:22.919Z", "updated_at": "2020-06-28T09:11:22.919Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/6/63/<PERSON><PERSON>_poster.png", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80000643", "prime_video": "https://www.primevideo.com/dp/amzn1.dv.gti.46ab8695-f544-89f1-405a-2a181068ae21", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Birdman_(film)", "youtube": "http://www.youtube.com/watch?v=WvyDo35E_eA"}}, {"id": 30, "name": "WIRED by Design: A Game Designer Explains the Counterintuitive Secret to Fun", "primary_type": "speech", "secondary_type": "Game design", "description": "<PERSON> at WIRED by Design, 2014.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FWIRED_The_Design_of_Fun.png?alt=media&token=3db85464-6c21-4c93-b1eb-13ae65b10235", "views_count": 0, "likes_count": 0, "created_at": "2020-06-27T23:00:08.426Z", "updated_at": "2020-06-27T23:00:08.426Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FWIRED_The_Design_of_Fun.png?alt=media&token=3db85464-6c21-4c93-b1eb-13ae65b10235", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 10, "name": "Développeuse Du Dimanche", "primary_type": "media_stream", "secondary_type": "Video Games", "description": "Développeuse Du Dimanche is a YouTube channel created by <PERSON>, a french game designer. The channel talks about the process to make a game and analyze game concepts.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLola_Guilldou.jpg?alt=media&token=dc99d468-45bf-4811-9a90-a738b51236a0", "views_count": 0, "likes_count": 0, "created_at": "2020-06-26T11:58:52.736Z", "updated_at": "2020-06-26T11:58:52.736Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLola_Guilldou.jpg?alt=media&token=dc99d468-45bf-4811-9a90-a738b51236a0", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.lola-guilldou.com/", "wikipedia": "", "youtube": ""}}, {"id": 197, "name": "The woods", "primary_type": "other", "secondary_type": "Crime drama", "description": "The Woods is a Polish mystery thriller web television miniseries, based on the novel of the same name by <PERSON>. The series premiered on Netflix on 12 June 2020.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe_Woods_TV_Miniseries.jpg?alt=media&token=af340296-7687-49b8-b838-1fbbcef9d029", "views_count": 0, "likes_count": 0, "created_at": "2020-06-17T09:43:38.956Z", "updated_at": "2020-06-17T09:43:38.956Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe_Woods_TV_Miniseries.jpg?alt=media&token=af340296-7687-49b8-b838-1fbbcef9d029", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_<PERSON>_(miniseries)", "youtube": ""}}, {"id": 105, "name": "Soundtrack", "primary_type": "tv_series", "secondary_type": "Musical drama", "description": "Soundtrack is an American musical drama web television series created by <PERSON>, that premiered on Netflix on December 18, 2019.", "image_url": "https://upload.wikimedia.org/wikipedia/en/7/74/Soundtrack_%28TV_series%29_Title_Card.jpg", "views_count": 0, "likes_count": 0, "created_at": "2020-06-17T09:25:07.262Z", "updated_at": "2020-06-17T09:25:07.262Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/7/74/Soundtrack_%28TV_series%29_Title_Card.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Soundtrack_(TV_series)", "youtube": ""}}, {"id": 153, "name": "Dark (TV Series)", "primary_type": "other", "secondary_type": "<PERSON><PERSON>", "description": "Dark is a German science fiction thriller web television series co-created by <PERSON><PERSON> and <PERSON><PERSON><PERSON>.[5][6][7] Set in the town of Winden, Germany, <PERSON> concerns the aftermath of a child's disappearance which exposes the secrets of, and hidden connections among, four estranged families as they slowly unravel a sinister time travel conspiracy which spans three generations. Throughout the series, <PERSON> explores the existential implications of time and its effects upon human nature.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDark%20(TV%20series).jpg?alt=media&token=1fe9d8dc-70dd-40ab-b854-762abe95aacd", "views_count": 0, "likes_count": 0, "created_at": "2020-06-17T09:20:18.165Z", "updated_at": "2020-06-17T09:20:18.165Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDark%20(TV%20series).jpg?alt=media&token=1fe9d8dc-70dd-40ab-b854-762abe95aacd", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80100172", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Dark_(TV_series)", "youtube": ""}}, {"id": 156, "name": "ALT 236", "primary_type": "media_stream", "secondary_type": "Art", "description": "ALT 236 is a french YouTube channel about art, cinema, strange pictures and mythology. ", "release_date": "2023-12-10T20:25:24.986Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Falt_236.jpeg?alt=media&token=19786896-2a2b-42de-bb45-aaec3bb9c7cd", "views_count": 0, "likes_count": 0, "created_at": "2020-06-17T09:01:37.416Z", "updated_at": "2020-06-18T09:01:37.000Z", "urls": {"amazon": "", "facebook": "http://www.facebook.com/whatisalt236", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Falt_236.jpeg?alt=media&token=19786896-2a2b-42de-bb45-aaec3bb9c7cd", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "https://www.twitch.tv/whatisalt236", "twitter": "http://twitter.com/whatisalt236", "website": "", "wikipedia": "", "youtube": "https://www.youtube.com/channel/UC1KxoDAzbWOWOhw5GbsE-Bw"}}, {"id": 185, "name": "The Holiday", "primary_type": "film", "secondary_type": "Romantic comedy", "description": "The Holiday is a 2006 romantic comedy film written, produced and directed by <PERSON>. Co-produced by <PERSON>, it was filmed in both California and England, and stars <PERSON> and <PERSON> as <PERSON> and <PERSON>, two lovelorn women from opposite sides of the Atlantic Ocean, who arrange a home exchange to escape heartbreak during the Christmas and holiday season. <PERSON> and <PERSON> were cast as the film's leading men <PERSON> and <PERSON>, with <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> playing key supporting roles.", "image_url": "https://upload.wikimedia.org/wikipedia/en/6/60/Theholidayposter.jpg", "views_count": 0, "likes_count": 0, "created_at": "2020-06-04T10:40:36.389Z", "updated_at": "2020-06-04T10:40:36.389Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/6/60/Theholidayposter.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Holiday", "youtube": ""}}, {"id": 177, "name": "Sous couleur de jouer", "primary_type": "book", "secondary_type": "Psychology, Game", "description": "\"Sous couleur de jouer\" is a book talking about the concepts behind \"gamification\".", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSous%20couleur%20de%20jouer.jpg?alt=media&token=ef8a70d0-06dc-4d9b-a6d8-cd60f32665ee", "views_count": 0, "likes_count": 0, "created_at": "2020-06-04T10:05:16.701Z", "updated_at": "2020-06-04T10:05:16.701Z", "urls": {"affiliate": "https://www.amazon.fr/Sous-couleur-jouer-m%C3%A9taphore-ludique/dp/2714303250", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSous%20couleur%20de%20jouer.jpg?alt=media&token=ef8a70d0-06dc-4d9b-a6d8-cd60f32665ee", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 192, "name": "Hollywood", "primary_type": "other", "secondary_type": "Drama", "description": "Hollywood is an American drama web television miniseries released on May 1, 2020, on Netflix. The miniseries is about a group of aspiring actors and filmmakers during the Hollywood Golden Age in the post-World War II era trying to make their dreams come true. The series received mixed reviews from critics who praised the performances of the cast and production value, but criticized its tone, writing, and the artistic license taken.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/7/77/Hollywood_-_Netflix_series_title_card.png", "views_count": 0, "likes_count": 0, "created_at": "2020-05-16T20:57:38.960Z", "updated_at": "2020-05-16T20:57:38.960Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/commons/7/77/Hollywood_-_Netflix_series_title_card.png", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Hollywood_(miniseries)", "youtube": ""}}, {"id": 71, "name": "Ponce (Twitch)", "primary_type": "other", "secondary_type": "Video games", "description": "<PERSON>, also known as the flowers king, is a french Twitch streamer. He plays various video games like Mario Kart, <PERSON><PERSON><PERSON> or Outer Wilds. On mondays, he organizes culture games with his viewers.", "release_date": "2021-03-14T08:46:35.291Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPonce (Twitch)-1615711670731.png?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-05-13T16:47:13.470Z", "updated_at": "2020-05-13T16:47:13.470Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPonce (Twitch)-1615711670731.png?alt=media", "image_name": "Ponce (Twitch)-1615711670731.png", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://slowrun.fr/", "wikipedia": "", "youtube": ""}}, {"id": 77, "name": "Succeeding by <PERSON>", "primary_type": "book", "secondary_type": "Business", "description": "Succeeding is a business book writen by <PERSON> and published in 2003. The books talk about how to achieve high success, but it will constantly remind you to go for enough, not more; and to pursue what suits you, not just some goal chosen solely based on how much you think it will impress others.", "image_url": "https://cdn.shopify.com/s/files/1/0958/9924/files/succeedingcover200_large.gif?12664741428457080432", "views_count": 0, "likes_count": 0, "created_at": "2020-05-02T23:15:19.281Z", "updated_at": "2020-05-02T23:15:19.281Z", "urls": {"affiliate": "https://www.amazon.com/Succeeding-<PERSON>-<PERSON>-2003-05-03/dp/B01FIWWAK2", "amazon": "", "facebook": "", "image": "https://cdn.shopify.com/s/files/1/0958/9924/files/succeedingcover200_large.gif?12664741428457080432", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, {"id": 96, "name": "<PERSON> <PERSON>", "primary_type": "film", "secondary_type": "Romantic", "description": "Meet Joe Black is a 1998 American romantic fantasy film directed and produced by <PERSON>, and starring <PERSON>, <PERSON>, and <PERSON>. It was the second pairing of <PERSON> and <PERSON> after their 1994 film Legends of the Fall. The film received mixed reviews from critics. It grossed $143 million worldwide. ", "image_url": "https://upload.wikimedia.org/wikipedia/en/f/f5/Meet_<PERSON>_<PERSON>-_1998.jpg", "views_count": 0, "likes_count": 0, "created_at": "2020-05-02T02:10:07.679Z", "updated_at": "2020-05-02T02:10:07.679Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/f/f5/Meet_<PERSON>_<PERSON>-_1998.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Meet_<PERSON>_<PERSON>", "youtube": ""}}, {"id": 47, "name": "The Great Gatsby (2013 film)", "primary_type": "film", "secondary_type": "Romantic, Drama", "description": "The Great Gatsby is a 2013 romantic drama film based on <PERSON><PERSON>'s 1925 novel of the same name. The film was co-written and directed by <PERSON><PERSON> and stars <PERSON> as the eponymous <PERSON>, with <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON>.", "image_url": "https://upload.wikimedia.org/wikipedia/en/c/c2/TheGreatGatsby2013Poster.jpg", "views_count": 0, "likes_count": 0, "created_at": "2020-04-23T23:08:58.491Z", "updated_at": "2020-04-23T23:08:58.491Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/c/c2/TheGreatGatsby2013Poster.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Great_Gatsby_(2013_film)", "youtube": ""}}, {"id": 133, "name": "OkCupid", "primary_type": "other", "secondary_type": "Dating", "description": "OkCupid is an American-based, internationally operating online dating, friendship, and social networking website that features multiple-choice questions in order to match members. It is supported by advertisements, by paying users who do not see ads, and by selling user data for data mining.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fokcupid.png?alt=media&token=a8bef069-60b7-4b77-8e9d-9eebb5e821f0", "views_count": 0, "likes_count": 0, "created_at": "2020-04-22T22:15:18.403Z", "updated_at": "2020-04-23T22:15:18.000Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fokcupid.png?alt=media&token=a8bef069-60b7-4b77-8e9d-9eebb5e821f0", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.okcupid.com/", "wikipedia": "https://en.wikipedia.org/wiki/OkCupid", "youtube": ""}}, {"id": 17, "name": "Unorthodox (miniseries)", "primary_type": "tv_series", "secondary_type": "Drama, Miniseries", "description": "Unorthodox is a German-American drama miniseries that debuted on Netflix on March 26, 2020. The series is loosely based on <PERSON>'s 2012 autobiography Unorthodox: The Scandalous Rejection of My Hasidic Roots. It is the first Netflix series to be primarily in Yiddish. ", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FUnorthodox%20(miniseries).jpg?alt=media&token=1d2e8193-6304-442e-9081-3860bd1bd241", "views_count": 0, "likes_count": 0, "created_at": "2020-04-22T22:11:03.925Z", "updated_at": "2020-04-22T22:11:03.926Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FUnorthodox%20(miniseries).jpg?alt=media&token=1d2e8193-6304-442e-9081-3860bd1bd241", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Unorthodox_(miniseries)", "youtube": ""}}, {"id": 190, "name": "The Nice Guys", "primary_type": "film", "secondary_type": "Comedy/Action", "description": "The Nice Guys is a 2016 American neo-noir crime black comedy film directed by <PERSON> and written by <PERSON> and <PERSON>. The film stars <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON>. Set in 1977 Los Angeles, the film focuses on a private eye (<PERSON><PERSON><PERSON>) and a tough enforcer (<PERSON><PERSON>) who team up to investigate the disappearance of a teenage girl (<PERSON><PERSON><PERSON>). ", "image_url": "https://upload.wikimedia.org/wikipedia/en/e/e9/The_Nice_Guys_poster.png", "views_count": 0, "likes_count": 0, "created_at": "2020-04-12T23:44:56.457Z", "updated_at": "2020-04-12T23:44:56.457Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/e/e9/The_Nice_Guys_poster.png", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Nice_Guys", "youtube": ""}}, {"id": 173, "name": "<PERSON><PERSON><PERSON>", "primary_type": "other", "secondary_type": "Design", "description": "Dribbble is a self-promotion and social networking platform for digital designers and creatives. It serves as a design portfolio platform, jobs and recruiting site and is one of the largest platforms for designers to share their work online. The company is fully remote with no headquarters.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fdribbble-ball-icon.png?alt=media&token=9cf965c9-da27-4659-ac58-46514d34ea55", "views_count": 0, "likes_count": 0, "created_at": "2020-04-11T09:25:09.014Z", "updated_at": "2020-04-11T09:25:09.014Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fdribbble-ball-icon.png?alt=media&token=9cf965c9-da27-4659-ac58-46514d34ea55", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://dribbble.com/", "wikipedia": "https://en.wikipedia.org/wiki/Drib<PERSON>", "youtube": ""}}, {"id": 21, "name": "<PERSON> (TV series)", "primary_type": "tv_series", "secondary_type": "Crime", "description": "Freud is an Austrian-German crime television series re-imagining the life of a young <PERSON><PERSON><PERSON>. 8 episodes have been produced, with the first series airing on 23 March 2020 on Netflix. While investigating several disappearances and murders in Vienna, young <PERSON><PERSON><PERSON> and a psychic medium become entangled in an occult conspiracy.", "image_url": "https://upload.wikimedia.org/wikipedia/en/4/4c/Freud_Netflix.jpg", "views_count": 0, "likes_count": 0, "created_at": "2020-04-08T06:59:27.694Z", "updated_at": "2020-04-08T06:59:27.694Z", "urls": {"affiliate": "https://www.netflix.com/watch/80209184", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/4/4c/Freud_Netflix.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Freud_(TV_series)", "youtube": ""}}, {"id": 160, "name": "Enemy", "primary_type": "film", "secondary_type": "Thriller/Erotic thriller", "description": "<PERSON>, a college professor, spots an actor in a movie who looks exactly like him. <PERSON> tracks down his doppelganger and starts living his life secretly, which gives birth to a complex situation.", "image_url": "https://upload.wikimedia.org/wikipedia/en/0/0d/Enemy_poster.jpg", "views_count": 0, "likes_count": 0, "created_at": "2020-04-08T06:47:12.684Z", "updated_at": "2020-04-08T06:47:12.684Z", "urls": {"affiliate": "https://www.netflix.com/watch/70293661", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/0/0d/Enemy_poster.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Enemy_(2013_film)", "youtube": ""}}, {"id": 97, "name": "Extremely Wicked, Shockingly Evil and Vile", "primary_type": "film", "secondary_type": "Biographical Crime Thriller", "description": "Extremely Wicked, Shockingly Evil and Vile is a 2019 American biographical crime thriller film about the life of serial killer <PERSON>. Directed by <PERSON> with a screenplay from <PERSON>, the film is based on <PERSON><PERSON>'s former girlfriend <PERSON>'s memoir, The Phantom Prince: My Life with <PERSON>. The film stars <PERSON><PERSON> as <PERSON><PERSON>, <PERSON> as <PERSON>, <PERSON><PERSON> as <PERSON><PERSON>'s wife <PERSON>, and <PERSON> as <PERSON>, the presiding judge at <PERSON><PERSON>'s trial. The title of the film is a reference to <PERSON><PERSON>'s remarks on <PERSON><PERSON>'s murders while sentencing him to death.", "image_url": "https://upload.wikimedia.org/wikipedia/en/8/8d/Extremely_Wicked%2C_Shockingly_Evil%2C_and_Vile_poster.png", "views_count": 0, "likes_count": 0, "created_at": "2020-04-06T22:46:00.841Z", "updated_at": "2020-04-06T22:46:00.841Z", "urls": {"affiliate": "https://www.netflix.com/title/81028570", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/8/8d/Extremely_Wicked%2C_Shockingly_Evil%2C_and_Vile_poster.png", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/title/81028570", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Extremely_Wicked,_Shockingly_Evil_and_Vile", "youtube": ""}}, {"id": 202, "name": "<PERSON> (Channel)", "primary_type": "media_stream", "secondary_type": "Game", "description": "<PERSON> makes videos where he talks about game development and art. Two of his games are: 'Pinstripe' and 'Coma'.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F%20Thomas%20Brush%20(Channel).png?alt=media&token=1312abbb-e0a7-428a-82cb-cce93d6cd19a", "views_count": 0, "likes_count": 0, "created_at": "2020-04-06T19:38:53.758Z", "updated_at": "2020-04-06T19:38:53.758Z", "urls": {"affiliate": "https://www.youtube.com/user/thomasmbrush/", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F%20Thomas%20Brush%20(Channel).png?alt=media&token=1312abbb-e0a7-428a-82cb-cce93d6cd19a", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "http://atmosgames.com/", "wikipedia": "", "youtube": "https://www.youtube.com/user/thomasmbrush/"}}, {"id": 138, "name": "National Assembly - Public session (31/03/2020)", "primary_type": "other", "secondary_type": "Politics", "description": "The subjects in this public session were: the strategy to fight COVID-19, domestic violence, medication, and financial aid, among others. <PERSON><PERSON> and <PERSON> attended this session.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAssemble%CC%81e%20Nationale.png?alt=media&token=684b9d8c-e258-43da-8fc3-a0c1e5d4952f", "views_count": 0, "likes_count": 0, "created_at": "2020-03-31T15:58:27.846Z", "updated_at": "2020-03-31T15:58:27.846Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAssemble%CC%81e%20Nationale.png?alt=media&token=684b9d8c-e258-43da-8fc3-a0c1e5d4952f", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "http://videos.assemblee-nationale.fr/video.8924502_5e833cf5c4761.1ere-seance--questions-au-gouvernement-31-mars-2020", "wikipedia": "", "youtube": ""}}, {"id": 84, "name": "La Cité de la peur", "primary_type": "film", "secondary_type": "Comedy", "description": "La Cité de la peur (French: \"The City of Fear\"), also known as Le film de Les Nuls (\"The Les Nuls Movie\"), is a 1994 French comedy film written by and starring <PERSON><PERSON>, <PERSON> and <PERSON>. The movie parodies big budget American films (Basic Instinct, Pretty Woman and The Terminator, among others, are directly spoofed) and relies heavily on puns and word play, which makes it somewhat inaccessible for non-French speakers. ", "image_url": "https://upload.wikimedia.org/wikipedia/en/a/ac/La_Cit%C3%A9_de_la_peur.jpg", "views_count": 0, "likes_count": 0, "created_at": "2020-03-30T01:15:56.583Z", "updated_at": "2020-03-30T01:15:56.583Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/a/ac/La_Cit%C3%A9_de_la_peur.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/La_Cit%C3%A9_de_la_peur", "youtube": ""}}, {"id": 72, "name": "Futura", "primary_type": "other", "secondary_type": "Sciences", "description": "Futura is a french online newspaper created in 2001 which delivers news with a scientific approach. Its main themes are: sciences, technology, planet, health, housing. Futura wants to improve critical mind and bring fair information. An experts group work regularly with the newspaper.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FFutura.png?alt=media&token=97fe325d-caf5-424c-baa2-19ef2967269b", "views_count": 0, "likes_count": 0, "created_at": "2020-03-28T23:30:22.751Z", "updated_at": "2020-03-28T23:30:22.751Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FFutura.png?alt=media&token=97fe325d-caf5-424c-baa2-19ef2967269b", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.futura-sciences.com/", "wikipedia": "https://fr.wikipedia.org/wiki/Futura_(portail_web)", "youtube": ""}}, {"id": 126, "name": "<PERSON><PERSON>gi<PERSON>", "primary_type": "media_stream", "secondary_type": "Sciences", "description": "Hygiène Mentale is a french sciences channel created by <PERSON><PERSON><PERSON><PERSON>. His videos teach how to have a critical mind and how search for information. He also work on paranormal events.", "release_date": "2021-03-18T00:36:39.009Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FHygiène Mentale-1616028195178.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-03-28T23:29:40.789Z", "updated_at": "2020-03-28T23:29:40.789Z", "urls": {"amazon": "", "facebook": "https://www.facebook.com/HygieneMentale/", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FHygiène Mentale-1616028195178.jpg?alt=media", "image_name": "Hygiène Mentale-1616028195178.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "https://twitter.com/hygienementale", "website": "http://laelith.fr/Zet/Episodes/", "wikipedia": "https://fr.wikipedia.org/wiki/<PERSON>_<PERSON>", "youtube": "https://www.youtube.com/channel/UCMFcMhePnH4onVHt2-ItPZw"}}, {"id": 127, "name": "Covid-19 Conference (28/03/2020)", "primary_type": "speech", "secondary_type": "Health", "description": "On Saturday, 28 March, 2020, the Prime Minister of France gave a conference with <PERSON>, the Health Minister, about the situation of the COVID-19. Several scientists came to explain how the country is fighting the situation and what to expect in the coming weeks.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCOVID-19.png?alt=media&token=32f07736-8ff6-4a1f-9510-759d54eb72f4", "views_count": 0, "likes_count": 0, "created_at": "2020-03-28T23:29:28.797Z", "updated_at": "2020-03-28T23:29:28.797Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCOVID-19.png?alt=media&token=32f07736-8ff6-4a1f-9510-759d54eb72f4", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.pscp.tv/w/1dRKZQXQWqDxB", "wikipedia": "", "youtube": ""}}, {"id": 157, "name": "<PERSON><PERSON><PERSON>", "primary_type": "other", "secondary_type": "Video Games", "description": "<PERSON><PERSON><PERSON> is a french Twitch streamer. She plays various video games including adventure, FPS (First Person Shooter) or horror. Her streams contain also manual craft and cooking amoung other activites.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fmaghla.jpg?alt=media&token=57f571dc-9b28-4915-b5af-b52741303259", "views_count": 0, "likes_count": 0, "created_at": "2020-03-27T12:46:10.364Z", "updated_at": "2020-03-27T12:46:10.364Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fmaghla.jpg?alt=media&token=57f571dc-9b28-4915-b5af-b52741303259", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "primevideo": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.twitch.tv/maghla", "wikipedia": "", "youtube": ""}}, {"id": 93, "name": "Accropolis", "primary_type": "other", "secondary_type": "Politics", "description": "Inspired from video games platforms, Accropolis brings knowledge to french citizens who want to understand public issues and take back democracy. Shows are dynamically animated with a chat where everyone can interact with the host.", "release_date": "2023-12-18T13:46:50.104Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fjeanmassiet.jpg?alt=media&token=7a0dfbe1-7d80-4353-bb92-56ad5f5ab9dd", "views_count": 0, "likes_count": 0, "created_at": "2020-03-27T12:37:06.900Z", "updated_at": "2020-03-27T12:37:06.901Z", "urls": {"amazon": "", "facebook": "https://www.facebook.com/Accropolis", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fjeanmassiet.jpg?alt=media&token=7a0dfbe1-7d80-4353-bb92-56ad5f5ab9dd", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "https://www.twitch.tv/accropolis", "twitter": "https://twitter.com/Accropolis", "website": "https://accropolis.fr/", "wikipedia": "", "youtube": "https://www.youtube.com/channel/UCqv_wXmLSFtTDA39HQaLssQ"}}, {"id": 159, "name": "Pixeland", "primary_type": "other", "secondary_type": "Games", "description": "Pixeland is an independent community for game developers, artists and students from all over the world. We welcome all members no matter how much experience they have.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fpixeland.png?alt=media&token=25541d3c-561a-4aee-8b67-5beb36feb6e6", "views_count": 0, "likes_count": 0, "created_at": "2020-03-27T11:26:02.344Z", "updated_at": "2020-03-27T11:26:02.344Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fpixeland.png?alt=media&token=25541d3c-561a-4aee-8b67-5beb36feb6e6", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://pixeland.io/", "wikipedia": "", "youtube": ""}}, {"id": 217, "name": "<PERSON><PERSON>", "primary_type": "media_stream", "secondary_type": "Sciences", "description": "Balade Mentale is a french YouTube channel exploring the Universe in poetry. It explores topics like the ending of the Universe, stars' lifespan, or the Library of Babel.", "image_url": "https://yt3.ggpht.com/a/AATXAJyIaTK7HHmPeyfI35MF7EEG3yrMw43vazxx8A=s288-c-k-c0xffffffff-no-rj-mo", "views_count": 0, "likes_count": 0, "created_at": "2020-03-23T19:29:49.100Z", "updated_at": "2020-03-23T19:29:49.100Z", "urls": {"affiliate": "https://en.tipeee.com/baladementale", "amazon": "", "facebook": "", "image": "https://yt3.ggpht.com/a/AATXAJyIaTK7HHmPeyfI35MF7EEG3yrMw43vazxx8A=s288-c-k-c0xffffffff-no-rj-mo", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.youtube.com/channel/UCS_7tplUgzJG4DhA16re5Yg/featured", "wikipedia": "", "youtube": ""}}, {"id": 15, "name": "SoCraTes", "primary_type": "speech", "secondary_type": "Sciences", "description": "SoCraTes UK is a non-profit, international Software Crafters retreat for open-minded crafters who want to improve their craft and the software industry as a whole. SoCraTes UK is totally community-focused. It's a great opportunity to speak to and code with many like-minded and talented developers in a very relaxed and beautiful setting.", "image_url": "https://socratesuk.org/img/SoCraTes_UK_circle.png", "views_count": 0, "likes_count": 0, "created_at": "2020-03-23T00:32:12.823Z", "updated_at": "2020-03-23T00:32:12.824Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://socratesuk.org/img/SoCraTes_UK_circle.png", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://socratesuk.org/", "wikipedia": "", "youtube": ""}}, {"id": 154, "name": "All the Bright Places", "primary_type": "film", "secondary_type": "Drama/Romance", "description": "After meeting each other, two people struggle with the emotional and physical scars of their past. They discover that even the smallest moments can mean something.", "release_date": "2020-02-27T23:00:00.000Z", "image_url": "https://upload.wikimedia.org/wikipedia/en/8/8c/All_the_Bright_Places.jpeg", "views_count": 0, "likes_count": 0, "created_at": "2020-03-22T23:00:00.000Z", "updated_at": "2020-03-22T23:00:00.000Z", "urls": {"amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/8/8c/All_the_Bright_Places.jpeg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "https://www.netflix.com/watch/80208802?source=35", "wikipedia": "https://en.wikipedia.org/wiki/All_the_Bright_Places_(film)", "youtube": ""}}, {"id": 170, "name": "Unbelievable (miniseries)", "primary_type": "other", "secondary_type": "Drama", "description": "Unbelievable is an American drama web television miniseries about a series of rapes in Washington and Colorado. It s based on the 2015 news article \"An Unbelievable Story of Rape\", written by <PERSON><PERSON> and <PERSON>, and originally published by ProPublica and The Marshall Project. The series received critical acclaim.", "image_url": "https://upload.wikimedia.org/wikipedia/en/d/d5/Unbelievable_%28miniseries%29_Title_Card.jpg", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.343Z", "updated_at": "2020-02-01T22:11:00.343Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/d/d5/Unbelievable_%28miniseries%29_Title_Card.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.netflix.com/watch/80153467", "wikipedia": "https://www.google.com/url?sa=t&rct=j&q=&esrc=s&source=web&cd=11&cad=rja&uact=8&ved=2ahUKEwi3vpCLkJ_nAhWMxYUKHVW9BGUQFjAKegQIARAB&url=https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FUnbelievable_(miniseries)&usg=AOvVaw1HDLT3QG-2RO_8OUSLv-3t", "youtube": ""}}, {"id": 171, "name": "On the Genealogy of Morality", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.343Z", "updated_at": "2020-02-01T22:11:00.343Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/On_the_Genealogy_of_Morality", "youtube": ""}}, {"id": 175, "name": "Pirates of the Caribbean", "primary_type": "other", "secondary_type": "Fantasy", "description": "Pirates of the Caribbean is a series of five fantasy swashbuckler films produced by <PERSON> and loosely based on <PERSON>'s eponymous theme park ride. Directors of the series include <PERSON>, <PERSON> and <PERSON> and <PERSON><PERSON><PERSON>.", "image_url": "https://live.staticflickr.com/4465/37801865102_a19371138c_b.jpg", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.343Z", "updated_at": "2020-02-01T22:11:00.343Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://live.staticflickr.com/4465/37801865102_a19371138c_b.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://pirates.disney.com", "wikipedia": "https://en.wikipedia.org/wiki/Pirates_of_the_Caribbean_(film_series)", "youtube": ""}}, {"id": 189, "name": "Mindvalley", "primary_type": "other", "secondary_type": "Mindfulness", "description": "Mindvalley is an online pseudoscientific New Age portal, founded by <PERSON><PERSON><PERSON> in 2003. Mindvalley offers both free and paid videos, courses, software, and services on New Age spirituality and self-help topics such as mindfulness, meditation, and \"personal growth\", fitness, and pseudoscientific topics such as spiritual energy, auras and energy medicine.", "image_url": "https://i.ytimg.com/vi/AxEDOqlclgI/maxresdefault.jpg", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.343Z", "updated_at": "2020-02-01T22:11:00.343Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://i.ytimg.com/vi/AxEDOqlclgI/maxresdefault.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.mindvalley.com", "wikipedia": "", "youtube": ""}}, {"id": 191, "name": "L'évolution des idées en physique", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.343Z", "updated_at": "2020-02-01T22:11:00.343Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.amazon.fr/Lévolution-idées-physique-<PERSON>-<PERSON>/dp/2081373106/ref=tmm_pap_swatch_0?_encoding=UTF8&qid=&sr=", "wikipedia": "https://www.amazon.fr/Lévolution-idées-physique-<PERSON>-<PERSON>/dp/2081373106/ref=tmm_pap_swatch_0?_encoding=UTF8&qid=&sr=", "youtube": ""}}, {"id": 193, "name": "Dirty Biology", "primary_type": "media_stream", "secondary_type": "Sciences - Biology", "description": "Sciences videos on mindfuck subject, dirty or just fun. Sometimes we talk about biology too.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDirty%20Biology.jpg?alt=media&token=9ab3b79e-ecca-4a20-8793-2b1eb8435b76", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.343Z", "updated_at": "2020-02-02T22:11:00.000Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDirty%20Biology.jpg?alt=media&token=9ab3b79e-ecca-4a20-8793-2b1eb8435b76", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.youtube.com/user/dirtybiology", "wikipedia": "", "youtube": ""}}, {"id": 194, "name": "<PERSON>", "primary_type": "writings", "secondary_type": "Comedy of manners", "description": "The Misanthrope, or the Cantankerous Lover is a 17th-century comedy of manners in verse written by <PERSON><PERSON><PERSON>. It was first performed on 4 June 1666 at the Théâtre du Palais-Royal, Paris by the King's Players.", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.343Z", "updated_at": "2020-02-01T22:11:00.343Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Misanthrope", "youtube": ""}}, {"id": 208, "name": "Reddit", "primary_type": "other", "secondary_type": "Social news aggregation", "description": "Reddit is an American social news aggregation, web content rating, and discussion website. Registered members submit content to the site such as links, text posts, and images, which are then voted up or down by other members.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FReddit-1689877118072.png?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.343Z", "updated_at": "2020-02-01T22:11:00.343Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FReddit-1689877118072.png?alt=media", "image_name": "Reddit-1689877118072.png", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.reddit.com/", "wikipedia": "https://en.wikipedia.org/wiki/Reddit", "youtube": ""}}, {"id": 212, "name": "La Semaine de 4 heures", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.343Z", "updated_at": "2020-02-01T22:11:00.343Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.amazon.fr/4-Hour-Work-Week-Escape-Anywhere/dp/0091929113/ref=tmm_pap_swatch_0?_encoding=UTF8&qid=1563212900&sr=1-1", "wikipedia": "https://www.amazon.fr/4-Hour-Work-Week-Escape-Anywhere/dp/0091929113/ref=tmm_pap_swatch_0?_encoding=UTF8&qid=1563212900&sr=1-1", "youtube": ""}}, {"id": 215, "name": "<PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "primary_type": "media_stream", "secondary_type": "Feel Good, Sexualité, Société", "description": "Une chaine réservée aux belles personnes, qui traite des plaisirs de cette vie. Le Q, entre autres. ", "image_url": "https://yt3.ggpht.com/FzYNjNduHvG7UyziSSAWGZnA9QegjKB0LLGk0po4--F_Ovv7Tz1K15-8DojCBzOI8iXoAHZx3o8=w2560-fcrop64=1,00005a57ffffa5a8-k-c0xffffffff-no-nd-rj", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.343Z", "updated_at": "2020-02-01T22:11:00.343Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://yt3.ggpht.com/FzYNjNduHvG7UyziSSAWGZnA9QegjKB0LLGk0po4--F_Ovv7Tz1K15-8DojCBzOI8iXoAHZx3o8=w2560-fcrop64=1,00005a57ffffa5a8-k-c0xffffffff-no-nd-rj", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.youtube.com/channel/UCCNYGiTc3u_W1JhxXD72GdQ", "wikipedia": "", "youtube": ""}}, {"id": 125, "name": "Quel effet ça fait d'être une baleine ? 🐳 - DBY #36", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.342Z", "updated_at": "2020-02-01T22:11:00.342Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/vCEUxvz4Egs?t=258", "youtube": ""}}, {"id": 129, "name": "Horizon - La soumission au costume", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.342Z", "updated_at": "2020-02-01T22:11:00.342Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/lzugeIXsLxc?t=284", "youtube": ""}}, {"id": 131, "name": "The Witcher", "primary_type": "tv_series", "secondary_type": "Fantasy", "description": "The Witcher is an American fantasy drama web television series created by <PERSON> for Netflix. It is based on the book series of the same name by Polish writer <PERSON><PERSON><PERSON>.", "image_url": "https://upload.wikimedia.org/wikipedia/en/2/23/The_Witcher_Title_Card.png", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.342Z", "updated_at": "2020-02-01T22:11:00.342Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/2/23/The_Witcher_Title_Card.png", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://en.wikipedia.org/wiki/The_Witcher_(TV_series)", "wikipedia": "https://www.netflix.com/watch/80189685", "youtube": ""}}, {"id": 135, "name": "Cette lutte microbiologique que nous perdons - DBY #56", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.342Z", "updated_at": "2020-02-01T22:11:00.342Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/LIDBmyT3YfA?t=365", "youtube": ""}}, {"id": 136, "name": "The future we're building -- and boring | Elon Musk", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.342Z", "updated_at": "2020-02-01T22:11:00.342Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://www.youtube.com/watch?v=zIwLWfaAg-8", "youtube": ""}}, {"id": 140, "name": "<PERSON> G<PERSON>ule", "primary_type": "media_stream", "secondary_type": "Education", "description": "Data Gueule is a french TV show and a web série. The program offers animated videos dealing with current events in a fun way, and condensed for educational purposes. Each episode attempts to reveal and decipher the mechanisms of society and their little-known aspects.", "image_url": "https://yt3.ggpht.com/a/AGF-l7-luyqwwKEE2thUIUI2dpGn8VkKZnCwMEjQ0w=s288-c-k-c0xffffffff-no-rj-mo", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.342Z", "updated_at": "2020-02-01T22:11:00.342Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://yt3.ggpht.com/a/AGF-l7-luyqwwKEE2thUIUI2dpGn8VkKZnCwMEjQ0w=s288-c-k-c0xffffffff-no-rj-mo", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.youtube.com/user/datagueule", "wikipedia": "https://fr.wikipedia.org/wiki/DataGueule", "youtube": ""}}, {"id": 144, "name": "<PERSON> talks culture with Zappos CEO <PERSON>", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.342Z", "updated_at": "2020-02-01T22:11:00.342Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/uqUx4BJ1ENY?t=504", "youtube": ""}}, {"id": 151, "name": "Value Proposition and Knowing What You’re Worth: Crash Course Business - Entrepreneurship #3", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.342Z", "updated_at": "2020-02-01T22:11:00.342Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/m2IPvT920XM?t=397", "youtube": ""}}, {"id": 152, "name": "<PERSON>", "primary_type": "media_stream", "secondary_type": "Coding - Personal Development", "description": "<PERSON> is a YouTube channel talking about coding, entrepreneurship and personal development.", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.342Z", "updated_at": "2020-02-01T22:11:00.342Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.youtube.com/channel/UC61GK_nOLSJdzAK5hoR2mJA", "wikipedia": "", "youtube": ""}}, {"id": 155, "name": "Daybreak", "primary_type": "tv_series", "secondary_type": "American comedy-drama series", "description": "Daybreak is an American post-apocalyptic comedy-drama web television series created by <PERSON> and <PERSON><PERSON>, based on the eponymous comic series by <PERSON>. The series follows the story of 17-year-old Canadian high school outcast <PERSON> searching for his missing British girlfriend <PERSON> in post-apocalyptic Glendale, California.", "image_url": "https://upload.wikimedia.org/wikipedia/en/4/47/Poster_for_Netflix_series_Daybreak.png", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.342Z", "updated_at": "2020-02-01T22:11:00.342Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/4/47/Poster_for_Netflix_series_Daybreak.png", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.netflix.com/fr-en/title/80197462", "wikipedia": "https://en.wikipedia.org/wiki/Daybreak_(2019_TV_series)", "youtube": ""}}, {"id": 94, "name": "Bref.", "primary_type": "tv_series", "secondary_type": "Comedy", "description": "Bref. is a French television series created by <PERSON><PERSON>, cowritten with <PERSON>, and produced by <PERSON> for My Box Productions. The first episode was released on Canal+ 29 August 2011 and the show ended on 12 July 2012, following an announcement to that effect on 29 June 2012", "release_date": "2011-08-28T22:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBref.-1700222940463.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.341Z", "updated_at": "2020-02-01T22:11:00.341Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBref.-1700222940463.jpg?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Bref", "youtube": "https://youtu.be/5pr4Q09-16k"}}, {"id": 99, "name": "Cubicle Culture | #grindreel", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.341Z", "updated_at": "2020-02-01T22:11:00.341Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/RyBCpabJkuY", "youtube": ""}}, {"id": 102, "name": "Nomade Digital Podcast", "primary_type": "podcast", "secondary_type": "Audio", "description": "French podcast talking about taking back ownership on your time and finance. It's co-hosted by <PERSON> and <PERSON>.", "image_url": "https://marketingmania.fr/wp-content/uploads/2019/04/NomadeDigital.png", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.341Z", "updated_at": "2020-02-01T22:11:00.341Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://marketingmania.fr/wp-content/uploads/2019/04/NomadeDigital.png", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://marketingmania.fr/nomadedigital/", "wikipedia": "", "youtube": ""}}, {"id": 110, "name": "Horizon - L'habit fait le moine", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.341Z", "updated_at": "2020-02-01T22:11:00.341Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/ZvURR83ohcg", "youtube": ""}}, {"id": 112, "name": "The Infinite Game: How to Lead in the 21st Century", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.341Z", "updated_at": "2020-02-01T22:11:00.341Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/3vX2iVIJMFQ?t=2910", "youtube": ""}}, {"id": 114, "name": "Redmi K20 Pro Review: Incredible Value!", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.341Z", "updated_at": "2020-02-01T22:11:00.341Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/WpPw7lUXyI0?t=205", "youtube": ""}}, {"id": 115, "name": "Biased by Design - MIT Technology Review", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.341Z", "updated_at": "2020-02-01T22:11:00.341Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://www.technologyreview.com/s/602154/biased-by-design/", "youtube": ""}}, {"id": 117, "name": "La mafia scientifique dont vous n'avez jamais entendu parler - DBY #53", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.341Z", "updated_at": "2020-02-01T22:11:00.341Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/rcgxY__YXEc?t=641", "youtube": ""}}, {"id": 118, "name": "The Nerdwriter", "primary_type": "other", "secondary_type": "Video essay", "image_url": "https://yt3.ggpht.com/a/AGF-l7_hJSGF0JQvluUmAEs9tCstkd9SX47z4BetoQ=s288-c-k-c0xffffffff-no-rj-mo", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.341Z", "updated_at": "2020-02-01T22:11:00.341Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://yt3.ggpht.com/a/AGF-l7_hJSGF0JQvluUmAEs9tCstkd9SX47z4BetoQ=s288-c-k-c0xffffffff-no-rj-mo", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.youtube.com/user/Nerdwriter1", "wikipedia": "", "youtube": ""}}, {"id": 120, "name": "<PERSON><PERSON><PERSON><PERSON>", "primary_type": "media_stream", "secondary_type": "Space - Cosmos - Science Fiction", "image_url": "https://yt3.ggpht.com/a/AGF-l7-GhYv57eMcNAREO5ACzmsiyLGggSlm5Z2raQ=s288-c-k-c0xffffffff-no-rj-mo", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.341Z", "updated_at": "2020-02-01T22:11:00.341Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://yt3.ggpht.com/a/AGF-l7-GhYv57eMcNAREO5ACzmsiyLGggSlm5Z2raQ=s288-c-k-c0xffffffff-no-rj-mo", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.youtube.com/user/melodysheep", "wikipedia": "", "youtube": ""}}, {"id": 49, "name": "What Can You Learn from Your Competition?: Crash Course Business Entrepreneurship #4", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.340Z", "updated_at": "2020-02-01T22:11:00.340Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/5hL66Xl6W6M", "youtube": ""}}, {"id": 51, "name": "La Terre pourrait-elle être un DONUT ? - DBY #50", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.340Z", "updated_at": "2020-02-01T22:11:00.340Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/eZU9qsugcBY", "youtube": ""}}, {"id": 57, "name": "Coding is a Privilege", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.340Z", "updated_at": "2020-02-01T22:11:00.340Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/PtKOzKNJF-s?t=977", "youtube": ""}}, {"id": 68, "name": "Terre des hommes Terre des hommes", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.340Z", "updated_at": "2020-02-01T22:11:00.340Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://fr.wikipedia.org/wiki/Terre_des_hommes", "youtube": ""}}, {"id": 69, "name": "Nous n'irons pas dans l'espace - LetsPlayScience #3 (Part 3)", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.340Z", "updated_at": "2020-02-01T22:11:00.340Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/noADnHKyRmc?t=379", "youtube": ""}}, {"id": 76, "name": "Feminists: What Were They Thinking?", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.340Z", "updated_at": "2020-02-01T22:11:00.340Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://www.netflix.com/browse?jbv=80216844&jbp=0&jbr=1", "youtube": ""}}, {"id": 78, "name": "The Hitchhiker's Guide to the Galaxy", "primary_type": "other", "description": "The Hitchhiker's Guide to the Galaxy is a comedy science fiction series created by <PERSON>. Originally a radio comedy broadcast on BBC Radio 4 in 1978, it was later adapted to other formats, including stage shows, novels, comic books, a 1981 TV series, a 1984 video game, and 2005 feature film.", "image_url": "https://upload.wikimedia.org/wikipedia/en/b/bd/H2G2_UK_front_cover.jpg", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.340Z", "updated_at": "2020-02-01T22:11:00.340Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/b/bd/H2G2_UK_front_cover.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Hitchhiker's_Guide_to_the_Galaxy", "youtube": ""}}, {"id": 83, "name": "Histoire(s) de femmes", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.340Z", "updated_at": "2020-02-01T22:11:00.340Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.amazon.fr/150-lutte-pour-droits-femmes/dp/2035966299", "wikipedia": "https://www.amazon.fr/150-lutte-pour-droits-femmes/dp/2035966299", "youtube": ""}}, {"id": 87, "name": "<PERSON><PERSON><PERSON><PERSON>", "primary_type": "other", "description": "<PERSON><PERSON><PERSON><PERSON>, or The Impostor, or The Hypocrite, first performed in 1664, is one of the most famous theatrical comedies by <PERSON><PERSON><PERSON>. The characters of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> are considered among the greatest classical theatre roles.", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.340Z", "updated_at": "2020-02-01T22:11:00.340Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Tartuffe", "youtube": ""}}, {"id": 88, "name": "erreur du biomimétisme - DBY #43", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.340Z", "updated_at": "2020-02-01T22:11:00.340Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/1qvJDQTqSHY?t=558", "youtube": ""}}, {"id": 29, "name": "The 4-Hour Workweek", "primary_type": "other", "description": "The 4-Hour Workweek: Escape 9–5, Live Anywhere, and Join the New Rich is a self-help book by <PERSON>, an American writer, educational activist, and entrepreneur.", "image_url": "https://upload.wikimedia.org/wikipedia/en/c/c3/The_4-Hour_Workweek_%28front_cover%29.jpg?1580597852118", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.339Z", "updated_at": "2020-02-01T22:11:00.339Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/c/c3/The_4-Hour_Workweek_%28front_cover%29.jpg?1580597852118", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.amazon.fr/dp/0091929113/ref=cm_sw_em_r_mt_dp_U_IUllDbNADBNXT", "wikipedia": "https://www.amazon.fr/dp/0091929113/ref=cm_sw_em_r_mt_dp_U_IUllDbNADBNXT", "youtube": ""}}, {"id": 35, "name": "Amphitryon", "primary_type": "writings", "secondary_type": "Language comedy", "description": "Amphitryon is a French language comedy in a prologue and 3 Acts by <PERSON><PERSON><PERSON> which is based on the story of the Greek mythological character <PERSON><PERSON><PERSON><PERSON> as told by <PERSON><PERSON><PERSON> in his play from ca. 190-185 B.C. The play was first performed at the Théâtre du Palais-Royal in Paris on 13 January 1668.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/a/ac/Amphitryon.jpg", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.339Z", "updated_at": "2020-02-01T22:11:00.339Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/commons/a/ac/Amphitryon.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Amphitryon_(Moli%C3%A8re_play)", "youtube": ""}}, {"id": 39, "name": "Paum_é_e_s", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.339Z", "updated_at": "2020-02-01T22:11:00.339Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://soundcloud.com/paumees", "youtube": ""}}, {"id": 40, "name": "<PERSON> Me Her", "primary_type": "tv_series", "secondary_type": "Comedie, Romance, Drama", "description": "You Me Her is an American–Canadian comedy-drama television series that revolves around a suburban married coup\nle who are entering a three-way romantic relationship, otherwise known as a polyamorous relationship. The series is set in Portland, Oregon and was created by <PERSON>.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FYou%20Me%20Her.jpg?alt=media&token=cc786ede-9afa-407a-92e5-9dd713b429bc", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.339Z", "updated_at": "2020-02-01T22:11:00.339Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FYou%20Me%20Her.jpg?alt=media&token=cc786ede-9afa-407a-92e5-9dd713b429bc", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/ru/title/80103417", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/You_Me_Her", "youtube": ""}}, {"id": 41, "name": "Usbek&Rica", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.339Z", "updated_at": "2020-02-01T22:11:00.339Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://usbeketrica.com", "youtube": ""}}, {"id": 42, "name": "The Misanthrope", "primary_type": "writings", "secondary_type": "Comedy of manners", "description": "The Misanthrope, or the Cantankerous Lover is a 17th-century comedy of manners in verse written by <PERSON><PERSON><PERSON>. It was first performed on 4 June 1666 at the Théâtre du Palais-Royal, Paris by the King's Players.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/0/08/LeMisanthrope.jpg", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.339Z", "updated_at": "2020-02-01T22:11:00.339Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/commons/0/08/LeMisanthrope.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Misanthrope", "youtube": ""}}, {"id": 44, "name": "Pourquoi l'humanité a-t-elle failli disparaître ? - DBY #33", "primary_type": "other", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.339Z", "updated_at": "2020-02-01T22:11:00.339Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/wZyx5EEfUSE?t=185", "youtube": ""}}, {"id": 46, "name": "Marketing Mania", "primary_type": "other", "secondary_type": "Marketing", "description": "Marketing Mania is a multi-production content about marketing and psychology. The content is mainly available through the official website, YouTube, a podcast and a book.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMarketing%20Mania.jpg?alt=media&token=822c5c4f-3af8-4c4a-be81-1ffe82b4a884", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.339Z", "updated_at": "2020-02-02T22:11:00.000Z", "urls": {"affiliate": "https://www.youtube.com/channel/UCSmUdD2Dd_v5uqBuRwtEZug", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMarketing%20Mania.jpg?alt=media&token=822c5c4f-3af8-4c4a-be81-1ffe82b4a884", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://marketingmania.fr", "wikipedia": "", "youtube": "https://www.youtube.com/channel/UCSmUdD2Dd_v5uqBuRwtEZug"}}, {"id": 8, "name": "Internet est mort. (et vous aussi)", "primary_type": "media_stream", "secondary_type": "Technology", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.338Z", "updated_at": "2020-02-01T22:11:00.338Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "primevideo": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": "https://youtu.be/vOB-56n4XCc"}}, {"id": 12, "name": "Mindhunter", "primary_type": "tv_series", "secondary_type": "Drama", "description": "Mindhunter is an American crime thriller television series created by <PERSON>, based on the true-crime book <PERSON><PERSON><PERSON>: Inside the FBI's Elite Serial Crime Unit written by <PERSON> and <PERSON>. Mindhunter revolves around FBI agents <PERSON> (<PERSON>) and <PERSON> (<PERSON>), along with psychologist <PERSON> (<PERSON>), who operate the FBI's Behavioral Science Unit within the Training Division at the FBI Academy in Quantico, Virginia. They interview imprisoned serial killers to understand how they think, with the hope of applying this knowledge to solve ongoing cases.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/thumb/3/30/Mindhunter_Logo.svg/1280px-Mindhunter_Logo.svg.png", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.338Z", "updated_at": "2020-02-01T22:11:00.338Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/commons/thumb/3/30/Mindhunter_Logo.svg/1280px-Mindhunter_Logo.svg.png", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Mindhunter_(TV_series)", "youtube": ""}}, {"id": 13, "name": "Nowtech", "primary_type": "media_stream", "secondary_type": "Technology", "description": "Nowtech is a french content production about technology and applications. They produce a daily live on YouTube.", "image_url": "https://yt3.ggpht.com/a/AGF-l7-9Hl942WiXIE222b_roDy9sqATUNDrtByE-Q=s288-c-k-c0xffffffff-no-rj-mo", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.338Z", "updated_at": "2020-02-01T22:11:00.338Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://yt3.ggpht.com/a/AGF-l7-9Hl942WiXIE222b_roDy9sqATUNDrtByE-Q=s288-c-k-c0xffffffff-no-rj-mo", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.youtube.com/user/NowTechTVfr", "wikipedia": "https://nowtech.tv/", "youtube": ""}}, {"id": 14, "name": "À quoi ça sert d'être triste ? - YouAsk #2", "primary_type": "media_stream", "secondary_type": "Sciences", "description": "A french video about the utility of sadness. The subject is studied and presented by <PERSON> from the channel Dirty Biology.", "release_date": "2018-02-13T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FÀ quoi ça sert d'être triste ? - YouAsk #2-1616029238900.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.338Z", "updated_at": "2020-02-01T22:11:00.338Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FÀ quoi ça sert d'être triste ? - YouAsk #2-1616029238900.jpg?alt=media", "image_name": "À quoi ça sert d'être triste ? - YouAsk #2-1616029238900.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/RFI1CzUoeLk?t=370https://youtu.be/RFI1CzUoeLk?t=370", "youtube": "https://www.youtube.com/watch?v=RFI1CzUoeLk"}}, {"id": 26, "name": "Twilight of the Idols", "primary_type": "book", "secondary_type": "Philosophy", "description": "Twilight of the Idols, or, How to Philosophize with a <PERSON> is a book by <PERSON>, written in 1888, and published in 1889.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/a/a6/Gotzen-dammerung.gif?1580587376781", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.338Z", "updated_at": "2020-02-01T22:11:00.338Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/commons/a/a6/Gotzen-dammerung.gif?1580587376781", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Twilight_of_the_Idols", "youtube": ""}}, {"id": 28, "name": "Le Bourgeois gentilhomme", "primary_type": "writings", "secondary_type": "Comédie Ballet", "description": "Le Bourgeois gentilhomme is a five-act comédie-ballet — a play intermingled with music, dance and singing — written by <PERSON><PERSON><PERSON>, first presented on 14 October 1670 before the court of <PERSON> XIV at the Château of Chambord by <PERSON><PERSON><PERSON>'s troupe of actors.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/a/ac/Amphitryon.jpg", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.338Z", "updated_at": "2020-02-01T22:11:00.338Z", "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/commons/a/ac/Amphitryon.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Le_Bourgeois_gentilhomme", "youtube": ""}}, {"id": 5, "name": "<PERSON><PERSON>'s tweet (2013-12-10)", "primary_type": "writings", "secondary_type": "Poetry", "description": "A poetic tweet from the artist <PERSON><PERSON>.", "release_date": "2013-12-09T23:00:00.000Z", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F-<PERSON><PERSON><PERSON>'s tweet (2013-12-10)-1616029594241.jpg?alt=media", "views_count": 0, "likes_count": 0, "created_at": "2020-02-01T22:11:00.334Z", "updated_at": "2020-02-01T22:11:00.334Z", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F-<PERSON><PERSON><PERSON>'s tweet (2013-12-10)-1616029594241.jpg?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "https://twitter.com/Bouletcorp/status/410390478526242816?s=20", "website": "https://twitter.com/Bouletcorp/status/410390478526242816?s=20", "wikipedia": "", "youtube": ""}}, {"id": 9, "name": "<PERSON>", "primary_type": "tv_series", "secondary_type": "horror", "description": "Marianne is a French horror web television series created and directed by <PERSON>, written by <PERSON><PERSON> and <PERSON><PERSON><PERSON> and starring <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>. The plot revolves around the young novelist <PERSON> who realizes that the characters she writes in her horror novels are also in the real world. The series was released on 13 September 2019 on Netflix. The series was canceled after one season in January 2020.", "image_url": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fmarianne.jpg?alt=media&token=575ad499-613e-4d31-8ada-abf64750c966", "views_count": 0, "likes_count": 0, "created_at": "2020-01-31T23:00:00.000Z", "updated_at": "2020-11-01T23:00:00.000Z", "urls": {"affiliate": "https://www.netflix.com/watch/80217779", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fmarianne.jpg?alt=media&token=575ad499-613e-4d31-8ada-abf64750c966", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_(TV_series)", "youtube": ""}}]