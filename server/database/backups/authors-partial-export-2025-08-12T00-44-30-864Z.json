[{"id": 672, "name": "Barman", "is_fictional": true, "job": "<PERSON>man (Disco Elysium)", "description": "The barman in Disco Elysium game.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:57:44", "updated_at": "2025-08-11 15:01:49", "socials": []}, {"id": 671, "name": "<PERSON>", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:56:50", "updated_at": "2025-08-11 14:56:50", "socials": []}, {"id": 670, "name": "<PERSON>", "is_fictional": false, "job": "Physicist", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:52:13", "updated_at": "2025-08-11 15:53:52", "socials": []}, {"id": 669, "name": "Woman in the store", "is_fictional": true, "job": "<PERSON><PERSON> (The Old Guard)", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:41:16", "updated_at": "2025-08-11 14:44:45", "socials": []}, {"id": 668, "name": "<PERSON>", "is_fictional": true, "job": "The Old Guard", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:40:34", "updated_at": "2025-08-11 14:42:26", "socials": []}, {"id": 667, "name": "<PERSON>", "is_fictional": true, "job": "The Old Guard", "description": "Fictional character from The Old Guard film.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:37:49", "updated_at": "2025-08-11 14:40:09", "socials": []}, {"id": 666, "name": "<PERSON>", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:35:49", "updated_at": "2025-08-11 14:35:49", "socials": []}, {"id": 665, "name": "<PERSON><PERSON>", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:34:52", "updated_at": "2025-08-11 14:34:52", "socials": []}, {"id": 664, "name": "<PERSON> (Final Space)", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:32:56", "updated_at": "2025-08-11 14:32:56", "socials": []}, {"id": 663, "name": "<PERSON><PERSON>", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:09:12", "updated_at": "2025-08-11 14:09:12", "socials": []}, {"id": 662, "name": "<PERSON>", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:07:26", "updated_at": "2025-08-11 14:07:26", "socials": []}, {"id": 661, "name": "<PERSON>", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:06:40", "updated_at": "2025-08-11 14:06:40", "socials": []}, {"id": 660, "name": "The Doctor", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:06:08", "updated_at": "2025-08-11 14:06:08", "socials": []}, {"id": 659, "name": "<PERSON>", "is_fictional": false, "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-11 14:05:36", "updated_at": "2025-08-11 14:05:36", "socials": []}, {"id": 636, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-07-13", "death_date": "2024-07-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 637, "name": "Edison", "is_fictional": false, "birth_date": "2023-10-14", "death_date": "2023-10-14", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 638, "name": "Proverbe portugais", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 639, "name": "Miss Piano", "is_fictional": false, "birth_date": "2025-01-06", "death_date": "2025-01-06", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 640, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "1966-09-25", "death_date": "2023-10-03", "job": "German physics researcher", "description": "<PERSON><PERSON> is a German Dutch professor of radio astronomy and astroparticle physics at the Radboud University Nijmegen. He was a winner of the 2011 Spinoza Prize. His main field of study is black holes, and he is the originator of the concept of the 'black hole shadow'.", "image_url": "https://upload.wikimedia.org/wikipedia/commons/thumb/a/a4/HeinoFalcke2011.jpg/897px-HeinoFalcke2011.jpg", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 641, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-27", "death_date": "2024-06-27", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 642, "name": "<PERSON><PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-01-05", "death_date": "2024-01-05", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 643, "name": "Lucy?", "is_fictional": false, "birth_date": "2024-02-02", "death_date": "2024-02-02", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 644, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 645, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 646, "name": "Christian Saint-Étienne", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 647, "name": "<PERSON>", "is_fictional": false, "birth_date": "2023-09-26", "death_date": "2023-09-26", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 648, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-01-05", "death_date": "2024-01-05", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 649, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-04-21", "death_date": "2024-04-21", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 650, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 651, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-03-12", "death_date": "2024-03-12", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 652, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 653, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 654, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 655, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "job": "Politic", "description": "Old mayor of Montpellier, France.", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 656, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 657, "name": "Malcom Forbes, PDG de la revue financière Forbes", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 658, "name": "<PERSON>, acteur éc<PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:19", "updated_at": "2025-08-08 13:02:19", "socials": []}, {"id": 559, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 560, "name": "Réformateur", "is_fictional": true, "birth_date": "2023-11-11", "death_date": "2023-11-11", "job": "Reformer in the Volt", "description": "A reformer in the Volt which warned <PERSON> and his colleagues about their actions. He's supposedly a person who works on rules concerning the Volt.\n\nHe's a fictional character in the book \"La Zone du Dehors\".", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 561, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 562, "name": "Narra<PERSON> (Fight Club)", "is_fictional": false, "birth_date": "2024-04-21", "death_date": "2024-04-21", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 563, "name": "House Owner", "is_fictional": false, "birth_date": "2023-12-11", "death_date": "2023-12-11", "job": "Market dealer", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 564, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-08-14", "death_date": "2024-08-14", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 565, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-01-29", "death_date": "2024-01-29", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 566, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 567, "name": "<PERSON>, cofonda<PERSON>ur de LinkedIn", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 568, "name": "<PERSON>", "is_fictional": false, "birth_date": "2024-09-14", "death_date": "2024-09-14", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 569, "name": "<PERSON><PERSON>", "is_fictional": false, "birth_date": "2024-01-05", "death_date": "2024-01-05", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 570, "name": "Proverbe espagnol", "is_fictional": false, "birth_date": "2024-06-13", "death_date": "2024-06-13", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}, {"id": 571, "name": "Sherpa 2", "is_fictional": false, "birth_date": "2024-01-04", "death_date": "2024-01-04", "description": "A random character in the book «Kilomètre zéro».", "views_count": 0, "likes_count": 0, "shares_count": 0, "created_at": "2025-08-08 13:02:18", "updated_at": "2025-08-08 13:02:18", "socials": []}]